#!/bin/bash

# 简化版部署脚本
# 使用 rsync 进行文件同步，更高效

# 配置
REMOTE_HOST="**************"
REMOTE_USER="root"
REMOTE_PASSWORD="Pix@121cc.#"
REMOTE_PATH="/data/lowcode"
BUILD_DIR="dist"

# 颜色输出
GREEN='\033[0;32m'
BLUE='\033[0;34m'
RED='\033[0;31m'
NC='\033[0m'

echo -e "${BLUE}开始部署前端项目...${NC}"

# 1. 检查环境
if ! command -v npm &> /dev/null; then
    echo -e "${RED}错误: npm 未安装${NC}"
    exit 1
fi

if ! command -v sshpass &> /dev/null; then
    echo -e "${RED}错误: sshpass 未安装${NC}"
    echo "安装命令:"
    echo "  Ubuntu/Debian: sudo apt-get install sshpass"
    echo "  macOS: brew install sshpass"
    exit 1
fi

# 2. 打包项目
echo -e "${BLUE}正在打包项目...${NC}"
npm run build

if [ ! -d "$BUILD_DIR" ]; then
    echo -e "${RED}错误: 打包失败，未找到 $BUILD_DIR 目录${NC}"
    exit 1
fi

echo -e "${GREEN}✓ 项目打包完成${NC}"

# 3. 删除远程目录
echo -e "${BLUE}正在清理远程目录...${NC}"
sshpass -p "$REMOTE_PASSWORD" ssh -o StrictHostKeyChecking=no "$REMOTE_USER@$REMOTE_HOST" "
    if [ -d '$REMOTE_PATH' ]; then
        rm -rf '$REMOTE_PATH'
        echo '远程目录已删除'
    fi
    mkdir -p '$REMOTE_PATH'
    echo '远程目录已创建'
"

echo -e "${GREEN}✓ 远程目录准备完成${NC}"

# 4. 上传文件
echo -e "${BLUE}正在上传文件...${NC}"
sshpass -p "$REMOTE_PASSWORD" rsync -avz --delete -e "ssh -o StrictHostKeyChecking=no" "$BUILD_DIR/" "$REMOTE_USER@$REMOTE_HOST:$REMOTE_PATH/"

if [ $? -eq 0 ]; then
    echo -e "${GREEN}✓ 文件上传完成${NC}"
else
    echo -e "${RED}错误: 文件上传失败${NC}"
    exit 1
fi

# 5. 验证部署
echo -e "${BLUE}验证部署结果...${NC}"
sshpass -p "$REMOTE_PASSWORD" ssh -o StrictHostKeyChecking=no "$REMOTE_USER@$REMOTE_HOST" "
    echo '部署后的目录结构:'
    ls -la '$REMOTE_PATH' | head -10
    echo ''
    echo '文件总数:'
    find '$REMOTE_PATH' -type f | wc -l
"

echo -e "${GREEN}🎉 部署完成！${NC}"
echo "================================"
echo "服务器: $REMOTE_HOST"
echo "路径: $REMOTE_PATH"
echo "================================"
