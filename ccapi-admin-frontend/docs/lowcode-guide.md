# CCAPI 低代码配置功能使用指南

## 概述

CCAPI 低代码配置功能是一个基于 Vue 3 + TypeScript + DaisyUI 的可视化配置系统，允许用户通过拖拽和配置的方式快速创建数据查询和展示页面，无需编写代码即可实现复杂的业务功能。

## 功能特性

### 🎯 核心功能
- **可视化配置界面**：拖拽式字段配置，实时预览
- **动态搜索表单**：支持多种字段类型和验证规则
- **灵活表格展示**：可配置列、排序、筛选、分页
- **多格式导出**：支持 Excel、CSV、PDF 导出


### 🛠 技术特性
- **类型安全**：完整的 TypeScript 类型定义
- **响应式设计**：适配不同屏幕尺寸
- **高性能**：虚拟滚动、懒加载优化
- **可扩展**：插件化架构支持自定义组件
- **安全可靠**：JSON Schema 验证，XSS 防护

## 快速开始

### 1. 访问配置中心

在 CCAPI 管理系统中，点击导航栏的"低代码配置"进入配置中心。

### 2. 创建新配置

1. 点击"新建配置"按钮
2. 输入配置名称和描述
3. 从零开始配置

### 3. 配置数据源

在"基础配置"选项卡中：
- **API地址**：设置数据接口 URL
- **请求方法**：选择 GET 或 POST
- **响应映射**：配置数据路径（如 `data.list`）
- **请求参数**：设置默认参数和请求头

### 4. 配置搜索表单

在"搜索配置"选项卡中：
- 点击"添加字段"创建搜索条件
- 配置字段类型：文本、下拉、日期等
- 设置验证规则和默认值
- 拖拽调整字段顺序

### 5. 配置数据表格

在"表格配置"选项卡中：
- 添加表格列并设置属性
- 配置列宽、对齐方式、排序
- 设置数据格式化规则
- 启用功能：选择、导出、刷新等

### 6. 配置导出功能

在"导出配置"选项卡中：
- 启用导出功能
- 选择支持的格式
- 配置文件名模板
- 选择导出列

### 7. 保存并预览

- 点击"保存"按钮保存配置
- 在右侧预览区域查看效果
- 点击"预览"按钮全屏预览

## 配置详解

### 搜索字段类型

| 类型 | 说明 | 配置项 |
|------|------|--------|
| `input` | 文本输入框 | 占位符、验证规则 |
| `select` | 下拉选择框 | 选项列表、多选 |
| `date` | 日期选择器 | 日期格式 |
| `daterange` | 日期范围 | 开始/结束字段 |
| `number` | 数字输入 | 最小/最大值 |
| `textarea` | 多行文本 | 行数设置 |

### 表格列类型

| 类型 | 说明 | 格式化选项 |
|------|------|------------|
| `text` | 文本显示 | 无 |
| `number` | 数字显示 | 精度、千分位 |
| `date` | 日期显示 | 日期格式 |
| `datetime` | 日期时间 | 日期时间格式 |
| `enum` | 枚举值 | 枚举映射 |
| `boolean` | 布尔值 | 是/否显示 |

### 数据格式化

支持多种数据格式化方式：

```typescript
// 日期格式化
{
  type: 'date',
  format: 'YYYY-MM-DD HH:mm:ss'
}

// 数字格式化
{
  type: 'number',
  precision: 2
}

// 货币格式化
{
  type: 'currency',
  precision: 2,
  prefix: '¥'
}

// 自定义格式化
{
  type: 'custom',
  customFunction: 'return value + " 元"'
}
```

## 高级功能

### 自定义样式

在"高级配置"中可以添加自定义 CSS：

```css
/* 自定义表格样式 */
.my-table {
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

/* 自定义搜索表单样式 */
.my-search-form {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20px;
  border-radius: 12px;
}
```

### 事件钩子

支持在关键节点执行自定义逻辑：

```javascript
// 搜索前钩子
function beforeSearch(searchParams) {
  // 修改搜索参数
  searchParams.timestamp = Date.now();
  return searchParams;
}

// 搜索后钩子
function afterSearch(data, searchParams) {
  // 处理返回数据
  console.log('搜索完成:', data.length, '条记录');
  return data;
}

// 导出前钩子
function beforeExport(data, exportConfig) {
  // 处理导出数据
  return data.map(item => ({
    ...item,
    exportTime: new Date().toISOString()
  }));
}
```

### 自定义验证

支持自定义字段验证规则：

```typescript
{
  type: 'custom',
  message: '请输入有效的手机号',
  validator: (value) => /^1[3-9]\d{9}$/.test(value)
}
```

## 最佳实践

### 1. 命名规范
- 配置名称：使用有意义的名称，如"订单管理-主列表"
- 字段标识：使用驼峰命名，如 `orderNo`、`createTime`
- 列标题：简洁明了，如"订单号"、"创建时间"

### 2. 性能优化
- 合理设置分页大小（建议 20-50 条）
- 避免过多的搜索字段（建议不超过 8 个）
- 表格列数控制在合理范围（建议不超过 10 列）

### 3. 用户体验
- 设置合适的默认值
- 提供清晰的占位符文本
- 合理的字段排序和分组

### 4. 数据安全
- 验证用户输入
- 避免在自定义脚本中执行危险操作
- 定期备份配置数据

## 故障排除

### 常见问题

**Q: 配置保存失败**
A: 检查必填字段是否完整，确保 API 地址格式正确

**Q: 数据加载失败**
A: 检查数据源配置，确认 API 接口可访问，响应格式正确

**Q: 搜索功能异常**
A: 检查搜索字段的 key 值是否与 API 参数匹配

**Q: 导出功能不工作**
A: 确认导出配置已启用，检查导出列设置

**Q: 自定义样式不生效**
A: 检查 CSS 语法，确保选择器正确

### 调试技巧

1. **使用浏览器开发者工具**
   - 查看网络请求
   - 检查控制台错误
   - 调试 JavaScript 代码

2. **配置验证**
   - 使用"测试连接"功能验证数据源
   - 在预览模式下测试功能

3. **逐步排查**
   - 从简单配置开始
   - 逐步添加复杂功能
   - 定位问题所在

## 技术架构

### 核心组件

```
LowCodeConfigView.vue          # 配置管理主页面
├── BasicConfigPanel.vue      # 基础配置面板
├── SearchConfigPanel.vue     # 搜索配置面板
├── TableConfigPanel.vue      # 表格配置面板
├── ExportConfigPanel.vue     # 导出配置面板
└── AdvancedConfigPanel.vue   # 高级配置面板

LowCodeRuntimeView.vue         # 运行时页面
└── LowCodeRenderer.vue       # 运行时渲染器
```

### 数据流

```
配置数据 → JSON Schema验证 → 存储
配置数据 → 运行时引擎 → 动态渲染 → 用户界面
```

### 扩展接口

系统提供了完整的扩展接口，支持：
- 自定义字段类型
- 自定义格式化器
- 自定义验证器
- 自定义导出格式

## 更新日志

### v1.0.0 (2024-07-13)
- ✨ 初始版本发布
- 🎯 支持基础的搜索、表格、导出配置
- 🛠 完整的 TypeScript 类型支持
- 📱 响应式设计


---

如有问题或建议，请联系开发团队。
