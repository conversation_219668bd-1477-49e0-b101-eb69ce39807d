# 低代码配置功能优化

## 概述

本次更新优化了低代码配置系统，简化了不必要的配置项，提升了用户体验。主要包括分页配置功能增强和搜索配置简化。

## 主要改进

### 1. 分页配置简化

移除了不必要的分页显示配置项，分页组件现在默认包含：
- ✅ **页码选择器**：自动显示，支持页大小切换
- ✅ **快速跳转**：自动显示，支持直接跳转到指定页
- ✅ **总数统计**：自动显示，显示当前页范围和总记录数

### 2. 搜索配置简化

移除了不必要的搜索表单配置项：
- ✅ **标签位置**：固定在顶部显示，无需用户选择
- ✅ **标签显示**：自动显示字段标签，提供更好的用户体验

### 3. 新增分页参数配置

在表格配置的"分页配置"部分，提供了以下核心配置项：

- **默认页大小**：设置初始加载时每页显示的记录数
- **页码参数名**：配置发送给后端API的页码参数名（如 `page`）
- **页大小参数名**：配置发送给后端API的页大小参数名（如 `limit` 或 `pageSize`）
- **页码选项**：配置可选的页大小选项（如 10,20,50,100）

### 4. 增强的分页组件

分页组件现在包含完整的功能：
- **智能页码显示**：自动处理大量页码的省略显示
- **页大小选择**：下拉选择每页显示条数
- **快速跳转**：输入框直接跳转到指定页
- **总数统计**：显示当前页范围和总记录数
- **响应式布局**：适配不同屏幕尺寸

### 5. 向后兼容性

- 现有配置会自动使用默认值：
  - 页码参数名：`page`
  - 页大小参数名：`limit`
  - 默认页大小：`20`
  - 页码选项：`[10, 20, 50, 100]`

## 使用方法

### 配置分页参数

1. 在低代码配置中心，选择或创建一个配置
2. 切换到"表格配置"选项卡
3. 在"分页配置"区域设置：
   - **默认页大小**：根据数据量和显示需求设置合适的值（建议10-100）
   - **页码参数名**：根据后端API要求设置（常见：`page`、`pageNum`）
   - **页大小参数名**：根据后端API要求设置（常见：`limit`、`pageSize`、`size`）
   - **页码选项**：设置用户可选择的页大小选项，用逗号分隔（如：10,20,50,100）

### 分页组件功能

配置完成后，运行时的分页组件将自动提供：

1. **总数统计**：显示"显示第 X - Y 条，共 Z 条"
2. **页大小选择器**：下拉菜单选择每页显示条数
3. **页码导航**：
   - 上一页/下一页按钮
   - 智能页码显示（超过7页时自动省略）
   - 点击页码直接跳转
4. **快速跳转**：输入框 + 确定按钮，直接跳转到指定页

### 常见后端API参数配置

#### 标准RESTful API
```
默认页大小: 20
页码参数名: page
页大小参数名: limit
页码选项: 10,20,50,100
```

#### Spring Boot分页
```
默认页大小: 15
页码参数名: page
页大小参数名: size
页码选项: 10,15,30,50
```

#### 自定义API
```
默认页大小: 25
页码参数名: pageNum
页大小参数名: pageSize
页码选项: 10,25,50,100
```

## 技术实现

### 类型定义更新

简化了 `TableConfig` 接口的 `pagination` 配置：
```typescript
pagination: {
  pageSize: number           // 默认页大小
  pageSizeOptions: number[]  // 页大小选项
  pageParamName: string      // 页码参数名
  pageSizeParamName: string  // 页大小参数名
  // 移除了 showSizeChanger, showQuickJumper, showTotal 等配置项
}
```

### 运行时参数构建

系统会根据配置动态构建请求参数：
```typescript
const paginationConfig = currentConfig.value.tableConfig.pagination
const requestParams = {
  // 其他参数...
  [paginationConfig.pageParamName || 'page']: currentPage,
  [paginationConfig.pageSizeParamName || 'limit']: pageSize
}
```

### 分页组件增强

新的分页组件包含完整功能：
- 智能页码显示算法（处理大量页码的省略）
- 页大小选择器（基于配置的选项）
- 快速跳转功能（输入验证和错误处理）
- 响应式布局（适配移动端）

## 注意事项

1. **参数名称**：确保配置的参数名与后端API接口文档一致
2. **默认页大小**：建议设置为10-100之间的合理值
3. **页码选项**：建议提供3-5个合理的选项，如 10,20,50,100
4. **兼容性**：现有配置无需修改，会自动使用默认值
5. **用户体验**：分页组件现在提供更丰富的交互功能

## 示例配置

### 示例1：标准分页API
```json
{
  "pagination": {
    "pageSize": 20,
    "pageParamName": "page",
    "pageSizeParamName": "limit",
    "pageSizeOptions": [10, 20, 50, 100]
  }
}
```

### 示例2：Spring Boot分页
```json
{
  "pagination": {
    "pageSize": 15,
    "pageParamName": "page",
    "pageSizeParamName": "size",
    "pageSizeOptions": [10, 15, 30, 50]
  }
}
```

## 总结

这次更新不仅简化了配置项，还大大增强了分页组件的功能性和用户体验。用户现在可以享受到更完整、更直观的分页操作，同时配置过程也更加简洁明了。
