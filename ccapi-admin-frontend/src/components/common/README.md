# 通用模态框组件库

一个基于 Vue 3 + TypeScript + DaisyUI 的可复用模态框组件库，为 CCAPI 管理系统提供一致的用户交互体验。

## 🚀 快速开始

```vue
<template>
  <div>
    <button @click="showDeleteConfirm">删除</button>
    
    <BaseModal
      v-model:show="modal.show.value"
      :config="modal.config.value"
      @confirm="modal.handleConfirm"
      @cancel="modal.handleCancel"
      @button-click="modal.handleButtonClick"
    />
  </div>
</template>

<script setup lang="ts">
import { useModal, BaseModal } from '@/components/common'

const modal = useModal()

const showDeleteConfirm = () => {
  modal.showDeleteConfirm('重要数据', async () => {
    await deleteData()
  })
}
</script>
```

## 📦 组件结构

```
src/components/common/
├── BaseModal.vue           # 基础模态框组件
├── index.ts               # 导出文件
└── README.md              # 说明文档

src/types/
└── modal.ts               # 类型定义

src/utils/
└── modal.ts               # 工具函数和预设配置

src/composables/
└── useModal.ts            # Composable API
```

## 🎨 模态框类型

| 类型 | 颜色 | 图标 | 用途 |
|------|------|------|------|
| `warning` | 黄色 | ⚠️ | 警告确认 |
| `danger` | 红色 | 🗑️ | 删除操作 |
| `info` | 蓝色 | ℹ️ | 信息提示 |
| `success` | 绿色 | ✅ | 成功反馈 |

## 🛠️ 主要功能

- ✅ **类型安全**: 完整的 TypeScript 支持
- ✅ **预设配置**: 常用模态框开箱即用
- ✅ **自定义配置**: 灵活的配置选项
- ✅ **异步支持**: 自动处理加载状态
- ✅ **可访问性**: 支持键盘导航和屏幕阅读器
- ✅ **响应式**: 适配不同屏幕尺寸
- ✅ **一致性**: 遵循 DaisyUI 设计系统

## 📖 使用方法

### 预设模态框

```typescript
// 删除确认
modal.showDeleteConfirm('配置名称', async () => {
  await deleteConfig()
})

// 保存确认
modal.showSaveConfirm(async () => {
  await saveData()
})

// 提示信息
modal.showError('操作失败')
modal.showSuccess('操作成功')
modal.showInfo('提示信息')
```

### 快速方法

```typescript
// 确认对话框
modal.confirm('确认操作', '您确定要继续吗？', {
  onConfirm: async () => {
    await performAction()
  }
})

// 提示对话框
modal.alert('warning', '注意', '这是重要提示')
```

### 自定义配置

```typescript
import { createModalConfig, createButton } from '@/components/common'

const config = createModalConfig('info', '自定义标题', {
  subtitle: '副标题',
  description: '描述文本',
  size: 'lg',
  buttons: [
    createButton('取消', 'ghost'),
    createButton('确认', 'primary', async () => {
      await customAction()
    })
  ]
})

modal.showModal(config)
```

## 🔧 配置选项

### ModalConfig

```typescript
interface ModalConfig {
  type: 'warning' | 'danger' | 'info' | 'success'
  title: string
  subtitle?: string
  description?: string
  customIcon?: string
  buttons?: ModalButton[]
  closable?: boolean
  persistent?: boolean
  size?: 'sm' | 'md' | 'lg' | 'xl'
  maxWidth?: string
}
```

### ModalButton

```typescript
interface ModalButton {
  text: string
  variant?: 'primary' | 'secondary' | 'error' | 'warning' | 'info' | 'success' | 'ghost'
  loading?: boolean
  disabled?: boolean
  onClick?: () => void | Promise<void>
}
```

## 🎯 最佳实践

1. **使用预设配置**保持一致性
2. **提供清晰的标题和描述**
3. **为异步操作添加加载状态**
4. **合理使用模态框类型**
5. **避免嵌套模态框**

## 📚 更多文档

- [完整使用指南](../../docs/modal-component-guide.md)
- [示例页面](../views/ModalExamplesView.vue)

## 🤝 贡献

欢迎提交 Issue 和 Pull Request 来改进这个组件库。
