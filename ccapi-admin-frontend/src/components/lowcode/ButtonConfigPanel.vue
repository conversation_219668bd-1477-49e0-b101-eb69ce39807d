<template>
  <div class="space-y-4">
    <h5 class="font-medium">按钮配置</h5>

    <!-- 按钮位置和对齐 -->
    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
      <div class="form-control">
        <label class="label">
          <span class="label-text">按钮位置</span>
        </label>
        <select v-model="localButtonConfig.position" class="select select-bordered select-sm">
          <option value="inline">与字段同行</option>
          <option value="newline">独立新行</option>
        </select>
      </div>

      <div class="form-control">
        <label class="label">
          <span class="label-text">按钮对齐</span>
        </label>
        <select v-model="localButtonConfig.alignment" class="select select-bordered select-sm">
          <option value="start">左对齐</option>
          <option value="center">居中</option>
          <option value="end">右对齐</option>
        </select>
      </div>
    </div>

    <!-- 按钮列表 -->
    <div class="space-y-3">
      <div v-for="button in localButtonConfig.buttons" :key="button.type" class="card bg-base-200 p-3">
        <div class="flex items-center gap-3">
          <!-- 启用/禁用 -->
          <input
            v-model="button.enabled"
            type="checkbox"
            class="checkbox checkbox-primary checkbox-sm"
          />

          <!-- 按钮信息 -->
          <div class="flex items-center gap-2 min-w-0 flex-1">
            <span class="font-medium">{{ getButtonLabel(button.type) }}</span>
            <span class="text-sm text-base-content/60">({{ button.type }})</span>
          </div>

          <!-- 宽度设置 -->
          <div class="form-control">
            <select v-model="button.width" class="select select-bordered select-sm w-32">
              <option value="">自动</option>
              <option value="80px">80px</option>
              <option value="100px">100px</option>
              <option value="120px">120px</option>
              <option value="150px">150px</option>
              <option value="200px">200px</option>
            </select>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue'
import type { ButtonConfig, ButtonType } from '@/types/lowcode'

// Props
const props = defineProps<{
  modelValue: {
    position?: 'inline' | 'newline'
    alignment?: 'start' | 'center' | 'end'
    buttons: ButtonConfig[]
  }
}>()

// Emits
const emit = defineEmits<{
  'update:modelValue': [value: typeof props.modelValue]
}>()

// 默认按钮配置
const defaultButtonConfig = {
  position: 'newline' as const,
  alignment: 'start' as const,
  buttons: [
    {
      type: 'search' as ButtonType,
      enabled: true
    },
    {
      type: 'reset' as ButtonType,
      enabled: true
    },
    {
      type: 'export' as ButtonType,
      enabled: true
    }
  ]
}

// 合并按钮配置的函数
const mergeButtons = (existingButtons: any[], defaultButtons: any[]) => {
  const merged = [...defaultButtons]

  // 用现有配置覆盖默认配置
  existingButtons?.forEach(existingBtn => {
    const index = merged.findIndex(btn => btn.type === existingBtn.type)
    if (index >= 0) {
      merged[index] = { ...merged[index], ...existingBtn }
    }
  })

  return merged
}

// 本地状态
const localButtonConfig = ref({
  ...defaultButtonConfig,
  ...props.modelValue,
  buttons: mergeButtons(props.modelValue?.buttons || [], defaultButtonConfig.buttons)
})



// 监听变化
watch(localButtonConfig, (newValue) => {
  emit('update:modelValue', newValue)
}, { deep: true })

watch(() => props.modelValue, (newValue) => {
  if (newValue && JSON.stringify(newValue) !== JSON.stringify(localButtonConfig.value)) {
    localButtonConfig.value = {
      ...defaultButtonConfig,
      ...newValue,
      buttons: mergeButtons(newValue.buttons || [], defaultButtonConfig.buttons)
    }
  }
}, { deep: true, immediate: true })

// 方法
const getButtonLabel = (type: ButtonType) => {
  const labelMap = {
    search: '查询',
    reset: '重置',
    export: '自定义导出'
  }
  return labelMap[type]
}
</script>
