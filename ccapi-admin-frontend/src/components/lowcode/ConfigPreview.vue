<template>
  <div class="h-full flex flex-col bg-base-100 rounded-lg border">
    <!-- 预览工具栏 -->
    <div class="flex items-center justify-between p-3 border-b bg-base-200 rounded-t-lg">
      <div class="flex items-center gap-2">
        <div class="w-3 h-3 bg-error rounded-full"></div>
        <div class="w-3 h-3 bg-warning rounded-full"></div>
        <div class="w-3 h-3 bg-success rounded-full"></div>
        <span class="text-xs text-base-content/60 ml-2">预览模式</span>
      </div>
      
      <div class="flex items-center gap-2">
        <button 
          class="btn btn-ghost btn-xs"
          @click="refreshPreview"
          title="刷新预览"
        >
          <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"/>
          </svg>
        </button>
        
        <button 
          class="btn btn-ghost btn-xs"
          @click="toggleFullscreen"
          title="全屏预览"
        >
          <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 8V4m0 0h4M4 4l5 5m11-1V4m0 0h-4m4 0l-5 5M4 16v4m0 0h4m-4 0l5-5m11 5l-5-5m5 5v-4m0 4h-4"/>
          </svg>
        </button>
      </div>
    </div>

    <!-- 预览内容 -->
    <div class="flex-1 overflow-auto p-4 space-y-4">
      <!-- 搜索表单预览 -->
      <div v-if="config.searchConfig.fields.length > 0" class="space-y-2">
        <h5 class="text-sm font-medium text-base-content/80">搜索表单</h5>
        <div class="bg-base-200 p-3 rounded border">
          <div class="grid grid-cols-1 md:grid-cols-3 gap-2">
            <div 
              v-for="field in enabledSearchFields" 
              :key="field.id"
              class="form-control"
            >
              <label v-if="field.label" class="label py-1">
                <span class="label-text text-xs">{{ field.label }}</span>
              </label>
              
              <!-- 文本输入 -->
              <input 
                v-if="field.type === 'input'"
                type="text" 
                class="input input-bordered input-xs"
                :placeholder="field.placeholder"
                disabled
              />
              
              <!-- 下拉选择 -->
              <select 
                v-else-if="field.type === 'select'"
                class="select select-bordered select-xs"
                disabled
              >
                <option>{{ field.placeholder || '请选择' }}</option>
                <option 
                  v-for="option in field.options" 
                  :key="option.value"
                >
                  {{ option.label }}
                </option>
              </select>
              
              <!-- 日期选择 -->
              <input 
                v-else-if="field.type === 'date'"
                type="date" 
                class="input input-bordered input-xs"
                disabled
              />
              
              <!-- 数字输入 -->
              <input 
                v-else-if="field.type === 'number'"
                type="number" 
                class="input input-bordered input-xs"
                :placeholder="field.placeholder"
                disabled
              />
              
              <!-- 多行文本 -->
              <textarea 
                v-else-if="field.type === 'textarea'"
                class="textarea textarea-bordered textarea-xs"
                :placeholder="field.placeholder"
                rows="2"
                disabled
              ></textarea>
            </div>
          </div>
          
          <div class="flex gap-2 mt-3">
            <button class="btn btn-primary btn-xs" disabled>查询</button>
            <button class="btn btn-ghost btn-xs" disabled>重置</button>
          </div>
        </div>
      </div>

      <!-- 表格预览 -->
      <div class="space-y-2">
        <div class="flex items-center justify-between">
          <h5 class="text-sm font-medium text-base-content/80">数据表格</h5>
          <div class="flex gap-1">

          </div>
        </div>
        
        <div class="border rounded overflow-hidden">
          <div class="overflow-x-auto">
            <table
              class="table"
              :class="{
                'table-xs': config.tableConfig.size === 'small',
                'table-sm': config.tableConfig.size === 'medium',
                'table-lg': config.tableConfig.size === 'large',

              }"
              style="table-layout: fixed; width: auto;"
            >
              <thead class="bg-base-200">
                <tr>

                  <th 
                    v-for="column in enabledColumns" 
                    :key="column.id"
                    :style="{ width: column.width ? `${column.width}px` : 'auto' }"
                    :class="`text-${column.align || 'left'}`"
                  >
                    <div class="flex items-center gap-1">
                      {{ column.label }}
                      <svg v-if="column.sortable" class="w-3 h-3 opacity-50" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16V4m0 0L3 8m4-4l4 4m6 0v12m0 0l4-4m-4 4l-4-4"/>
                      </svg>
                    </div>
                  </th>
                </tr>
              </thead>
              <tbody>
                <tr v-for="i in 3" :key="i">

                  <td
                    v-for="column in enabledColumns"
                    :key="column.id"
                    :style="{ width: column.width ? `${column.width}px` : 'auto' }"
                    :class="`text-${column.align || 'left'}`"
                  >
                    <span class="text-xs text-base-content/60">
                      {{ getPreviewData(column, i) }}
                    </span>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
        
        <!-- 分页预览 -->
        <div v-if="config.tableConfig.pagination" class="flex items-center justify-between text-xs">
          <div class="text-base-content/60">
            共 {{ mockTotal }} 条记录
          </div>
          <div class="flex items-center gap-2">
            <button class="btn btn-ghost btn-xs" disabled>上一页</button>
            <div class="flex gap-1">
              <button class="btn btn-primary btn-xs" disabled>1</button>
              <button class="btn btn-ghost btn-xs" disabled>2</button>
              <button class="btn btn-ghost btn-xs" disabled>3</button>
            </div>
            <button class="btn btn-ghost btn-xs" disabled>下一页</button>
          </div>
        </div>
      </div>

      <!-- 导出配置预览 -->
      <div v-if="config.exportConfig.enabled" class="space-y-2">
        <h5 class="text-sm font-medium text-base-content/80">导出配置</h5>
        <div class="bg-base-200 p-3 rounded border">
          <div class="text-xs space-y-1">
            <div>
              <span class="font-medium">支持格式:</span>
              <span class="ml-2">{{ config.exportConfig.formats.join(', ') }}</span>
            </div>
            <div>
              <span class="font-medium">导出列数:</span>
              <span class="ml-2">{{ config.exportConfig.columns.length }} 列</span>
            </div>
            <div>
              <span class="font-medium">文件名:</span>
              <span class="ml-2">{{ previewFilename }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import type { LowCodeConfig } from '@/types/lowcode'
import { formatDate } from '@/utils/lowcode'

// Props
const props = defineProps<{
  config: LowCodeConfig
}>()

// 计算属性
const enabledSearchFields = computed(() => {
  return props.config.searchConfig.fields.filter(field => field.enabled)
})

const enabledColumns = computed(() => {
  return props.config.tableConfig.columns.filter(column => column.enabled)
})

const mockTotal = computed(() => 1247) // 模拟总数

const previewFilename = computed(() => {
  const now = new Date()
  const timestamp = formatDate(now, 'YYYYMMDDHHmmss')
  const date = formatDate(now, 'YYYY-MM-DD')
  
  return props.config.exportConfig.filename
    .replace('{name}', props.config.name)
    .replace('{timestamp}', timestamp)
    .replace('{date}', date) + '.xlsx'
})

// 方法
const getPreviewData = (column: any, rowIndex: number) => {
  // 生成模拟数据
  switch (column.type) {
    case 'text':
      return `示例${column.label}${rowIndex}`
    case 'number':
      return (Math.random() * 1000).toFixed(0)
    case 'date':
      return formatDate(new Date(), 'YYYY-MM-DD')
    case 'datetime':
      return formatDate(new Date(), 'YYYY-MM-DD HH:mm')
    case 'enum':
      if (column.enumOptions) {
        const values = Object.values(column.enumOptions)
        return values[rowIndex % values.length]
      }
      return '选项' + rowIndex
    case 'boolean':
      return rowIndex % 2 === 0 ? '是' : '否'
    default:
      return `数据${rowIndex}`
  }
}

const refreshPreview = () => {
  // 刷新预览逻辑
  console.log('刷新预览')
}

const toggleFullscreen = () => {
  // 全屏预览逻辑
  console.log('全屏预览')
}
</script>
