<template>
  <div v-if="show" class="modal modal-open">
    <div class="modal-box w-11/12 max-w-4xl">
      <h3 class="font-bold text-lg mb-4">内置函数帮助</h3>
      
      <!-- 分类标签 -->
      <div class="tabs tabs-boxed mb-4">
        <button 
          v-for="category in categories" 
          :key="category.key"
          class="tab"
          :class="{ 'tab-active': activeCategory === category.key }"
          @click="activeCategory = category.key"
        >
          {{ category.name }}
        </button>
      </div>

      <!-- 函数列表 -->
      <div class="space-y-3 max-h-96 overflow-y-auto">
        <div 
          v-for="func in filteredFunctions" 
          :key="func.name"
          class="card bg-base-200 p-4 cursor-pointer hover:bg-base-300 transition-colors"
          @click="selectFunction(func)"
        >
          <div class="flex items-start justify-between">
            <div class="flex-1">
              <h4 class="font-semibold text-primary">{{ func.name }}()</h4>
              <p class="text-sm text-base-content/70 mt-1">{{ func.description }}</p>
              
              <!-- 参数信息 -->
              <div v-if="func.parameters && func.parameters.length > 0" class="mt-2">
                <div class="text-xs text-base-content/60 mb-1">参数：</div>
                <div class="flex flex-wrap gap-1">
                  <span 
                    v-for="param in func.parameters" 
                    :key="param.name"
                    class="badge badge-outline badge-xs"
                    :class="{ 'badge-error': param.required }"
                  >
                    {{ param.name }}: {{ param.type }}
                    {{ param.required ? '*' : '' }}
                  </span>
                </div>
              </div>

              <!-- 示例 -->
              <div class="mt-2">
                <div class="text-xs text-base-content/60 mb-1">示例：</div>
                <code class="bg-base-300 px-2 py-1 rounded text-xs">{{ func.example }}</code>
              </div>
            </div>

            <button class="btn btn-primary btn-sm">
              选择
            </button>
          </div>
        </div>
      </div>

      <div class="modal-action">
        <button class="btn" @click="close">取消</button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { dynamicParamResolver } from '@/utils/dynamic-params'
import type { BuiltinFunction } from '@/utils/dynamic-params'

// Props
const props = defineProps<{
  show: boolean
}>()

// Emits
const emit = defineEmits<{
  'update:show': [value: boolean]
  'select': [functionCall: string]
}>()

// 状态
const activeCategory = ref('time')
const functions = ref<BuiltinFunction[]>([])

// 分类
const categories = [
  { key: 'time', name: '时间函数（秒级时间戳）' },
  { key: 'user', name: '用户函数' },
  { key: 'utility', name: '工具函数' }
]

// 计算属性
const filteredFunctions = computed(() => {
  return functions.value.filter(func => func.category === activeCategory.value)
})

// 方法
const close = () => {
  emit('update:show', false)
}

const selectFunction = (func: BuiltinFunction) => {
  // 始终生成基础函数名，参数将在参数配置区域处理
  let functionCall = `${func.name}()`

  // 如果有参数，生成带占位符的调用以便用户知道需要配置参数
  if (func.parameters && func.parameters.length > 0) {
    const placeholders = func.parameters.map(p => `<${p.name}>`).join(', ')
    functionCall = `${func.name}(${placeholders})`
  }

  emit('select', functionCall)
}

// 生命周期
onMounted(() => {
  functions.value = dynamicParamResolver.getBuiltinFunctions()
})
</script>
