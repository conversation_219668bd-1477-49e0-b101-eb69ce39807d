<template>
  <div class="space-y-6">
    <!-- 表格基础配置 -->
    <div class="card bg-base-200 p-4">
      <h4 class="font-semibold mb-4 flex items-center gap-2">
        <svg class="w-5 h-5 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 10h18M3 6h18m-9 8h9"/>
        </svg>
        表格基础配置
      </h4>
      
      <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div class="form-control">
          <label class="label">
            <span class="label-text">表格尺寸</span>
          </label>
          <select 
            v-model="localTableConfig.size"
            class="select select-bordered"
          >
            <option value="small">紧凑</option>
            <option value="medium">标准</option>
            <option value="large">宽松</option>
          </select>
        </div>
        
        <div class="form-control">
          <label class="label">
            <span class="label-text">行唯一标识</span>
          </label>
          <input 
            v-model="localTableConfig.rowKey"
            type="text" 
            class="input input-bordered"
            placeholder="id"
          />
        </div>
        

      </div>
      

    </div>

    <!-- 列配置 -->
    <div class="card bg-base-200 p-4">
      <div class="flex items-center justify-between mb-4">
        <h4 class="font-semibold flex items-center gap-2">
          <svg class="w-5 h-5 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 17V7m0 10a2 2 0 01-2 2H5a2 2 0 01-2-2V7a2 2 0 012-2h2a2 2 0 012 2m0 10a2 2 0 002 2h2a2 2 0 002-2M9 7a2 2 0 012-2h2a2 2 0 012 2m0 10V7m0 10a2 2 0 002 2h2a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2h2a2 2 0 002-2z"/>
          </svg>
          表格列配置
          <span class="badge badge-primary badge-sm">{{ enabledColumnsCount }}</span>
        </h4>
        
        <button 
          class="btn btn-outline btn-sm"
          @click="addColumn"
        >
          <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"/>
          </svg>
          添加列
        </button>
      </div>
      
      <div v-if="localTableConfig.columns.length === 0" class="text-center py-8">
        <svg class="w-12 h-12 mx-auto text-base-content/30 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 17V7m0 10a2 2 0 01-2 2H5a2 2 0 01-2-2V7a2 2 0 012-2h2a2 2 0 012 2m0 10a2 2 0 002 2h2a2 2 0 002-2M9 7a2 2 0 012-2h2a2 2 0 012 2m0 10V7m0 10a2 2 0 002 2h2a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2h2a2 2 0 002-2z"/>
        </svg>
        <p class="text-base-content/60">暂无表格列，点击"添加列"开始配置</p>
      </div>

      <div v-else class="space-y-6">
        <Draggable
          v-model="localTableConfig.columns"
          item-key="id"
          handle=".drag-handle"
          @end="onDragEnd"
          class="space-y-6"
        >
          <template #item="{ element: column, index }">
            <div
              class="bg-base-100 rounded-lg p-6 border mb-6"
              :class="{ 'border-primary': column.enabled, 'border-base-300 opacity-60': !column.enabled }"
            >
              <div class="flex items-center gap-4">
                <!-- 拖拽手柄 -->
                <div class="drag-handle cursor-move text-base-content/40 hover:text-primary">
                  <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 8h16M4 16h16"/>
                  </svg>
                </div>

                <!-- 启用开关 -->
                <input 
                  v-model="column.enabled"
                  type="checkbox" 
                  class="toggle toggle-primary toggle-sm"
                />

                <!-- 列信息 -->
                <div class="flex-1 space-y-4">
                  <!-- 第一行：基本信息 -->
                  <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
                    <div class="form-control">
                      <label class="label label-text-alt">字段名</label>
                      <input
                        v-model="column.key"
                        type="text"
                        class="input input-bordered input-sm"
                        placeholder="fieldKey"
                      />
                    </div>

                    <div class="form-control">
                      <label class="label label-text-alt">列标题</label>
                      <input
                        v-model="column.label"
                        type="text"
                        class="input input-bordered input-sm"
                        placeholder="列标题"
                      />
                    </div>

                    <div class="form-control">
                      <label class="label label-text-alt">数据类型</label>
                      <select
                        v-model="column.type"
                        class="select select-bordered select-sm"
                        @change="onColumnTypeChange(column)"
                      >
                        <option value="text">文本</option>
                        <option value="number">数字</option>
                        <option value="date">日期</option>
                        <option value="datetime">日期时间</option>
                        <option value="enum">枚举</option>
                        <option value="boolean">布尔值</option>
                      </select>
                    </div>

                    <div class="form-control">
                      <label class="label label-text-alt">宽度(px)</label>
                      <input
                        v-model.number="column.width"
                        type="number"
                        class="input input-bordered input-sm"
                        placeholder="120"
                        min="50"
                      />
                    </div>
                  </div>

                  <!-- 第二行：对齐方式和时间格式 -->
                  <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
                    <div class="form-control">
                      <label class="label label-text-alt">对齐方式</label>
                      <select
                        v-model="column.align"
                        class="select select-bordered select-sm"
                      >
                        <option value="left">左对齐</option>
                        <option value="center">居中</option>
                        <option value="right">右对齐</option>
                      </select>
                    </div>

                    <!-- 时间格式配置 -->
                    <div v-if="column.type === 'date' || column.type === 'datetime'" class="form-control md:col-span-3">
                      <label class="label label-text-alt">时间格式</label>
                      <div class="flex gap-2">
                        <select
                          v-model="column.dateFormat"
                          :key="`${column.id}-${column.type}`"
                          class="select select-bordered select-sm flex-1"
                          @change="updateDateFormatter(column)"
                        >
                          <option value="">选择格式</option>
                          <!-- 日期格式选项 - 只在日期类型时显示 -->
                          <template v-if="column.type === 'date'">
                            <option value="YYYY-MM-DD">YYYY-MM-DD (2024-12-24)</option>
                            <option value="YYYY/MM/DD">YYYY/MM/DD (2024/12/24)</option>
                            <option value="MM-DD-YYYY">MM-DD-YYYY (12-24-2024)</option>
                            <option value="DD/MM/YYYY">DD/MM/YYYY (24/12/2024)</option>
                          </template>
                          <!-- 日期时间格式选项 - 只在日期时间类型时显示 -->
                          <template v-if="column.type === 'datetime'">
                            <option value="YYYY-MM-DD HH:mm:ss">YYYY-MM-DD HH:mm:ss (2024-12-24 14:30:22)</option>
                            <option value="YYYY-MM-DD HH:mm">YYYY-MM-DD HH:mm (2024-12-24 14:30)</option>
                            <option value="MM/DD/YYYY HH:mm">MM/DD/YYYY HH:mm (12/24/2024 14:30)</option>
                            <option value="DD/MM/YYYY HH:mm">DD/MM/YYYY HH:mm (24/12/2024 14:30)</option>
                          </template>
                          <option value="custom">自定义格式</option>
                        </select>

                        <!-- 自定义格式输入 -->
                        <input
                          v-if="column.dateFormat === 'custom'"
                          v-model="column.customDateFormat"
                          type="text"
                          class="input input-bordered input-sm flex-1"
                          :placeholder="column.type === 'date' ? 'YYYY-MM-DD' : 'YYYY-MM-DD HH:mm:ss'"
                          @blur="updateCustomDateFormatter(column)"
                        />
                      </div>

                      <!-- 格式预览 -->
                      <div v-if="getDateFormatPreview(column)" class="mt-1">
                        <span class="text-xs text-base-content/60">预览: {{ getDateFormatPreview(column) }}</span>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- 枚举选项配置 -->
                <div v-if="column.type === 'enum'" class="mt-4 pt-4 border-t">
                  <label class="label label-text-alt">枚举选项配置</label>
                  <textarea
                    v-model="column.enumOptionsText"
                    class="textarea textarea-bordered textarea-sm"
                    placeholder='{"0": "选项1", "1": "选项2"}'
                    rows="3"
                    @blur="updateEnumOptions(column)"
                  ></textarea>
                  <div class="label">
                    <span class="label-text-alt">JSON格式，如: {"0": "选项1", "1": "选项2"}</span>
                  </div>
                </div>

                <!-- 操作按钮 -->
                <div class="flex gap-2">
                  <button 
                    class="btn btn-ghost btn-sm"
                    @click="editColumn(index)"
                    title="编辑"
                  >
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"/>
                    </svg>
                  </button>
                  
                  <button 
                    class="btn btn-ghost btn-sm text-error"
                    @click="removeColumn(index)"
                    title="删除"
                  >
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"/>
                    </svg>
                  </button>
                </div>
              </div>

              <!-- 功能选项 -->
              <div class="mt-4 pt-4 border-t flex flex-wrap gap-4">
                <label class="label cursor-pointer">
                  <input 
                    v-model="column.sortable"
                    type="checkbox" 
                    class="checkbox checkbox-sm"
                  />
                  <span class="label-text ml-2">可排序</span>
                </label>
                

                
                <label class="label cursor-pointer">
                  <input 
                    v-model="column.resizable"
                    type="checkbox" 
                    class="checkbox checkbox-sm"
                  />
                  <span class="label-text ml-2">可调整大小</span>
                </label>
              </div>
            </div>
          </template>
        </Draggable>
      </div>
    </div>

    <!-- 分页配置 -->
    <div class="card bg-base-200 p-4">
      <h4 class="font-semibold mb-4">分页配置</h4>

      <!-- 基础分页设置 -->
      <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
        <div class="form-control">
          <label class="label">
            <span class="label-text">默认页大小</span>
          </label>
          <input
            v-model.number="localTableConfig.pagination.pageSize"
            type="number"
            class="input input-bordered"
            placeholder="20"
            min="1"
            max="1000"
          />
        </div>

        <div class="form-control">
          <label class="label">
            <span class="label-text">页码参数名</span>
          </label>
          <input
            v-model="localTableConfig.pagination.pageParamName"
            type="text"
            class="input input-bordered"
            placeholder="page"
          />
        </div>

        <div class="form-control">
          <label class="label">
            <span class="label-text">页大小参数名</span>
          </label>
          <input
            v-model="localTableConfig.pagination.pageSizeParamName"
            type="text"
            class="input input-bordered"
            placeholder="limit"
          />
        </div>
      </div>

      <!-- 页码选项和显示设置 -->
      <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div class="form-control">
          <label class="label">
            <span class="label-text">页码选项</span>
          </label>
          <input
            v-model="pageSizeOptionsText"
            type="text"
            class="input input-bordered"
            placeholder="10,20,50,100"
            @blur="updatePageSizeOptions"
            @keyup.enter="updatePageSizeOptions"
          />
        </div>

        <div class="form-control">
          <label class="label">
            <span class="label-text-alt text-base-content/60">
              分页组件将默认显示：页码选择器、快速跳转、总数统计
            </span>
          </label>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import Draggable from 'vuedraggable'
import type { TableConfig, ColumnConfig } from '@/types/lowcode'
import { generateUniqueId } from '@/utils/lowcode'

// Props
const props = defineProps<{
  modelValue: TableConfig
}>()

// Emits
const emit = defineEmits<{
  'update:modelValue': [value: TableConfig]
}>()

// 本地状态
const localTableConfig = ref<TableConfig>(JSON.parse(JSON.stringify(props.modelValue)))
const pageSizeOptionsText = ref(props.modelValue.pagination.pageSizeOptions.join(','))

// 初始化列配置
const initializeColumns = () => {
  localTableConfig.value.columns.forEach(column => {
    // 初始化日期格式配置
    if ((column.type === 'date' || column.type === 'datetime') && column.formatter?.format) {
      column.dateFormat = column.formatter.format
    }

    // 初始化枚举选项文本
    if (column.type === 'enum' && column.enumOptions) {
      column.enumOptionsText = JSON.stringify(column.enumOptions)
    } else {
      column.enumOptionsText = '{}'
    }

    // 确保必要字段存在
    if (!column.dateFormat) column.dateFormat = ''
    if (!column.customDateFormat) column.customDateFormat = ''
  })
}

// 执行初始化
initializeColumns()

// 计算属性
const enabledColumnsCount = computed(() => {
  return localTableConfig.value.columns.filter(column => column.enabled).length
})

// 监听变化
watch(localTableConfig, (newValue) => {
  emit('update:modelValue', newValue)
}, { deep: true })

watch(() => props.modelValue, (newValue) => {
  // 避免递归更新：只有当值真正不同时才更新
  if (JSON.stringify(newValue) !== JSON.stringify(localTableConfig.value)) {
    localTableConfig.value = JSON.parse(JSON.stringify(newValue))
    // 只有当页码选项真正改变时才更新文本框的值
    const newOptionsText = newValue.pagination.pageSizeOptions.join(',')
    if (pageSizeOptionsText.value !== newOptionsText) {
      pageSizeOptionsText.value = newOptionsText
    }
  }
}, { deep: true, immediate: true })

// 方法
const addColumn = () => {
  const newColumn: ColumnConfig = {
    id: generateUniqueId('column'),
    key: '',
    label: '',
    type: 'text',
    width: 120,
    sortable: false,
    resizable: true,
    enabled: true,
    order: localTableConfig.value.columns.length,
    align: 'left',
    dateFormat: '', // 初始化日期格式
    customDateFormat: '', // 初始化自定义日期格式
    enumOptionsText: '{}' // 初始化枚举选项文本
  }

  localTableConfig.value.columns.push(newColumn)
}

const removeColumn = (index: number) => {
  if (confirm('确定要删除这个列吗？')) {
    localTableConfig.value.columns.splice(index, 1)
    updateOrder()
  }
}

const editColumn = (index: number) => {
  // 这里可以打开列编辑模态框
  console.log('编辑列:', index)
}

const onDragEnd = () => {
  updateOrder()
}

const updateOrder = () => {
  localTableConfig.value.columns.forEach((column, index) => {
    column.order = index
  })
}

// 更新日期格式化器
const updateDateFormatter = (column: any) => {
  if (!column.dateFormat || column.dateFormat === 'custom') return

  // 创建或更新格式化器
  if (!column.formatter) {
    column.formatter = { type: 'date' }
  }
  column.formatter.type = 'date'
  column.formatter.format = column.dateFormat
}

// 更新自定义日期格式化器
const updateCustomDateFormatter = (column: any) => {
  if (!column.customDateFormat) return

  // 创建或更新格式化器
  if (!column.formatter) {
    column.formatter = { type: 'date' }
  }
  column.formatter.type = 'date'
  column.formatter.format = column.customDateFormat
}

// 获取日期格式预览
const getDateFormatPreview = (column: any) => {
  try {
    const now = new Date()
    let format = ''

    if (column.dateFormat === 'custom') {
      format = column.customDateFormat || ''
    } else {
      format = column.dateFormat || ''
    }

    if (!format) return ''

    // 简单的格式化预览
    const year = now.getFullYear()
    const month = String(now.getMonth() + 1).padStart(2, '0')
    const day = String(now.getDate()).padStart(2, '0')
    const hours = String(now.getHours()).padStart(2, '0')
    const minutes = String(now.getMinutes()).padStart(2, '0')
    const seconds = String(now.getSeconds()).padStart(2, '0')

    return format
      .replace(/YYYY/g, year.toString())
      .replace(/MM/g, month)
      .replace(/DD/g, day)
      .replace(/HH/g, hours)
      .replace(/mm/g, minutes)
      .replace(/ss/g, seconds)
  } catch (error) {
    return '格式错误'
  }
}

// 处理列类型变化
const onColumnTypeChange = (column: any) => {
  // 如果切换到日期类型，且当前格式是时间格式，则重置为日期格式
  if (column.type === 'date' && column.dateFormat) {
    const timeFormats = ['YYYY-MM-DD HH:mm:ss', 'YYYY-MM-DD HH:mm', 'MM/DD/YYYY HH:mm', 'DD/MM/YYYY HH:mm']
    if (timeFormats.includes(column.dateFormat)) {
      column.dateFormat = 'YYYY-MM-DD' // 重置为默认日期格式
      updateDateFormatter(column)
    }
  }

  // 如果切换到日期时间类型，且当前格式是纯日期格式，则重置为日期时间格式
  if (column.type === 'datetime' && column.dateFormat) {
    const dateFormats = ['YYYY-MM-DD', 'YYYY/MM/DD', 'MM-DD-YYYY', 'DD/MM/YYYY']
    if (dateFormats.includes(column.dateFormat)) {
      column.dateFormat = 'YYYY-MM-DD HH:mm:ss' // 重置为默认日期时间格式
      updateDateFormatter(column)
    }
  }

  // 如果改为非日期类型，清除日期格式配置
  if (column.type !== 'date' && column.type !== 'datetime') {
    column.dateFormat = ''
    column.customDateFormat = ''
    if (column.formatter && column.formatter.type === 'date') {
      delete column.formatter
    }
  }
}

// 更新枚举选项
const updateEnumOptions = (column: any) => {
  if (!column.enumOptionsText) {
    column.enumOptions = {}
    return
  }

  try {
    column.enumOptions = JSON.parse(column.enumOptionsText)
  } catch (error) {
    console.error('枚举选项JSON格式错误:', error)
    // 恢复到原来的值
    column.enumOptionsText = JSON.stringify(column.enumOptions || {})
  }
}

const updatePageSizeOptions = () => {
  if (pageSizeOptionsText.value.trim()) {
    const options = pageSizeOptionsText.value
      .split(',')
      .map(item => parseInt(item.trim()))
      .filter(item => !isNaN(item) && item > 0)

    if (options.length > 0) {
      localTableConfig.value.pagination.pageSizeOptions = options
    } else {
      // 如果解析失败，恢复到原来的值
      pageSizeOptionsText.value = localTableConfig.value.pagination.pageSizeOptions.join(',')
    }
  } else {
    // 如果输入为空，恢复到原来的值
    pageSizeOptionsText.value = localTableConfig.value.pagination.pageSizeOptions.join(',')
  }
}
</script>
