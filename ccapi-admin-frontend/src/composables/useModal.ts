/**
 * 模态框 Composable
 * 提供简化的模态框使用接口
 */

import { ref, reactive, type Ref } from 'vue'
import type { ModalConfig, ModalButton } from '@/types/modal'
import { modalPresets, createConfirmDialog, createAlertDialog } from '@/utils/modal'

export interface UseModalReturn {
  // 状态
  show: Ref<boolean>
  config: Ref<ModalConfig>
  loading: Ref<boolean>
  
  // 方法
  showModal: (config: ModalConfig) => void
  hideModal: () => void
  
  // 预设方法
  showDeleteConfirm: (itemName: string, onConfirm?: () => void | Promise<void>) => void
  showSaveConfirm: (onConfirm?: () => void | Promise<void>) => void
  showError: (message: string) => void
  showSuccess: (message: string) => void
  showInfo: (message: string) => void
  
  // 快速方法
  confirm: (title: string, description?: string, options?: {
    confirmText?: string
    cancelText?: string
    onConfirm?: () => void | Promise<void>
    onCancel?: () => void
  }) => void
  
  alert: (type: 'warning' | 'danger' | 'info' | 'success', title: string, description?: string) => void
  
  // 事件处理
  handleConfirm: () => void
  handleCancel: () => void
  handleButtonClick: (index: number, button: ModalButton) => void
}

export function useModal(): UseModalReturn {
  // 状态
  const show = ref(false)
  const loading = ref(false)
  const config = ref<ModalConfig>({
    type: 'info',
    title: '',
    buttons: []
  })

  // 当前的回调函数
  let currentOnConfirm: (() => void | Promise<void>) | undefined
  let currentOnCancel: (() => void) | undefined

  // 基础方法
  const showModal = (modalConfig: ModalConfig) => {
    config.value = modalConfig
    show.value = true
  }

  const hideModal = () => {
    show.value = false
    loading.value = false
    currentOnConfirm = undefined
    currentOnCancel = undefined
  }

  // 预设方法
  const showDeleteConfirm = (itemName: string, onConfirm?: () => void | Promise<void>) => {
    currentOnConfirm = onConfirm
    const modalConfig = modalPresets.deleteConfirm(itemName)
    showModal(modalConfig)
  }

  const showSaveConfirm = (onConfirm?: () => void | Promise<void>) => {
    currentOnConfirm = onConfirm
    const modalConfig = modalPresets.saveConfirm()
    showModal(modalConfig)
  }

  const showError = (message: string) => {
    const modalConfig = modalPresets.errorAlert(message)
    showModal(modalConfig)
  }

  const showSuccess = (message: string) => {
    const modalConfig = modalPresets.successAlert(message)
    showModal(modalConfig)
  }

  const showInfo = (message: string) => {
    const modalConfig = modalPresets.infoAlert(message)
    showModal(modalConfig)
  }

  // 快速方法
  const confirm = (
    title: string, 
    description?: string, 
    options: {
      confirmText?: string
      cancelText?: string
      onConfirm?: () => void | Promise<void>
      onCancel?: () => void
    } = {}
  ) => {
    currentOnConfirm = options.onConfirm
    currentOnCancel = options.onCancel
    
    const modalConfig = createConfirmDialog(title, description, {
      confirmText: options.confirmText,
      cancelText: options.cancelText
    })
    
    showModal(modalConfig)
  }

  const alert = (
    type: 'warning' | 'danger' | 'info' | 'success',
    title: string,
    description?: string
  ) => {
    const modalConfig = createAlertDialog(type, title, description)
    showModal(modalConfig)
  }

  // 事件处理
  const handleConfirm = async () => {
    if (currentOnConfirm) {
      try {
        loading.value = true
        await currentOnConfirm()
        hideModal()
      } catch (error) {
        console.error('Modal confirm error:', error)
        loading.value = false
      }
    } else {
      hideModal()
    }
  }

  const handleCancel = () => {
    if (currentOnCancel) {
      currentOnCancel()
    }
    hideModal()
  }

  const handleButtonClick = async (index: number, button: ModalButton) => {
    // 如果按钮有自己的 onClick，优先执行
    if (button.onClick) {
      try {
        loading.value = true
        await button.onClick()
        hideModal()
      } catch (error) {
        console.error('Button click error:', error)
        loading.value = false
      }
      return
    }

    // 否则根据按钮位置执行默认行为
    if (config.value.buttons?.length === 2) {
      if (index === 0) {
        handleCancel()
      } else {
        await handleConfirm()
      }
    } else {
      hideModal()
    }
  }

  return {
    // 状态
    show,
    config,
    loading,
    
    // 方法
    showModal,
    hideModal,
    
    // 预设方法
    showDeleteConfirm,
    showSaveConfirm,
    showError,
    showSuccess,
    showInfo,
    
    // 快速方法
    confirm,
    alert,
    
    // 事件处理
    handleConfirm,
    handleCancel,
    handleButtonClick
  }
}
