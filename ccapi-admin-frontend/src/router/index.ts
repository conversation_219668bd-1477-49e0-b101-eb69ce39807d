import { createRouter, createWebHistory } from 'vue-router'
import HomeView from '../views/HomeView.vue'
import LowCodeConfigView from '../views/LowCodeConfigView.vue'
import LowCodeRuntimeView from '../views/LowCodeRuntimeView.vue'

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/login',
      name: 'login',
      component: () => import('../views/LoginView.vue'),
      meta: { requiresGuest: true }
    },
    {
      path: '/',
      name: 'home',
      component: HomeView,
      meta: { requiresAuth: true }
    },
    {
      path: '/about',
      name: 'about',
      component: () => import('../views/AboutView.vue'),
      meta: { requiresAuth: true }
    },
    {
      path: '/orders',
      name: 'orders',
      component: () => import('../views/OrderView.vue'),
      meta: { requiresAuth: true }
    },

    {
      path: '/lowcode',
      name: 'lowcode',
      component: LowCodeConfigView,
      meta: { requiresAuth: true }
    },
    {
      path: '/lowcode/runtime',
      name: 'lowcode-runtime',
      component: LowCodeRuntimeView,
      meta: { requiresAuth: true }
    },


  ],
})

// 路由守卫（兼容原有认证系统）
router.beforeEach((to, from, next) => {
  // 检查本地存储中的登录状态（兼容多种token存储方式）
  const token = localStorage.getItem('token') ||
                localStorage.getItem('authToken') ||
                localStorage.getItem('X-Token')
  const isLoggedIn = !!token

  // 检查是否需要认证
  if (to.meta.requiresAuth && !isLoggedIn) {
    // 需要认证但未登录，跳转到登录页
    next({ name: 'login', query: { redirect: to.fullPath } })
    return
  }

  // 检查是否需要游客状态（如登录页）
  if (to.meta.requiresGuest && isLoggedIn) {
    // 已登录用户访问登录页，跳转到首页
    next({ name: 'home' })
    return
  }

  next()
})

export default router
