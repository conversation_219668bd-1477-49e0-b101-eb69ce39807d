/**
 * 模态框组件类型定义
 */

// 模态框类型
export type ModalType = 'warning' | 'danger' | 'info' | 'success'

// 按钮配置
export interface ModalButton {
  text: string
  variant?: 'primary' | 'secondary' | 'error' | 'warning' | 'info' | 'success' | 'ghost'
  loading?: boolean
  disabled?: boolean
  onClick?: () => void | Promise<void>
}

// 模态框配置
export interface ModalConfig {
  // 基本信息
  type: ModalType
  title: string
  subtitle?: string
  description?: string
  
  // 图标配置
  customIcon?: string // SVG 字符串或图标名称
  
  // 按钮配置
  buttons?: ModalButton[]
  
  // 行为配置
  closable?: boolean // 是否可以通过 ESC 或点击背景关闭
  persistent?: boolean // 是否持久化显示（不可关闭）
  
  // 样式配置
  size?: 'sm' | 'md' | 'lg' | 'xl'
  maxWidth?: string
}

// 模态框事件
export interface ModalEvents {
  'update:show': [value: boolean]
  'confirm': []
  'cancel': []
  'close': []
  'button-click': [buttonIndex: number, button: ModalButton]
}

// 预设模态框配置
export interface PresetModalConfigs {
  deleteConfirm: (itemName: string) => ModalConfig
  saveConfirm: () => ModalConfig
  errorAlert: (message: string) => ModalConfig
  successAlert: (message: string) => ModalConfig
  infoAlert: (message: string) => ModalConfig
}
