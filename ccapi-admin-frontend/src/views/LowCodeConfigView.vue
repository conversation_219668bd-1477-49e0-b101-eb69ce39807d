<template>
  <div class="h-full flex flex-col p-4">
    <!-- 顶部工具栏 -->
    <div class="bg-base-100 p-4 rounded-lg shadow-sm flex-shrink-0 mb-4">
      <div class="flex items-center justify-between">
        <div class="flex items-center gap-4">
          <h1 class="text-2xl font-bold text-primary">低代码配置中心</h1>
          <div class="badge badge-info">Beta</div>
        </div>
        <div class="flex items-center gap-3">
          <button
            class="btn btn-primary btn-sm"
            @click="createNewConfig"
          >
            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"/>
            </svg>
            新建配置
          </button>
        </div>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="flex-1 grid grid-cols-12 gap-4 min-h-0 overflow-hidden">
      <!-- 左侧配置列表 -->
      <div class="col-span-2 bg-base-100 rounded-lg shadow-sm p-4 flex flex-col">
        <div class="flex items-center justify-between mb-4">
          <h3 class="font-semibold">配置列表</h3>
          <button
            class="btn btn-ghost btn-xs"
            @click="refreshConfigs"
          >
            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"/>
            </svg>
          </button>
        </div>

        <!-- 搜索框 -->
        <div class="mb-4 space-y-2">
          <div class="form-control">
            <input
              v-model="searchKeyword"
              type="text"
              class="input input-bordered input-sm"
              placeholder="搜索配置名称..."
              @input="handleSearch"
            />
          </div>
          <div class="form-control">
            <input
              v-model="searchRoutePath"
              type="text"
              class="input input-bordered input-sm"
              placeholder="精准搜索路由路径..."
              @input="handleSearch"
            />
          </div>
        </div>
        
        <div class="flex-1 overflow-y-auto space-y-2">
          <div 
            v-for="config in configs" 
            :key="config.id"
            class="p-3 border rounded-lg cursor-pointer transition-all hover:bg-base-200"
            :class="{ 'border-primary bg-primary/10': currentConfig?.id === config.id }"
            @click="selectConfig(config)"
          >
            <div class="font-medium text-sm">{{ config.name }}</div>
            <div class="text-xs text-base-content/60 mt-1">{{ config.description || '无描述' }}</div>
            <div class="flex items-center justify-between mt-2">
              <div class="text-xs text-base-content/40">
                {{ formatDate(config.metadata.updatedAt) }}
              </div>
              <div class="flex gap-1">
                <button 
                  class="btn btn-ghost btn-xs"
                  @click.stop="cloneConfig(config)"
                  title="复制"
                >
                  <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"/>
                  </svg>
                </button>
                <button 
                  class="btn btn-ghost btn-xs text-error"
                  @click.stop="deleteConfig(config.id)"
                  title="删除"
                >
                  <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"/>
                  </svg>
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 中间配置编辑区域 -->
      <div class="col-span-7 bg-base-100 rounded-lg shadow-sm flex flex-col min-h-0">
        <div v-if="!currentConfig" class="flex-1 flex items-center justify-center">
          <div class="text-center">
            <svg class="w-16 h-16 mx-auto text-base-content/30 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
            </svg>
            <p class="text-base-content/60">请选择或创建一个配置</p>
          </div>
        </div>

        <div v-else class="flex flex-col h-full">
          <!-- 配置头部 -->
          <div class="p-4 border-b flex items-center justify-between flex-shrink-0">
            <div>
              <h2 class="text-lg font-semibold">{{ currentConfig.name }}</h2>
              <p class="text-sm text-base-content/60">{{ currentConfig.description || '无描述' }}</p>
            </div>
            <div class="flex gap-2">
              <button 
                class="btn btn-outline btn-sm"
                @click="previewConfig"
              >
                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"/>
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"/>
                </svg>
                预览
              </button>
              <button 
                class="btn btn-primary btn-sm"
                @click="saveCurrentConfig"
                :disabled="saving"
              >
                <span v-if="saving" class="loading loading-spinner loading-xs"></span>
                <svg v-else class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7H5a2 2 0 00-2 2v9a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-3m-1 4l-3 3m0 0l-3-3m3 3V4"/>
                </svg>
                保存
              </button>
            </div>
          </div>

          <!-- 配置选项卡 -->
          <div class="tabs tabs-bordered px-4 flex-shrink-0">
            <button
              v-for="tab in configTabs"
              :key="tab.key"
              class="tab"
              :class="{ 'tab-active': activeTab === tab.key }"
              @click="activeTab = tab.key"
            >
              {{ tab.label }}
            </button>
          </div>

          <!-- 选项卡内容 -->
          <div class="flex-1 p-4 overflow-y-auto min-h-0">
            <!-- 基础配置 -->
            <div v-show="activeTab === 'basic'">
              <BasicConfigPanel v-if="currentConfig" v-model="currentConfig" />
            </div>

            <!-- 表格配置 -->
            <div v-show="activeTab === 'table'">
              <TableConfigPanel v-if="currentConfig" v-model="currentConfig.tableConfig" />
            </div>

            <!-- 搜索配置 -->
            <div v-show="activeTab === 'search'">
              <SearchConfigPanel
                v-if="currentConfig"
                v-model="currentConfig.searchConfig"
              />
            </div>

            <!-- 导出配置 -->
            <div v-show="activeTab === 'export'">
              <ExportConfigPanel v-if="currentConfig" v-model="currentConfig.exportConfig" />
            </div>


          </div>
        </div>
      </div>

      <!-- 右侧预览区域 -->
      <div class="col-span-3 bg-base-100 rounded-lg shadow-sm p-4 flex flex-col">
        <h3 class="font-semibold mb-4">实时预览</h3>
        <div class="flex-1 overflow-hidden">
          <ConfigPreview 
            v-if="currentConfig" 
            :config="currentConfig"
            class="h-full"
          />
          <div v-else class="flex items-center justify-center h-full text-base-content/40">
            暂无预览
          </div>
        </div>
      </div>
    </div>

    <!-- 通用模态框 -->
    <BaseModal
      v-model:show="modal.show.value"
      :config="modal.config.value"
      @confirm="modal.handleConfirm"
      @cancel="modal.handleCancel"
      @button-click="modal.handleButtonClick"
    />

  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue'
import { useLowCodeConfig } from '@/services/lowcode'
import { formatDate, generateUniqueId } from '@/utils/lowcode'
import { toast } from '@/utils/toast'
import { useModal } from '@/composables/useModal'
import type { LowCodeConfig } from '@/types/lowcode'

// 导入子组件
import BasicConfigPanel from '@/components/lowcode/BasicConfigPanel.vue'
import SearchConfigPanel from '@/components/lowcode/SearchConfigPanel.vue'
import TableConfigPanel from '@/components/lowcode/TableConfigPanel.vue'
import ExportConfigPanel from '@/components/lowcode/ExportConfigPanel.vue'
import ConfigPreview from '@/components/lowcode/ConfigPreview.vue'
import BaseModal from '@/components/common/BaseModal.vue'


// 状态管理
const {
  configs,
  currentConfig,
  getConfig,
  saveConfig,
  deleteConfig: deleteConfigService,
  cloneConfig: cloneConfigService,
  setCurrentConfig,
  refreshConfigs: refreshConfigsService
} = useLowCodeConfig()

// 本地状态
const activeTab = ref('basic')
const saving = ref(false)

// 搜索状态
const searchKeyword = ref('')
const searchRoutePath = ref('')

// 模态框
const modal = useModal()

// 配置选项卡
const configTabs = [
  { key: 'basic', label: '基础配置' },
  { key: 'table', label: '表格配置' },
  { key: 'search', label: '搜索配置' },
  { key: 'export', label: '导出配置' }
]

// 方法
const refreshConfigs = async () => {
  const params: any = {}
  if (searchKeyword.value.trim()) {
    params.keyword = searchKeyword.value.trim()
  }
  if (searchRoutePath.value.trim()) {
    params.routePath = searchRoutePath.value.trim()
  }
  await refreshConfigsService(params)
}

// 搜索处理
const handleSearch = () => {
  refreshConfigs()
}

const selectConfig = async (config: LowCodeConfig) => {
  const fullConfig = await getConfig(config.id)
  if (fullConfig) {
    setCurrentConfig(fullConfig)
  }
}

const createNewConfig = () => {
  const newConfig: LowCodeConfig = {
    id: '', // 新建配置不设置ID，让后端生成
    name: '新建配置',
    description: '',
    routePath: '',
    dataSource: {
      url: '/api/data/list',
      method: 'GET',
      params: {},
      headers: {},
      dynamicParams: [],
      responseMapping: {
        dataPath: 'data.list',
        totalPath: 'data.total'
      }
    },
    searchConfig: {
      fields: [],
      layout: {
        gap: '16px',
        buttonConfig: {
          position: 'newline',
          alignment: 'start',
          buttons: [
            {
              type: 'search',
              enabled: true
            },
            {
              type: 'reset',
              enabled: true
            },
            {
              type: 'export',
              enabled: true
            }
          ]
        }
      }
    },
    tableConfig: {
      columns: [],
      pagination: {
        pageSize: 20,
        showSizeChanger: true,
        pageSizeOptions: [10, 20, 50, 100],
        pageParamName: 'page',
        pageSizeParamName: 'pageSize'
      },
      sorting: {
        multiple: false
      },
      features: {
        columnSettings: false,
        fullscreen: false
      },
      rowKey: 'id',
      size: 'medium'
    },
    exportConfig: {
      enabled: true,
      api: {
        url: '/api/export',
        method: 'POST'
      },
      filename: '{name}_{timestamp}',
      columns: [],
      formats: ['excel', 'csv']
    },
    metadata: {
      createdBy: 'current-user',
      createdAt: new Date().toISOString(),
      updatedBy: 'current-user',
      updatedAt: new Date().toISOString()
    }
  }
  
  setCurrentConfig(newConfig)
  activeTab.value = 'basic'

  // 显示提示
  toast.info('已创建新配置，请完善基本信息后保存', {
    title: '新建配置',
    duration: 3000
  })
}



const saveCurrentConfig = async () => {
  if (!currentConfig.value) return

  saving.value = true
  try {
    await saveConfig(currentConfig.value)
    // 刷新配置列表
    await refreshConfigs()

    // 显示成功提示
    toast.success('配置保存成功！', {
      title: '保存成功',
      duration: 2000
    })
  } catch (error) {
    console.error('保存失败:', error)

    // 显示错误提示
    toast.error('配置保存失败，请重试', {
      title: '保存失败',
      duration: 4000
    })
  } finally {
    saving.value = false
  }
}

const cloneConfig = async (config: LowCodeConfig) => {
  const name = prompt('请输入新配置名称:', `${config.name} - 副本`)
  if (name) {
    try {
      const cloned = await cloneConfigService(config.id, name)
      if (cloned) {
        await refreshConfigs()
        setCurrentConfig(cloned)

        // 显示成功提示
        toast.success(`配置 "${name}" 复制成功！`, {
          title: '复制成功',
          duration: 2000
        })
      }
    } catch (error) {
      console.error('复制失败:', error)
      toast.error('配置复制失败，请重试', {
        title: '复制失败',
        duration: 3000
      })
    }
  }
}

// 删除配置
const deleteConfig = (id: string) => {
  const config = configs.value.find(c => c.id === id)
  if (config) {
    modal.showDeleteConfirm(config.name, async () => {
      try {
        const success = await deleteConfigService(id)
        if (success) {
          await refreshConfigs()
          if (currentConfig.value?.id === id) {
            setCurrentConfig(null)
          }

          // 显示成功提示
          toast.success('配置删除成功！', {
            title: '删除成功',
            duration: 2000
          })
        }
      } catch (error) {
        console.error('删除失败:', error)
        toast.error('配置删除失败，请重试', {
          title: '删除失败',
          duration: 3000
        })
        throw error // 重新抛出错误，让模态框处理加载状态
      }
    })
  }
}

const previewConfig = () => {
  // 打开预览页面或模态框
  console.log('预览配置:', currentConfig.value)
}

// 生命周期
onMounted(() => {
  refreshConfigs()
})
</script>

<style scoped>
.tab {
  @apply px-4 flex items-center justify-center;
}

.tab-active {
  @apply border-b-2 border-primary text-primary;
}
</style>
