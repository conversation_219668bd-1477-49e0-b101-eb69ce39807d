package common

import (
	"crypto/sha256"
	"encoding/hex"
	"encoding/json"
	"errors"
	"time"

	"github.com/beego/beego/v2/core/logs"
	beego "github.com/beego/beego/v2/server/web"
	"github.com/golang-jwt/jwt/v5"
)

type JWTClaims struct {
	Key string `json:"key"`
	jwt.RegisteredClaims
}

var (
	TokenExpireDuration   int    // token有效期
	RefreshExpireDuration int    // refresh token有效期
	TokenIssuer           string // token签发者
	JWTSecret             []byte // JWT密钥
)

func init() {
	// 从配置文件读取JWT配置
	// tokenExpire, err := beego.AppConfig.Int("jwt_token_expire")
	// 先共用原来的token有效期配置
	tokenExpire, err := beego.AppConfig.Int("token_expiration")
	if err != nil {
		logs.Error("读取jwt_token_expire配置失败: %v", err)
		return
	}
	TokenExpireDuration = tokenExpire

	refreshExpire, err := beego.AppConfig.Int("jwt_refresh_expire")
	if err != nil {
		logs.Error("读取jwt_refresh_expire配置失败: %v", err)
		return
	}
	RefreshExpireDuration = refreshExpire

	TokenIssuer, err = beego.AppConfig.String("jwt_issuer")
	if err != nil {
		logs.Error("读取jwt_issuer配置失败: %v", err)
		return
	}

	secret, err := beego.AppConfig.String("jwt_secret")
	if err != nil {
		logs.Error("读取jwt_secret配置失败: %v", err)
		return
	}
	JWTSecret = []byte(secret)
}

// GenerateUniqueKey 生成用户唯一标识
func GenerateUniqueKey(username, password string) string {
	h := sha256.New()
	h.Write([]byte(username + password))
	return hex.EncodeToString(h.Sum(nil))
}

// JsonEncode JSON编码
func JsonEncode(v interface{}) ([]byte, error) {
	return json.Marshal(v)
}

// JsonDecode JSON解码
func JsonDecode(data []byte, v interface{}) error {
	return json.Unmarshal(data, v)
}

// GenerateToken 生成token
func GenerateToken(key string, expireDuration time.Duration) (string, error) {
	// 创建token的声明
	claims := JWTClaims{
		key,
		jwt.RegisteredClaims{
			ExpiresAt: jwt.NewNumericDate(time.Now().Add(expireDuration)),
			IssuedAt:  jwt.NewNumericDate(time.Now()),
			Issuer:    TokenIssuer,
		},
	}

	// 生成token
	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
	return token.SignedString(JWTSecret)
}

// ParseToken 解析token
func ParseToken(tokenString string) (*JWTClaims, error) {
	token, err := jwt.ParseWithClaims(tokenString, &JWTClaims{}, func(token *jwt.Token) (interface{}, error) {
		return JWTSecret, nil
	})

	if err != nil {
		return nil, err
	}

	if claims, ok := token.Claims.(*JWTClaims); ok && token.Valid {
		return claims, nil
	}

	return nil, errors.New("invalid token")
}

// RefreshToken 刷新token
// func RefreshToken(refreshTokenString string) (string, string, error) {
// 	// 解析refresh token
// 	claims, err := ParseToken(refreshTokenString)
// 	if err != nil {
// 		return "", "", err
// 	}
//
// 	// 生成新的token对
// 	return GenerateToken(claims.UserId)
// }
