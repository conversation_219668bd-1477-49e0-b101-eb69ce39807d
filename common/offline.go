package common

import (
	"ccapi/server/redis"
	beego "github.com/beego/beego/v2/server/web"
	"time"
)

// Offline struct
type Offline struct {
	deviceId string
	now      time.Time
	nowTs    int64
}

func (t *Offline) Time(now time.Time) *Offline {
	t.now = now
	t.nowTs = t.now.Unix()
	return t
}

// NewOffline Offline init
func NewOffline() *Offline {
	t := new(Offline)
	t.Time(time.Now())
	return t
}

func (t *Offline) SetDeviceId(deviceId string) *Offline {
	t.deviceId = deviceId
	return t
}

// IsTermOffline 判断终端是否离线
// 判断逻辑:
// 1. 获取设备最后离线时间
// 2. 如果返回-1表示设备在线
// 3. 获取配置的离线判断阈值时间
// 4. 当前时间与最后离线时间差值超过阈值则判定为离线
func (t *Offline) IsTermOffline() bool {
	if t.TermOfflineTime() == -1 {
		return false
	} else {
		// 目前是30分钟 1800
		delayOffline, _ := beego.AppConfig.Int64("offline_interval")
		// 如果当前时间与离线时间达到了30分钟间隔，认为是离线
		return t.nowTs-t.TermOfflineTime() > delayOffline
	}
}

// TermOfflineTime 获取终端最后离线时间
// 从Redis中获取设备离线时间戳
// 返回:
// - 0: Redis查询错误
// - -1: 设备在线
// - >0: 最后离线时间戳
func (t *Offline) TermOfflineTime() int64 {
	data, err := redis.HGet(RedisDeviceOfflineTS, t.deviceId)
	if err != nil {
		return 0
	}

	return int64(ChangeStringToInt(data))
}

// Lan1OfflineTime 判断LAN1网口是否离线
// 获取配置的网络离线判断阈值
// 当前时间与最后在线时间差值超过阈值则判定为离线
func (t *Offline) Lan1OfflineTime() int64 {
	data, err := redis.HGet(RedisDeviceLanOfflineTS, t.deviceId)
	if err != nil {
		return 0
	}
	return int64(ChangeStringToInt(data))
}

// Lan2OfflineTime IsLan2Offline 判断LAN2网口是否离线
// 判断逻辑同LAN1
func (t *Offline) Lan2OfflineTime() int64 {
	data, err := redis.HGet(RedisDeviceLan2OfflineTS, t.deviceId)
	if err != nil {
		return 0
	}
	return int64(ChangeStringToInt(data))
}

// Lan1FixedOfflineTime LAN1断开时间(修正)
func (t *Offline) Lan1FixedOfflineTime() int64 {
	data := t.Lan1OfflineTime()
	if data == -1 {
		return time.Now().Unix()
	}
	return data
}

// Lan2FixedOfflineTime LAN2断开时间(修正
// 若为 -1 则未断开, 则延后 5 分钟离线
func (t *Offline) Lan2FixedOfflineTime() int64 {
	data := t.Lan2OfflineTime()
	if data == -1 {
		return time.Now().Unix()
	}
	return data
}

// IsLan1Offline LAN1 口是否断开
func (t *Offline) IsLan1Offline() bool {
	delayLanOffline, _ := beego.AppConfig.Int64("lan_offline_interval")
	return t.nowTs-t.Lan1FixedOfflineTime() > delayLanOffline
}

// IsLan2Offline LAN2 口是否断开
func (t *Offline) IsLan2Offline() bool {
	delayLanOffline, _ := beego.AppConfig.Int64("lan_offline_interval")
	return t.nowTs-t.Lan2FixedOfflineTime() > delayLanOffline
}
