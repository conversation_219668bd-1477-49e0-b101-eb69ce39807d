package protocol

const (
	// StartAutoDriving 开始自动驾驶任务
	StartAutoDriving = 0x0023

	// EmergencyStop 紧急停车
	EmergencyStop = 0x0034

	// Unlock 开中控锁
	Unlock = 0x0004

	// Lock 关中控锁
	Lock = 0x0005

	// GoPoint 移动到指定位置
	GoPoint = 0x0032

	// Whistle 鸣笛
	Whistle = 0x0006

	// FindOta 让设备查询新固件版本
	FindOta = 0x9003

	// FindVcuOta 让设备查询新vcu固件版本
	FindVcuOta = 0xA003

	// UpdateOta 白名单方式OTA软件升级
	UpdateOta = 0xA001

	// Task 下发自动驾驶任务
	Task = 0x0018

	// Download OTA软件包下载开始
	Download = 0x9004

	// DownloadVcu vcu_OTA软件包下载开始
	DownloadVcu = 0xA004

	// Install OTA软件包安装开始
	Install = 0x9005

	// InstallVcu vcu_OTA软件包安装开始
	InstallVcu = 0xA005

	// Reboot 重启
	Reboot = 0x0010

	// StartAuto 启动执行自动驾驶任务
	StartAuto = 0x0023

	// CancelAuto 取消自动驾驶任务
	CancelAuto = 0x0025

	// StopAuto 停止自动驾驶任务
	StopAuto = 0x0026

	// PauseAuto 暂停自动驾驶任务
	PauseAuto = 0x0024

	//Continue 断点续扫
	Continue = 0x0605

	//GoHome 一键回家
	GoHome = 0x0036

	// SetSpeed 设置最高限速
	SetSpeed = 0x0040

	// SelectSpeed 查询最高限速
	SelectSpeed = 0x0041

	// RelieveEmergencyStop 解除紧急停车
	RelieveEmergencyStop = 0x0035

	// SetMap 设置自驾地图
	SetMap = 0x0042

	// SelectMap 查询自驾地图
	SelectMap = 0x0043

	// DeviceSync 车端地图同步到云端
	DeviceSync = 0x0044

	// CloudSync 云端地图同步到车端
	CloudSync = 0x0046

	// SelectAlarm 查询告警详情
	SelectAlarm = 0x0039
)

// CmdMap 命令数组
var CmdMap = map[int]int{
	1:  StartAutoDriving,
	2:  StopAuto,
	3:  EmergencyStop,
	4:  Unlock,
	5:  Lock,
	6:  GoPoint,
	7:  Whistle,
	8:  FindOta,
	9:  Task,
	10: Download,
	11: Install,
	12: FindVcuOta,
	13: DownloadVcu,
	14: InstallVcu,
	15: Reboot,
	16: StartAuto,
	17: CancelAuto,
	18: StopAuto,
	19: PauseAuto,
	20: Continue,
	21: GoHome,
	22: SetSpeed,
	23: UpdateOta,
	24: RelieveEmergencyStop,
	25: SetMap,
	26: SelectMap,
	27: DeviceSync,
	28: CloudSync,
	29: SelectAlarm,
}

// LogMap 日志数组
var LogMap = map[int]string{
	1:  "开始自动驾驶任务",
	2:  "停止自动驾驶任务",
	3:  "紧急停车",
	4:  "开中控锁",
	5:  "关中控锁",
	6:  "移动到指定位置",
	7:  "鸣笛",
	8:  "让设备查询新固件版本",
	9:  "下发自动驾驶任务",
	10: "OTA软件包下载开始",
	11: "OTA软件包安装开始",
	12: "让设备查询新vcu固件版本",
	13: "vcu_OTA软件包下载开始",
	14: "vcu_OTA软件包安装开始",
	15: "重启终端",
	16: "启动执行自动驾驶任务",
	17: "取消自动驾驶任务",
	18: "停止自动驾驶任务",
	19: "暂停自动驾驶任务",
	20: "断点续扫",
	21: "一键回家",
	22: "设置最高限速",
	23: "白名单方式OTA软件升级",
	24: "解除紧急停车",
	25: "设置车辆自动驾驶地图",
	26: "查询车辆自动驾驶地图",
	27: "车端地图同步到云端",
	28: "云端地图同步到车端",
	29: "查询告警详情",
}
