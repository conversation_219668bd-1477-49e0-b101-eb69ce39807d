package common

var (
	// RedisSessionMultiTokenUid token -> uid
	RedisSessionMultiTokenUid = "SESSION::MULTI::TOKEN_%s"

	WxRedisSessionMultiTokenUid = "WX::SESSION::MULTI::TOKEN_%s"

	// // RedisSessionMultiUidToken uid -> token
	// RedisSessionMultiUidToken = "SESSION::MULTI::UID_%d"

	// RedisUserInfof 用户信息 (key)
	RedisUserInfof = "USER::INFO::UID_"

	// RedisUserRule 用户权限
	RedisUserRule = "USER::RULE::UID_"

	// RedisXJUserList 市级管理员列表
	RedisXJUserList = "User::XJ"

	// RedisDeviceOwnerUID 终端拥有者 (set)
	RedisDeviceOwnerUID = "DEVICE::OWNER::UID_"

	// RedisSessionUidTokenHash 储存会话 TOKEN
	RedisSessionUidTokenHash = "SESSION::UID_TOKEN"

	// RedisSessionTokenToUid 储存会话 UID
	RedisSessionTokenToUid = "SESSION::TOKEN_UID"

	// RedisSessionExpireTsHash 储存会话过期时间戳的散列
	RedisSessionExpireTsHash = "SESSION::EXPIRE_TIMESTAMP"

	// RedisDeviceOfflineTS 默认为0，表示设备未上线； 设置为-1，表示设备在线； 设置为其他非负数，表示设备离线时间；
	RedisDeviceOfflineTS = "DEVICE::TS::DEVICE_OFFLINE"

	// RedisDeviceHeartbeatTS 终端心跳时间 (hash) (未上过线: -1)
	RedisDeviceHeartbeatTS = "DEVICE::TS::HEARTBEAT"

	// RedisDeviceLanOfflineTS 终端 LAN1 离线时间 (正常(初始化时为 0): -1, 0, 其它: 离线时间戳) (hash)
	RedisDeviceLanOfflineTS = "DEVICE::TS::LAN_1_OFFLINE"

	// RedisDeviceLan2OfflineTS 终端 LAN2 离线时间 (正常(初始化时为 0): -1, 0, 其它: 离线时间戳) (hash)
	RedisDeviceLan2OfflineTS = "DEVICE::TS::LAN_2_OFFLINE"

	// RedisLastHeartbeat 设备的最后心跳数据,即使server退出，都保留在Redis中，server重启也不清空
	RedisLastHeartbeat = "DEVICE::HB::HASH"

	// RedisDeviceAuthorizedCnt 已授权的设备总量
	RedisDeviceAuthorizedCnt = "DEVICE::AUTHORIZED::CNT"

	// RedisOperationPbMsg 设置终端信息信息 params: imei, operate
	RedisOperationPbMsg = "OPERATION::PB::%s::%d"
)

// UserLevel 用户等级类型(int 的别名
type UserLevel = int

const (
	_       UserLevel = iota // 超级管理员  无用
	SGLevel                  // 市级管理员 已重定义为 省级管理员
	SZLevel                  // 市级值班员 已重定义为 省级值班员
	XGLevel                  // 县级管理员 已重定义为 市级管理员
	XZLevel                  // 县级值班员 已重定义为 市级值班员
)

var (
	// UserLevelArr 用户等级组
	UserLevelArr = map[int]string{
		SGLevel: "h1",
		SZLevel: "h2",
		XGLevel: "l1",
		XZLevel: "l2",
	}
)

// var Model = map[int]string{
//	1:  "首页",
//	2:  "车辆列表",
//	3:  "客户管理",
//	4:  "任务管理",
//	5:  "视频监控",
//	6:  "历史轨迹",
//	7:  "消息中心",
//	8:  "运营管理",
//	9:  "运营统计",
//	10: "升级管理",
//	11: "地图管理",
//	12: "操作记录",
//	13: "广告管理",
// }
