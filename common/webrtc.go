package common

import (
	"ccapi/server/log"
	"encoding/json"
	"errors"
	"fmt"
	"github.com/gorilla/websocket"
	"github.com/pion/interceptor"
	"github.com/pion/interceptor/pkg/intervalpli"
	"github.com/pion/rtp"
	"github.com/pion/webrtc/v4"
	"github.com/pion/webrtc/v4/pkg/media"
	"github.com/pion/webrtc/v4/pkg/media/ivfwriter"
	"github.com/pion/webrtc/v4/pkg/media/oggwriter"
	"net"
	"strings"
	"sync"
)

var (
	MsgLogin          = "login"
	MsgCameraList     = "cameraList"
	MsgCandidate      = "candidate"
	MsgOffer          = "offer"
	MsgAnswer         = "answer"
	MsgHb             = "hb" // 心跳
	MsgGetVehicleList = "getVehicleList"
	peerConnectionMap = new(sync.Map)
)

type udpConn struct {
	conn        *net.UDPConn
	port        int
	payloadType uint8
}

type Receive struct {
	Cmd       string                    `json:"cmd"`
	Code      int                       `json:"code"`
	Info      string                    `json:"info"`
	Answer    webrtc.SessionDescription `json:"answer"`
	Candidate webrtc.ICECandidateInit   `json:"candidate"`
	Offer     webrtc.SessionDescription `json:"offer"`
}

type Terminal struct {
	PeerConnection *webrtc.PeerConnection
	Quit           chan bool
	Imsi           string
	Position       string
}

var AudioPort = 4000
var VideoPort = 4002

func saveToDisk(i media.Writer, track *webrtc.TrackRemote) {
	defer func() {
		if err := i.Close(); err != nil {
			log.Error("28 save err:", err)
		}
	}()

	for {
		rtpPacket, _, err := track.ReadRTP()
		if err != nil {
			log.Error("35 save err:", err)
			return
		}
		if err := i.WriteRTP(rtpPacket); err != nil {
			log.Error("39 save err:", err)
			return
		}
	}
}

func CreateWebrtcChannel(ws *websocket.Conn, imsi string, position string) {
	// Everything below is the Pion WebRTC API! Thanks for using it ❤️.

	// Create a MediaEngine object to configure the supported codec
	m := &webrtc.MediaEngine{}

	// Setup the codecs you want to use.
	// We'll use a VP8 and Opus but you can also define your own
	err := m.RegisterDefaultCodecs()
	if err != nil {
		log.Error(err.Error())
	}

	// Create a InterceptorRegistry. This is the user configurable RTP/RTCP Pipeline.
	// This provides NACKs, RTCP Reports and other features. If you use `webrtc.NewPeerConnection`
	// this is enabled by default. If you are manually managing You MUST create a InterceptorRegistry
	// for each PeerConnection.
	i := &interceptor.Registry{}

	// Register a intervalpli factory
	// This interceptor sends a PLI every 3 seconds. A PLI causes a video keyframe to be generated by the sender.
	// This makes our video seekable and more error resilent, but at a cost of lower picture quality and higher bitrates
	// A real world application should process incoming RTCP packets from viewers and forward them to senders
	intervalPliFactory, err := intervalpli.NewReceiverInterceptor()
	if err != nil {
		log.Error(err.Error())
	}
	i.Add(intervalPliFactory)

	// Use the default set of Interceptors
	if err = webrtc.RegisterDefaultInterceptors(m, i); err != nil {
		log.Error(err.Error())
	}

	// Create the API object with the MediaEngine
	api := webrtc.NewAPI(webrtc.WithMediaEngine(m), webrtc.WithInterceptorRegistry(i))

	// Prepare the configuration
	config := webrtc.Configuration{
		ICEServers: []webrtc.ICEServer{
			{
				URLs: []string{"stun:testpix.pixmoving.city:3478"},
			},
			{
				URLs:       []string{"turn:testpix.pixmoving.city:3478"},
				Username:   "yhl",
				Credential: "123456",
			},
		},
	}

	// Create a new RTCPeerConnection
	peerConnection, err := api.NewPeerConnection(config)

	if err != nil {
		log.Error(err.Error())
	}
	defer func() {
		if cErr := peerConnection.Close(); cErr != nil {
			fmt.Printf("cannot close peerConnection: %v\n", cErr)
		}
	}()

	Add(imsi, peerConnection)

	//Allow us to receive 1 audio track, and 1 video track
	if _, err = peerConnection.AddTransceiverFromKind(webrtc.RTPCodecTypeAudio); err != nil {
		log.Error(err.Error())
	} else if _, err = peerConnection.AddTransceiverFromKind(webrtc.RTPCodecTypeVideo); err != nil {
		log.Error(err.Error())
	}

	// Create a local addr
	var laddr *net.UDPAddr
	if laddr, err = net.ResolveUDPAddr("udp", "127.0.0.1:"); err != nil {
		log.Error("220", err.Error())
	}

	// Prepare udp conns
	// Also update incoming packets with expected PayloadType, the browser may use
	// a different value. We have to modify so our stream matches what rtp-forwarder.sdp expects
	udpConns := map[string]*udpConn{
		"audio": {port: AudioPort, payloadType: 111},
		"video": {port: VideoPort, payloadType: 96},
	}
	for _, c := range udpConns {
		// Create remote addr
		var raddr *net.UDPAddr
		if raddr, err = net.ResolveUDPAddr("udp", fmt.Sprintf("127.0.0.1:%d", c.port)); err != nil {
			log.Error(err.Error())
		}

		// Dial udp
		if c.conn, err = net.DialUDP("udp", laddr, raddr); err != nil {
			log.Error(err.Error())
		}
		defer func(conn net.PacketConn) {
			if closeErr := conn.Close(); closeErr != nil {
				log.Error(closeErr.Error())
			}
		}(c.conn)
	}

	oggFile, err := oggwriter.New("output.ogg", 48000, 2)
	if err != nil {
		log.Error("oggFile create err:", err.Error())
	}
	ivfFile, err := ivfwriter.New("output.ivf")
	if err != nil {
		log.Error("oggFile create err:", err.Error())
	}

	// Set a handler for when a new remote track starts, this handler will forward data to
	// our UDP listeners.
	// In your application this is where you would handle/process audio/video
	peerConnection.OnTrack(func(track *webrtc.TrackRemote, receiver *webrtc.RTPReceiver) {
		// Retrieve udp connection
		c, ok := udpConns[track.Kind().String()]
		if !ok {
			log.Error("206")
		}

		log.Info("onTrack+++++++++++++++++++++++++++++++++++++++++++++++")
		b := make([]byte, 1500)
		rtpPacket := &rtp.Packet{}

		go func() {
			codec := track.Codec()
			if strings.EqualFold(codec.MimeType, webrtc.MimeTypeOpus) {
				fmt.Println("Got Opus track, saving to disk as output.opus (48 kHz, 2 channels)")
				saveToDisk(oggFile, track)
			} else if strings.EqualFold(codec.MimeType, webrtc.MimeTypeVP8) {
				fmt.Println("Got VP8 track, saving to disk as output.ivf")
				saveToDisk(ivfFile, track)
			}
		}()

		for {
			// Read
			n, _, readErr := track.Read(b)
			if readErr != nil {
				log.Error(readErr.Error())
			}

			// Unmarshal the packet and update the PayloadType
			if err = rtpPacket.Unmarshal(b[:n]); err != nil {
				log.Error(err.Error())
			}
			rtpPacket.PayloadType = c.payloadType

			// Marshal into original buffer with updated PayloadType
			if n, err = rtpPacket.MarshalTo(b); err != nil {
				log.Error(err.Error())
			}

			// Write
			if _, writeErr := c.conn.Write(b[:n]); writeErr != nil {
				// For this particular example, third party applications usually timeout after a short
				// amount of time during which the user doesn't have enough time to provide the answer
				// to the browser.
				// That's why, for this particular example, the user first needs to provide the answer
				// to the browser then open the third party application. Therefore we must not kill
				// the forward on "connection refused" errors
				var opError *net.OpError
				if errors.As(writeErr, &opError) && opError.Err.Error() == "write: connection refused" {
					continue
				}
				log.Error("244:", err.Error())
			}
		}
	})

	peerConnection.OnICECandidate(func(candidate *webrtc.ICECandidate) {
		if candidate != nil {
			candidateData := map[string]interface{}{
				"cmd":       MsgCandidate,
				"name":      imsi + "c",
				"candidate": candidate.ToJSON(),
				"position":  position,
			}

			log.Info("candidateData:", candidateData)

			candidateByte, _ := json.Marshal(candidateData)
			ws.WriteMessage(1, candidateByte)
		}
	})

	// Set the handler for ICE connection state
	// This will notify you when the peer has connected/disconnected
	peerConnection.OnICEConnectionStateChange(func(connectionState webrtc.ICEConnectionState) {
		fmt.Printf("Connection State has changed %s \n", connectionState.String())

		if connectionState == webrtc.ICEConnectionStateConnected {
			fmt.Println("Ctrl+C the remote client to stop the demo")
		}
	})

	// Set the handler for Peer connection state
	// This will notify you when the peer has connected/disconnected
	peerConnection.OnConnectionStateChange(func(s webrtc.PeerConnectionState) {
		fmt.Printf("Peer Connection State has changed: %s\n", s.String())

		if s == webrtc.PeerConnectionStateFailed {
			// Wait until PeerConnection has had no network activity for 30 seconds or another failure. It may be reconnected using an ICE Restart.
			// Use webrtc.PeerConnectionStateDisconnected if you are interested in detecting faster timeout.
			// Note that the PeerConnection may come back from PeerConnectionStateDisconnected.
			fmt.Println("Done forwarding 310")
		}

		if s == webrtc.PeerConnectionStateClosed {
			// PeerConnection was explicitly closed. This usually happens from a DTLS CloseNotify
			fmt.Println("Done forwarding 315")
		}
	})

	// Create channel that is blocked until ICE Gathering is complete
	gatherComplete := webrtc.GatheringCompletePromise(peerConnection)

	<-gatherComplete

	select {}
}

func Add(imsi string, data interface{}) {
	peerConnectionMap.Store(imsi, data)
}

func Get(imsi string) interface{} {
	data, _ := peerConnectionMap.Load(imsi)
	return data
}

func All() *sync.Map {
	return peerConnectionMap
}

func Del(imsi string) {
	peerConnectionMap.Delete(imsi)
}
