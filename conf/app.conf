appname = ccapi
httpport = 8086
runmode = dev
autorender = false
copyrequestbody = true
EnableDocs = true

# 通用配置
; token_expiration = 86400
token_expiration = 86400
offline_interval = 1800
lan_offline_interval = 300
disk_name = "/dev/nvme0n1p3"
server_dir = "/data/ccapi/"
certKey = "78fe3379a291ece5"

# MQTT配置
mqtt_broker_url = "wss://testpixconsole.pixmoving.city/wss"
mqtt_client_id = "ccapi-client"

# JWT配置
 # JWT token有效期(秒) 24小时
; jwt_token_expire = 86400
jwt_token_expire = 86400
# JWT refresh token有效期(秒) 7天
jwt_refresh_expire = 604800
# JWT签发者
jwt_issuer = "ccapi"
# JWT密钥(使用certKey)
jwt_secret = "78fe3379a291ece5"

# 验证码配置
# digit:数字验证码 math:算数验证码
captcha_type = "math"
# 验证码长度(仅数字验证码有效)
captcha_length = 4
# 验证码图片宽度
captcha_width = 240
# 验证码图片高度
captcha_height = 80
# 干扰点数量，值越大验证码越模糊
captcha_noise = 25
# 干扰线数量，值越大验证码越难识别
captcha_show_line = 5
# 数字验证码倾斜度(0-1之间,值越大变形程度越大)
captcha_max_skew = 0.7

# 地图投影配置
mapProjectorType = "LocalCartesianUTM"
mapVerticalDatum = "WGS84"
mapOriginLatitude = 26.748334043
mapOriginLongitude = 106.669172767
mapOriginAltitude = 1263.627
