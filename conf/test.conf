# ==================== 数据库配置 ====================
# 使用统一格式: db.{连接名}.{配置项}
# 系统会自动发现所有 db.*.host 配置并创建对应的数据库连接

# 主数据库 (连接名: default)
db.default.host = "**************"
db.default.port = 3306
db.default.user = "root"
db.default.pass = "pix@6688"
db.default.name = "pixmoving-test"
db.default.charset = utf8mb4
db.default.parseTime = true
db.default.loc = Asia/Shanghai
db.default.maxOpenConns = 100
db.default.maxIdleConns = 10
db.default.connMaxLifetime = 3600
db.default.connMaxIdleTime = 1800

# xxlJob数据库 (连接名: xxlJob)
db.xxlJob.host = "**************"
db.xxlJob.port = 3306
db.xxlJob.user = "root"
db.xxlJob.pass = "pix@6688"
db.xxlJob.name = "xxl_job"
db.xxlJob.charset = utf8mb4
db.xxlJob.parseTime = true
db.xxlJob.loc = Asia/Shanghai
db.xxlJob.maxOpenConns = 100
db.xxlJob.maxIdleConns = 10
db.xxlJob.connMaxLifetime = 3600
db.xxlJob.connMaxIdleTime = 1800

# robobusWX数据库 (连接名: robobusWX)
db.robobusWX.host = "**************"
db.robobusWX.port = 3306
db.robobusWX.user = "root"
db.robobusWX.pass = "pix@6688"
db.robobusWX.name = "pixmoving-test"
db.robobusWX.charset = utf8mb4
db.robobusWX.parseTime = true
db.robobusWX.loc = Asia/Shanghai
db.robobusWX.maxOpenConns = 100
db.robobusWX.maxIdleConns = 10
db.robobusWX.connMaxLifetime = 3600
db.robobusWX.connMaxIdleTime = 1800

# wurenche数据库 (连接名: wurenche)
db.wurenche.host = "**************"
db.wurenche.port = 3306
db.wurenche.user = "root"
db.wurenche.pass = "pix@6688"
db.wurenche.name = "pixmoving-test"
db.wurenche.charset = utf8mb4
db.wurenche.parseTime = true
db.wurenche.loc = Asia/Shanghai
db.wurenche.maxOpenConns = 100
db.wurenche.maxIdleConns = 10
db.wurenche.connMaxLifetime = 3600
db.wurenche.connMaxIdleTime = 1800


# ==================== 其他服务配置 ====================

# TDengine配置
tdengineHost = "************"
tdenginePort = "6041"
tdengineUser = "root"
tdenginePass = "Pixm2022"
tdengineDB = "pixmoving"
tdengineEnableSQLLog = false

# Redis配置
redis_host = "**************:6379"
redis_db = 3
redis_pass = "Pix@121cc"

# Kafka配置
kafka_host = "127.0.0.1:9092"
kafka_topic = "device_cmd2"

# 通用配置
token_expiration = 86400
offline_interval = 1800
lan_offline_interval = 300

# OSS配置
endpoint = "oss-accelerate.aliyuncs.com"
accessKeyId = "LTAI5tKnqGSPn3shdAEqNyJx"
accessKeySecret = "******************************"
bucketName = "pix0922"
firmwareOss = 0
ossUrl = "https://oss2.pixmoving.city/"

# 系统配置
disk_name = "/dev/nvme0n1p3"
server_dir = "/data/ccapi/"
certKey = "78fe3379a291ece5"

# 其他配置
es_pass = "pix@6688"
vin_pass = "a65581299ee379ae9c9fb8cec3db5996"
HangZhouUserId = 223
WebSocketAddr = "ws://**************:3900/ws"