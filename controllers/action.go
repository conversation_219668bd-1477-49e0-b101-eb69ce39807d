package controllers

import (
	"ccapi/common"
	"ccapi/common/protocol"
	"ccapi/models/dto"
	"ccapi/models/pix"
	"ccapi/server/redis"
	"ccapi/service"
	"encoding/json"
	"fmt"
	"time"

	"github.com/beego/beego/v2/client/orm"
	beego "github.com/beego/beego/v2/server/web"
)

// ActionController 车辆控制模块
type ActionController struct {
	BaseController
	service                service.DeviceServiceInter
	taskService            service.TaskServiceInter
	executionReportService service.TaskExecutionReportServiceInter
}

// InitActionRouter 初始化路由
func InitActionRouter() beego.LinkNamespace {
	return beego.NSNamespace("/device/action",
		beego.NSRouter("/cmd", &ActionController{}, "post:Cmd"),            // 下发车辆控制命令
		beego.NSRouter("/go_point", &ActionController{}, "post:GoPoint"),   // 移动到指定位置
		beego.NSRouter("/set_speed", &ActionController{}, "post:SetSpeed"), // 设置最高限速
		beego.NSRouter("/set_map", &ActionController{}, "post:SetMap"),     // 设置自驾地图
		beego.NSRouter("/task", &ActionController{}, "post:Task"),          // 下发清扫任务
		beego.NSRouter("/reboot", &ActionController{}, "post:Reboot"),      // 重启
	)
}

// Prepare 注册服务
func (c *ActionController) Prepare() {
	c.service = service.NewDeviceService()
	c.taskService = service.NewTaskService()
	c.executionReportService = service.NewTaskExecutionReportService()
}

// Cmd 下发车辆控制命令
func (c *ActionController) Cmd() {
	var (
		err       error
		deviceCmd dto.DeviceCmd
	)

	data := c.Ctx.Input.RequestBody
	err = common.Validate(data, &deviceCmd)

	if err != nil {
		c.Fail("参数错误," + err.Error())
		return
	}

	cmd := deviceCmd.Cmd
	logId := int64(0)
	userInfo, _ := c.CheckToken()
	cmdData := map[string]interface{}{}

	var child pix.TaskChild
	var task pix.Task
	var device pix.Device
	imsi := ""
	if cmd == 9 {
		var actionList = make([]dto.TaskContent, 0)
		task, err = c.taskService.Find(deviceCmd.Id)
		if err != nil {
			c.Fail(err.Error())
			return
		}

		imsi = task.Imsi
		// 使用新的执行实例冲突检查方法
		exist := c.taskService.CheckDeviceExecutionConflict(imsi, 0)
		if exist {
			c.Fail("该设备存在将要执行、正在执行或已暂停的任务，无法下发新任务")
			return
		}

		child, err = c.taskService.Child(deviceCmd.Id)
		if err != nil {
			c.Fail("任务下发失败，暂无可下发的子任务")
			return
		}

		err = c.taskService.Cancel(deviceCmd.Id)
		if err != nil {
			c.Fail("任务取消失败：" + err.Error())
			return
		}

		// 获取用户信息并创建执行实例
		userInfo, err := c.CheckToken()
		if err != nil {
			c.Fail("用户身份校验失败")
			return
		}

		// 创建执行实例记录
		err = c.createExecutionReport(&task, &child, &userInfo)
		if err != nil {
			c.Fail("创建执行实例失败：" + err.Error())
			return
		}

		nextChild, err := c.taskService.NextChild(child)

		actionList = append(actionList, dto.TaskContent{
			Action:  child.Action,
			Content: child.Content,
			PST:     int(time.Now().Unix()),
			PET:     0,
		})

		if err == nil {
			actionList = append(actionList, dto.TaskContent{
				Action:  nextChild.Action,
				Content: nextChild.Content,
				PST:     int(time.Now().Unix()),
				PET:     0,
			})
		}

		// 获取执行实例信息
		executionReport, err := c.executionReportService.GetExecutionReport(child.Id, task.Times)
		if err != nil {
			c.Fail("获取执行实例失败：" + err.Error())
			return
		}

		cmdData = map[string]interface{}{
			"imsi":       task.Imsi,
			"cmd":        protocol.CmdMap[9],
			"taskId":     executionReport.Id, // 重构：现在taskId字段实际包含执行实例ID，保持向后兼容
			"routeId":    task.MapId,
			"name":       task.Name,
			"desp":       task.Desp,
			"actionList": actionList,
		}
	} else {
		id := int(deviceCmd.Id)
		device, err = c.service.Find(id)
		if err != nil {
			c.Fail("获取车辆信息失败：" + err.Error())
			return
		}
		imsi = device.Imsi

		cmdData = map[string]interface{}{
			"imsi": device.Imsi,
			"cmd":  protocol.CmdMap[cmd],
		}
	}

	err = c.service.SendMessage(cmdData)
	if err != nil {
		c.Fail("发送消息失败：" + err.Error())
		return
	}

	var condition struct {
		Type int `json:"type"`
		Time int `json:"time"`
	}

	if child.Condition != "" {
		err = json.Unmarshal([]byte(child.Condition), &condition)
		if err != nil {
			c.Fail("json解析失败")
			return
		}

		if condition.Type == 2 {
			go func() {
				time.Sleep(time.Duration(condition.Time) * time.Second)
				cmdData2 := map[string]interface{}{
					"imsi": task.Imsi,
					"cmd":  protocol.CmdMap[16],
				}
				c.service.SendMessage(cmdData2)
				c.taskService.AddLog(device.Imsi, task.Id, child.Id, 2, "执行任务", userInfo.Id, task.Name)
			}()
		}
	}

	// 操作日志
	logType := protocol.LogMap[cmd]
	lastHbJsonStr, err := redis.HGet(common.RedisLastHeartbeat, imsi)
	if err != nil {
		lastHbJsonStr = ""
	}
	var heartData dto.RedisHeart
	if lastHbJsonStr != "" {
		err = json.Unmarshal([]byte(lastHbJsonStr), &heartData)
		if err != nil {
			c.Fail("心跳数据解析失败：" + err.Error())
			return
		}
	}

	logMsg := fmt.Sprintf("终端(%s)%s: Lng:%.6f, Lat:%.6f",
		device.Imsi,
		logType,
		heartData.Lng,
		heartData.Lat,
	)

	logId, err = c.AddOperationLogWithId(userInfo, logType, 1, "", logMsg, imsi)
	if err != nil {
		msg := fmt.Sprintf(AddLogFailed, logType, err)
		c.Fail(msg)
		return
	}

	redisData, _ := json.Marshal(map[string]interface{}{
		"cmd": protocol.CmdMap[cmd],
	})
	// 设置到 Redis 缓存 (120s
	redisKey := fmt.Sprintf(common.RedisOperationPbMsg, imsi, protocol.CmdMap[cmd])
	redis.SetEx(redisKey, 120, redisData)

	info := ""
	flag := 0
	status := 0
	if cmd == 16 || cmd == 1 {
		info = "指令下发：执行"
		flag = 1
		status = 2
	} else if cmd == 17 {
		info = "指令下发：取消"
		flag = 1
		status = 5
	} else if cmd == 18 || cmd == 2 {
		info = "指令下发：停止"
		flag = 1
		status = 6
	} else if cmd == 19 {
		flag = 1
		info = "指令下发：暂停"
		status = 4
	}

	if flag == 1 {
		child, err := c.taskService.FindChild(int64(heartData.LatestTask.TaskId))
		if err != nil {
			c.Fail("获取子任务失败：" + err.Error())
			return
		}

		// 获取任务信息以获取任务名称
		task, err := c.taskService.Find(int64(child.ParentId))
		if err != nil {
			c.Fail("获取任务失败：" + err.Error())
			return
		}

		err = c.taskService.AddLog(imsi, int(child.ParentId), child.Id, status, info, userInfo.Id, task.Name)

		// 重构后：基于执行实例报告更新状态，而不是直接更新task和task_child表的status字段
		// 获取对应的执行实例并更新状态
		executionReport, err := c.executionReportService.GetLatestExecutionByChildId(child.Id)
		if err == nil && executionReport.Status != 3 { // 3 = 已完成
			// 更新执行实例状态
			err = c.executionReportService.UpdateExecutionStatus(executionReport.Id, status)
			if err != nil {
				c.Fail("更新执行状态失败：" + err.Error())
				return
			}
		}
	}

	c.Success(map[string]interface{}{
		"operate": protocol.CmdMap[cmd],
		"msg_id":  logId,
	})
	return
}

// GoPoint 移动到指定位置
func (c *ActionController) GoPoint() {
	var (
		err     error
		goPoint dto.GoPoint
	)

	data := c.Ctx.Input.RequestBody
	err = common.Validate(data, &goPoint)

	if err != nil {
		c.Fail("参数错误," + err.Error())
		return
	}

	id := goPoint.Id
	lat := goPoint.Lat
	lng := goPoint.Lng
	raw := goPoint.Raw

	device, err := c.service.Find(id)
	if err != nil {
		c.Fail("获取车辆信息失败：" + err.Error())
		return
	}

	userInfo, _ := c.CheckToken()
	// if !common.IsShengJiLevel(userInfo.Level) { // 操作员非省级账号,需要判断权限
	//	if (common.IsEditableLevel(userInfo.Level) && (device.Uid != int(userInfo.Id))) || // 本账号是市级管理员,判断是否为本账号所属
	//		(!common.IsEditableLevel(userInfo.Level) && (device.Uid != int(userInfo.ParentID))) { // 本账号是市级值班员,判断是否为本账号所属
	//		c.Fail("权限不足")
	//		return
	//	}
	// }
	// 操作日志
	logType := protocol.LogMap[6]
	lastHbJsonStr, err := redis.HGet(common.RedisLastHeartbeat, device.Imsi)
	if err != nil {
		lastHbJsonStr = ""
	}
	var heartData dto.RedisHeart
	if lastHbJsonStr != "" {
		err = json.Unmarshal([]byte(lastHbJsonStr), &heartData)
		if err != nil {
			c.Fail("心跳数据解析失败：" + err.Error())
			return
		}
	}

	logMsg := fmt.Sprintf("终端(%s)%s: Lng:%.6f, Lat:%.6f",
		device.Imsi,
		logType,
		heartData.Lng,
		heartData.Lat,
	)

	logId, err := c.AddOperationLogWithId(userInfo, logType, 1, "", logMsg, device.Imsi)
	if err != nil {
		msg := fmt.Sprintf(AddLogFailed, logType, err)
		c.Fail(msg)
		return
	}

	cmdData := map[string]interface{}{
		"imsi": device.Imsi,
		"cmd":  protocol.CmdMap[6],
		"lat":  lat,
		"lng":  lng,
		"raw":  raw,
	}

	fmt.Println("Task Received, GoPoint", cmdData)

	err = c.service.SendMessage(cmdData)
	if err != nil {
		c.Fail("发送消息失败：" + err.Error())
		return
	}

	redisData, _ := json.Marshal(map[string]interface{}{
		"cmd": protocol.CmdMap[6],
	})
	// 设置到 Redis 缓存 (120s
	redisKey := fmt.Sprintf(common.RedisOperationPbMsg, device.Imsi, protocol.CmdMap[6])
	redis.SetEx(redisKey, 120, redisData)

	c.Success(map[string]interface{}{
		"operate": protocol.CmdMap[6],
		"msg_id":  logId,
	})
	return
}

// SetSpeed 设置最高限速
func (c *ActionController) SetSpeed() {
	var (
		err      error
		setSpeed dto.SetSpeed
	)

	data := c.Ctx.Input.RequestBody
	err = common.Validate(data, &setSpeed)

	if err != nil {
		c.Fail("参数错误," + err.Error())
		return
	}

	userInfo, _ := c.CheckToken()

	logType := protocol.LogMap[22]
	lastHbJsonStr, err := redis.HGet(common.RedisLastHeartbeat, setSpeed.Imsi)
	if err != nil {
		lastHbJsonStr = ""
	}
	var heartData dto.RedisHeart
	if lastHbJsonStr != "" {
		err = json.Unmarshal([]byte(lastHbJsonStr), &heartData)
		if err != nil {
			c.Fail("心跳数据解析失败：" + err.Error())
			return
		}
	}

	logMsg := fmt.Sprintf("终端(%s)%s: Lng:%.6f, Lat:%.6f",
		setSpeed.Imsi,
		logType,
		heartData.Lng,
		heartData.Lat,
	)

	logId, err := c.AddOperationLogWithId(userInfo, logType, 1, "", logMsg, setSpeed.Imsi)
	if err != nil {
		msg := fmt.Sprintf(AddLogFailed, logType, err)
		c.Fail(msg)
		return
	}

	cmdData := map[string]interface{}{
		"imsi":  setSpeed.Imsi,
		"cmd":   protocol.CmdMap[22],
		"speed": setSpeed.Speed,
	}

	fmt.Println("Task Received, SetSpeed", cmdData)

	err = c.service.SendMessage(cmdData)
	if err != nil {
		c.Fail("发送消息失败：" + err.Error())
		return
	}

	redisData, _ := json.Marshal(map[string]interface{}{
		"cmd": protocol.CmdMap[6],
	})
	// 设置到 Redis 缓存 (120s
	redisKey := fmt.Sprintf(common.RedisOperationPbMsg, setSpeed.Imsi, protocol.CmdMap[6])
	redis.SetEx(redisKey, 120, redisData)

	c.Success(map[string]interface{}{
		"operate": protocol.CmdMap[22],
		"msg_id":  logId,
	})
	return
}

// Task 下发清扫路径任务
func (c *ActionController) Task() {
	var (
		err  error
		task dto.Task
	)

	data := c.Ctx.Input.RequestBody
	err = common.Validate(data, &task)

	if err != nil {
		c.Fail("参数错误," + err.Error())
		return
	}

	id := task.Id
	name := task.Name
	routeId := task.RouteId
	desp := task.Desp
	actionList := task.ActionList

	device, err := c.service.Find(id)
	if err != nil {
		c.Fail("获取车辆信息失败：" + err.Error())
		return
	}

	userInfo, _ := c.CheckToken()
	// if !common.IsShengJiLevel(userInfo.Level) { // 操作员非省级账号,需要判断权限
	//	if (common.IsEditableLevel(userInfo.Level) && (device.Uid != int(userInfo.Id))) || // 本账号是市级管理员,判断是否为本账号所属
	//		(!common.IsEditableLevel(userInfo.Level) && (device.Uid != int(userInfo.ParentID))) { // 本账号是市级值班员,判断是否为本账号所属
	//		c.Fail("权限不足")
	//		return
	//	}
	// }

	// 操作日志
	logType := protocol.LogMap[9]
	lastHbJsonStr, err := redis.HGet(common.RedisLastHeartbeat, device.Imsi)
	if err != nil {
		lastHbJsonStr = ""
	}
	var heartData dto.RedisHeart
	if lastHbJsonStr != "" {
		err = json.Unmarshal([]byte(lastHbJsonStr), &heartData)
		if err != nil {
			c.Fail("心跳数据解析失败：" + err.Error())
			return
		}
	}

	logMsg := fmt.Sprintf("终端(%s)%s: Lng:%.6f, Lat:%.6f",
		device.Imsi,
		logType,
		heartData.Lng,
		heartData.Lat,
	)

	logId, err := c.AddOperationLogWithId(userInfo, logType, 1, "", logMsg, device.Imsi)
	if err != nil {
		msg := fmt.Sprintf(AddLogFailed, logType, err)
		c.Fail(msg)
		return
	}

	cmdData := map[string]interface{}{
		"imsi":       device.Imsi,
		"cmd":        protocol.CmdMap[9],
		"routeId":    routeId,
		"name":       name,
		"desp":       desp,
		"actionList": actionList,
	}

	err = c.service.SendMessage(cmdData)
	if err != nil {
		c.Fail("发送消息失败：" + err.Error())
		return
	}

	redisData, _ := json.Marshal(map[string]interface{}{
		"cmd": protocol.CmdMap[9],
	})
	// 设置到 Redis 缓存 (120s
	redisKey := fmt.Sprintf(common.RedisOperationPbMsg, device.Imsi, protocol.CmdMap[9])
	redis.SetEx(redisKey, 120, redisData)

	c.Success(map[string]interface{}{
		"operate": protocol.CmdMap[9],
		"msg_id":  logId,
	})
	return
}

// Reboot 下发车辆控制命令
func (c *ActionController) Reboot() {
	var (
		err       error
		deviceCmd dto.DeviceCmds
		userInfo  dto.UserInfo
	)

	data := c.Ctx.Input.RequestBody
	err = common.Validate(data, &deviceCmd)

	if err != nil {
		c.Fail("参数错误," + err.Error())
		return
	}

	id := deviceCmd.Id
	cmd := 15
	devices, err := c.service.FindAll(id)
	if err != nil {
		c.Fail("获取车辆信息失败：" + err.Error())
		return
	}

	// if !common.IsShengJiLevel(userInfo.Level) { // 操作员非省级账号,需要判断权限
	//	if (common.IsEditableLevel(userInfo.Level) && (device.Uid != int(userInfo.Id))) || // 本账号是市级管理员,判断是否为本账号所属
	//		(!common.IsEditableLevel(userInfo.Level) && (device.Uid != int(userInfo.ParentID))) { // 本账号是市级值班员,判断是否为本账号所属
	//		c.Fail("权限不足")
	//		return
	//	}
	// }

	// 操作日志

	for _, device := range devices {
		logType := protocol.LogMap[cmd]
		lastHbJsonStr, err := redis.HGet(common.RedisLastHeartbeat, device.Imsi)
		if err != nil {
			lastHbJsonStr = ""
		}
		var heartData dto.RedisHeart
		if lastHbJsonStr != "" {
			err = json.Unmarshal([]byte(lastHbJsonStr), &heartData)
			if err != nil {
				c.Fail("心跳数据解析失败：" + err.Error())
				return
			}
		}

		logMsg := fmt.Sprintf("终端(%s)%s: Lng:%.6f, Lat:%.6f",
			device.Imsi,
			logType,
			heartData.Lng,
			heartData.Lat,
		)

		_, err = c.AddOperationLogWithId(userInfo, logType, 1, "", logMsg, device.Imsi)
		if err != nil {
			msg := fmt.Sprintf(AddLogFailed, logType, err)
			c.Fail(msg)
			return
		}

		cmdData := map[string]interface{}{
			"imsi": device.Imsi,
			"cmd":  protocol.CmdMap[cmd],
		}

		err = c.service.SendMessage(cmdData)
		if err != nil {
			c.Fail("发送消息失败：" + err.Error())
			return
		}

		redisData, _ := json.Marshal(map[string]interface{}{
			"cmd": protocol.CmdMap[cmd],
		})
		// 设置到 Redis 缓存 (120s
		redisKey := fmt.Sprintf(common.RedisOperationPbMsg, device.Imsi, protocol.CmdMap[cmd])
		redis.SetEx(redisKey, 120, redisData)

	}

	c.Success(map[string]interface{}{
		"operate": protocol.CmdMap[cmd],
	})
	return
}

// SetMap 设置车辆自动驾驶地图
func (c *ActionController) SetMap() {
	var (
		err      error
		setMap   dto.SetMap
		userInfo dto.UserInfo
	)

	data := c.Ctx.Input.RequestBody
	err = common.Validate(data, &setMap)

	if err != nil {
		c.Fail("参数错误," + err.Error())
		return
	}

	userInfo, _ = c.CheckToken()
	logType := protocol.LogMap[25]
	lastHbJsonStr, err := redis.HGet(common.RedisLastHeartbeat, setMap.Imsi)
	if err != nil {
		lastHbJsonStr = ""
	}
	var heartData dto.RedisHeart
	if lastHbJsonStr != "" {
		err = json.Unmarshal([]byte(lastHbJsonStr), &heartData)
		if err != nil {
			c.Fail("心跳数据解析失败：" + err.Error())
			return
		}
	}

	logMsg := fmt.Sprintf("终端(%s)%s: Lng:%.6f, Lat:%.6f",
		setMap.Imsi,
		logType,
		heartData.Lng,
		heartData.Lat,
	)

	logId, err := c.AddOperationLogWithId(userInfo, logType, 1, "", logMsg, setMap.Imsi)
	if err != nil {
		msg := fmt.Sprintf(AddLogFailed, logType, err)
		c.Fail(msg)
		return
	}

	cmdData := map[string]interface{}{
		"imsi": setMap.Imsi,
		"cmd":  protocol.CmdMap[25],
		"map": map[string]interface{}{
			"name": setMap.Name,
		},
	}

	err = c.service.SendMessage(cmdData)
	if err != nil {
		c.Fail("发送消息失败：" + err.Error())
		return
	}

	redisData, _ := json.Marshal(map[string]interface{}{
		"cmd": protocol.CmdMap[25],
	})
	// 设置到 Redis 缓存 (120s
	redisKey := fmt.Sprintf(common.RedisOperationPbMsg, setMap.Imsi, protocol.CmdMap[25])
	redis.SetEx(redisKey, 120, redisData)
	c.Success(map[string]interface{}{
		"operate": protocol.CmdMap[9],
		"msg_id":  logId,
	})
	return
}

// createExecutionReport 创建任务执行实例记录
func (c *ActionController) createExecutionReport(task *pix.Task, child *pix.TaskChild, userInfo *dto.UserInfo) error {
	// 获取设备信息 - 通过All方法查询单个设备
	devices, err := c.service.All(0, task.Imsi, -1, "", false, -1)
	var device pix.Device
	if err != nil || len(devices) == 0 {
		// 如果获取设备失败，使用默认值
		device = pix.Device{
			Type: 0, // 默认类型
		}
	} else {
		device = devices[0]
	}

	// 🆕 自动计算execution_no（统一处理逻辑）
	o := orm.NewOrm()
	var maxExecutionNo int
	err = o.Raw("SELECT COALESCE(MAX(execution_no), -1) FROM task_execution_report WHERE child_id = ?", child.Id).QueryRow(&maxExecutionNo)
	if err != nil {
		return fmt.Errorf("查询最大execution_no失败: %v", err)
	}
	executionNo := maxExecutionNo + 1

	// 创建执行实例记录
	executionReport := &pix.TaskExecutionReport{
		TaskId:       int64(task.Id),
		ChildId:      child.Id,
		ExecutionNo:  executionNo, // 🆕 使用计算出的execution_no
		Imsi:         task.Imsi,
		RunningTask:  fmt.Sprintf("%d-%d", child.Id, executionNo), // 🆕 使用计算出的execution_no
		Status:       1,                                           // 1 = 将要执行
		VehiclePlate: task.Imsi,
		VehicleType:  pix.GetVehicleType(device.Type),
		TaskType:     child.Type,
		VehicleUsage: pix.GetVehicleUsage(device.Type),
		Creator:      userInfo.Username,
		CreatedTime:  time.Now().Unix(),
	}

	// 解析任务内容，填充地点信息
	err = c.executionReportService.ParseTaskContent(executionReport, child.Content, task.MapId)
	if err != nil {
		return fmt.Errorf("解析任务内容失败: %v", err)
	}

	// 保存到数据库
	err = c.executionReportService.CreateExecutionReport(executionReport)
	if err != nil {
		return fmt.Errorf("保存执行实例失败: %v", err)
	}

	return nil
}
