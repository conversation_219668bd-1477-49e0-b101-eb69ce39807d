package controllers

import (
	"ccapi/common"
	"ccapi/models/dto"
	"ccapi/service"
	"fmt"
	beego "github.com/beego/beego/v2/server/web"
	"strings"
	"time"
)

// AdvertController 广告模块
type AdvertController struct {
	BaseController
	service     service.AdvertServiceInter
	userService service.UserServiceInter
}

// InitAdvertRouter 初始化路由
func InitAdvertRouter() beego.LinkNamespace {
	return beego.NSNamespace("/advert",
		beego.NSRouter("/list", &AdvertController{}, "get:List"),             // 获取广告列表
		beego.NSRouter("/set_list", &AdvertController{}, "get:SetList"),      // 获取广告配置列表
		beego.NSRouter("/add", &AdvertController{}, "post:Add"),              // 新增广告
		beego.NSRouter("/add_set", &AdvertController{}, "post:AddSet"),       // 新增广告配置
		beego.NSRouter("/delete", &AdvertController{}, "post:Delete"),        // 删除广告
		beego.NSRouter("/delete_set", &AdvertController{}, "post:DeleteSet"), // 删除广告配置
		beego.NSRouter("/update_set", &AdvertController{}, "post:UpdateSet"), // 修改广告配置
		beego.NSRouter("/get_device", &AdvertController{}, "get:GetDevice"),  // 获取该广告配置的设备和可选设备
	)
}

// Prepare 注册服务
func (c *AdvertController) Prepare() {
	c.service = service.NewAdvertService()
	c.userService = service.NewUserService()
}

// List 获取广告列表
func (c *AdvertController) List() {
	name := c.GetString("name", "")
	_type := c.GetString("type", "")
	page, err := c.GetInt("page", 1)
	limit, err := c.GetInt("limit", 10)

	if err != nil {
		c.Fail("参数错误" + err.Error())
		return
	}

	userInfo, _ := c.CheckToken()

	userList, err := c.userService.Child(userInfo, "")

	var idsStr []string
	for _, v := range userList {
		idsStr = append(idsStr, fmt.Sprintf("%d", v.Id))
	}

	allId := strings.Join(idsStr, ",")

	data, count, err := c.service.List(page, limit, name, _type, allId)

	if err != nil {
		c.Fail("获取列表失败：" + err.Error())
		return
	}

	c.Success(map[string]interface{}{
		"items": data,
		"total": count,
	})
}

// Add 新增广告
func (c *AdvertController) Add() {
	var (
		err error
	)

	describe := c.GetString("describe")
	if err != nil {
		c.Fail("参数错误," + err.Error())
		return
	}

	file, header, err := c.GetFile("file")
	filename := header.Filename
	position := strings.LastIndex(filename, ".")
	_type := filename[position+1:]

	endpoint, _ := beego.AppConfig.String("endpoint")
	accessKeyId, _ := beego.AppConfig.String("accessKeyId")
	accessKeySecret, _ := beego.AppConfig.String("accessKeySecret")
	bucketName, _ := beego.AppConfig.String("bucketName")

	ossFilename := fmt.Sprintf("%s%d", common.RandomString("alpha", 5), time.Now().Unix()) + filename[position:]
	err = c.UploadOss(endpoint, accessKeyId, accessKeySecret, bucketName, ossFilename, file)

	if err != nil {
		c.Fail("上传到oss失败:" + err.Error())
		return
	}

	s, err := beego.AppConfig.String("ossUrl")
	ossUrl := s + ossFilename

	userInfo, _ := c.CheckToken()
	err = c.service.Add(ossUrl, filename, _type, describe, userInfo.Id)
	if err != nil {
		c.Fail("新增失败" + err.Error())
		return
	}
	c.Success(nil)
	return
}

// Delete 删除广告
func (c *AdvertController) Delete() {
	var (
		err    error
		postId dto.PostId
	)

	data := c.Ctx.Input.RequestBody
	err = common.Validate(data, &postId)

	if err != nil {
		c.Fail("参数错误," + err.Error())
		return
	}

	id := postId.Id

	err = c.service.Delete(id)
	if err != nil {
		c.Fail("删除失败：" + err.Error())
		return
	}

	c.Success(nil)
}

// AddSet 新增广告配置
func (c *AdvertController) AddSet() {
	var (
		err error
		set dto.AddAdvertSet
	)

	data := c.Ctx.Input.RequestBody
	err = common.Validate(data, &set)

	if err != nil {
		c.Fail("参数错误," + err.Error())
		return
	}

	name := set.Name
	_type := set.Type
	content := set.Content
	deviceId := set.DeviceId
	uid := set.Uid

	exist := c.service.CheckDevice(0, deviceId)
	if exist {
		c.Fail("存在已有广告配置的设备")
		return
	}

	err = c.service.AddSet(name, _type, content, deviceId, uid)
	if err != nil {
		c.Fail("新增失败:" + err.Error())
		return
	}

	c.Success(nil)
	return
}

// SetList 获取广告配置列表
func (c *AdvertController) SetList() {
	name := c.GetString("name", "")
	_type, err := c.GetInt("type", -1)
	page, err := c.GetInt("page", 1)
	limit, err := c.GetInt("limit", 10)

	if err != nil {
		c.Fail("参数错误" + err.Error())
		return
	}

	userInfo, _ := c.CheckToken()

	userList, err := c.userService.Child(userInfo, "")

	var idsStr []string
	for _, v := range userList {
		idsStr = append(idsStr, fmt.Sprintf("%d", v.Id))
	}

	allId := strings.Join(idsStr, ",")

	data, count, err := c.service.SetList(page, limit, name, _type, allId)

	if err != nil {
		c.Fail("获取列表失败：" + err.Error())
		return
	}

	c.Success(map[string]interface{}{
		"items": data,
		"total": count,
	})
}

// DeleteSet 删除广告配置
func (c *AdvertController) DeleteSet() {
	var (
		err    error
		postId dto.PostId
	)

	data := c.Ctx.Input.RequestBody
	err = common.Validate(data, &postId)

	if err != nil {
		c.Fail("参数错误," + err.Error())
		return
	}

	id := postId.Id

	err = c.service.DeleteSet(id)
	if err != nil {
		c.Fail("删除失败：" + err.Error())
		return
	}

	c.Success(nil)
}

// UpdateSet 修改广告配置
func (c *AdvertController) UpdateSet() {
	var (
		err       error
		updateSet dto.UpdateAdvertSet
	)

	data := c.Ctx.Input.RequestBody
	err = common.Validate(data, &updateSet)

	if err != nil {
		c.Fail("参数错误," + err.Error())
		return
	}

	id := updateSet.Id
	name := updateSet.Name
	_type := updateSet.Type
	deviceId := updateSet.DeviceId
	content := updateSet.Content
	uid := updateSet.Uid

	exist := c.service.CheckDevice(id, deviceId)
	if exist {
		c.Fail("存在已有任务的设备")
		return
	}

	err = c.service.UpdateSet(id, name, _type, content, deviceId, uid)
	if err != nil {
		c.Fail("修改失败:" + err.Error())
		return
	}

	c.Success(nil)
	return
}

// GetDevice 获取该广告配置的设备和可选设备
func (c *AdvertController) GetDevice() {
	id, err := c.GetInt64("id", 0)
	uid, err := c.GetInt("uid", 0)
	if err != nil {
		c.Fail("参数错误" + err.Error())
		return
	}

	device, allDevice, err := c.service.GetDevice(id, uid)
	if err != nil {
		c.Fail("获取失败")
		return
	}

	c.Success(map[string]interface{}{
		"device":     device,
		"all_device": allDevice,
	})
}
