package controllers

import (
	"ccapi/common"
	"ccapi/common/protocol"
	"ccapi/models/dto"
	"ccapi/models/pix"
	"ccapi/server/log"
	"ccapi/server/mqtt"
	"ccapi/service"
	"encoding/json"
	"errors"
	"fmt"
	"strings"
	"time"

	"github.com/beego/beego/v2/client/orm"
	"github.com/beego/beego/v2/core/validation"
	beego "github.com/beego/beego/v2/server/web"
)

// AlarmController 警报呼叫模块
type AlarmController struct {
	BaseController
	service      service.AlarmServiceInter
	userServer   service.UserServiceInter
	deviceServer service.DeviceServiceInter
}

// InitAlarmRouter 初始化路由
func InitAlarmRouter() beego.LinkNamespace {
	return beego.NSNamespace("/alarm",
		beego.NSRouter("/list", &AlarmController{}, "get:List"),                          // 获取警报呼叫列表
		beego.NSRouter("/delete", &AlarmController{}, "post:Delete"),                     // 删除警报呼叫
		beego.NSRouter("/add_config", &AlarmController{}, "post:AddConfig"),              // 新增配置
		beego.NSRouter("/add_configs", &AlarmController{}, "post:AddConfigs"),            // 批量新增配置
		beego.NSRouter("/edit_config", &AlarmController{}, "post:EditConfig"),            // 修改配置
		beego.NSRouter("/delete_config", &AlarmController{}, "post:DeleteConfig"),        // 删除配置
		beego.NSRouter("/delete_alarm", &AlarmController{}, "post:DeleteAlarm"),          // 删除告警记录
		beego.NSRouter("/config_list", &AlarmController{}, "get:ConfigList"),             // 配置列表
		beego.NSRouter("/alarm_device", &AlarmController{}, "get:AlarmDevice"),           // 告警记录
		beego.NSRouter("/select_alarm", &AlarmController{}, "get:SelectAlarm"),           // 查询实时告警
		beego.NSRouter("/save_alarm_lock", &AlarmController{}, "post:SaveAlarmLock"),     // 保存告警锁定
		beego.NSRouter("/delete_alarm_lock", &AlarmController{}, "post:DeleteAlarmLock"), // 删除告警锁定
		beego.NSRouter("/list_alarm_lock", &AlarmController{}, "get:ListAlarmLock"),      // 查询告警锁定
	)
}

// Prepare 注册服务
func (c *AlarmController) Prepare() {
	c.service = service.NewAlarmService()
	c.userServer = service.NewUserService()
	c.deviceServer = service.NewDeviceService()
}

// List 获取权限列表
func (c *AlarmController) List() {
	imsi := c.GetString("imsi", "")
	userId, err := c.GetInt("user_id", -1)
	name := c.GetString("name", "")
	level, err := c.GetInt("level", -1)
	status, err := c.GetInt("status", -1)
	limit, err := c.GetInt("limit", 10)
	page, err := c.GetInt("page", 1)

	if err != nil {
		c.Fail("参数错误：" + err.Error())
		return
	}

	userInfo, err := c.CheckToken()
	userList, err := c.userServer.Child(userInfo, "")

	var idsStr []string
	for _, v := range userList {
		idsStr = append(idsStr, fmt.Sprintf("%d", v.Id))
	}

	allId := strings.Join(idsStr, ",")

	if err != nil {
		c.Fail("获取用户信息失败：" + err.Error())
		return
	}

	data, count, err := c.service.List(imsi, userId, name, level, status, allId, limit, page)
	if err != nil {
		c.Fail("获取失败：" + err.Error())
		return
	}
	c.Success(map[string]interface{}{
		"data":  data,
		"total": count,
	})
	return
}

// Delete 删除消息
func (c *AlarmController) Delete() {
	var (
		err error
		ids dto.Ids
	)

	data := c.Ctx.Input.RequestBody
	err = common.Validate(data, &ids)

	if err != nil {
		c.Fail("参数错误," + err.Error())
		return
	}

	id := ids.Id
	err = c.service.Delete(id)
	if err != nil {
		c.Fail("删除失败：" + err.Error())
		return
	}
	c.Success(nil)
	return
}

// AddConfig 新增告警码配置
func (c *AlarmController) AddConfig() {
	var (
		err       error
		addConfig dto.AddConfig
	)

	err = json.Unmarshal(c.Ctx.Input.RequestBody, &addConfig)

	if err != nil {
		c.Fail("参数错误," + err.Error())
		return
	}

	code := addConfig.Code
	level := uint8(0)
	_type := addConfig.Type
	firstDomain := addConfig.FirstDomain
	secondDomain := addConfig.SecondDomain
	name := addConfig.Name
	handCancel := addConfig.HandCancel
	autoCancel := addConfig.AutoCancel
	ignore := addConfig.Ignore
	injection := addConfig.Injection
	originalCode := addConfig.OriginalCode
	msg := addConfig.Msg
	proposal := addConfig.Proposal
	method := addConfig.Method

	exist := c.service.IfExist(code)
	if exist {
		c.Fail("该告警码已存在")
		return
	}

	err = c.service.AddConfig(code, level, _type, firstDomain, secondDomain, name, handCancel, autoCancel, ignore,
		injection, originalCode, msg, proposal, method)

	if err != nil {
		c.Fail("新增失败：" + err.Error())
		return
	}

	c.Success(nil)
	return
}

// AddConfigs 批量新增告警码配置
func (c *AlarmController) AddConfigs() {
	var (
		addConfigs dto.AddConfigs
	)

	valid := validation.Validation{}
	var message = map[string]string{
		"Required": "不能为空",
	}
	validation.SetDefaultMessage(message)

	json.Unmarshal(c.Ctx.Input.RequestBody, &addConfigs)

	i := 1
	for _, addConfig := range addConfigs {
		valid.Required(addConfig.Code, "Code").Message("'告警码值'不能为空或0")
		valid.Required(addConfig.Msg, "Msg").Message("'告警描述内容'不能为空")
		valid.Required(addConfig.Name, "Name").Message("'告警名称'不能为空")
		valid.Required(addConfig.FirstDomain, "FirstDomain").Message("'告警一级域'不能为空")
		valid.Required(addConfig.SecondDomain, "SecondDomain").Message("'告警二级域'不能为空")
		valid.Required(addConfig.OriginalCode, "OriginalCode").Message("'原始故障代码'不能为空")
		valid.Required(addConfig.Proposal, "Proposal").Message("'处理建议'不能为空")
		valid.Required(addConfig.Method, "Method").Message("'构造方法'不能为空")
		if valid.HasErrors() {
			c.Fail(fmt.Sprintf("第%d行数据错误，%s", i, valid.Errors))
			return
		}

		if !common.InArray(addConfig.FirstDomain, []string{"车身", "自动驾驶", "远程驾驶"}) {
			c.Fail(fmt.Sprintf("第%d行数据错误，%s", i, "告警一级域不存在"))
			return
		}

		if addConfig.FirstDomain == "车身" && !common.InArray(addConfig.SecondDomain, []string{"VCU", "MCU", "BCM", "EHB",
			"EPS", "BMS", "RCU", "AC", "BCU", "DCDC", "OBC", "数字电源", "门控制器", "低速提示器", "长条控车屏"}) {
			c.Fail(fmt.Sprintf("第%d行数据错误，%s", i, "告警二级域与告警一级域不匹配"))
			return
		}

		if addConfig.FirstDomain == "自动驾驶" && !common.InArray(addConfig.SecondDomain, []string{"数据采集", "感知模块",
			"定位模块", "规划模块", "控制模块", "地图模块"}) {
			c.Fail(fmt.Sprintf("第%d行数据错误，%s", i, "告警二级域与告警一级域不匹配"))
			return
		}

		if addConfig.FirstDomain == "远程驾驶" && !common.InArray(addConfig.SecondDomain, []string{"车端", "遥控座舱"}) {
			c.Fail(fmt.Sprintf("第%d行数据错误，%s", i, "告警二级域与告警一级域不匹配"))
			return
		}

		code := addConfig.Code
		level := uint8(0)
		_type := addConfig.Type
		firstDomain := addConfig.FirstDomain
		secondDomain := addConfig.SecondDomain
		name := addConfig.Name
		handCancel := addConfig.HandCancel
		autoCancel := addConfig.AutoCancel
		ignore := addConfig.Ignore
		injection := addConfig.Injection
		originalCode := addConfig.OriginalCode
		msg := addConfig.Msg
		proposal := addConfig.Proposal
		method := addConfig.Method

		err := c.service.AddConfig(code, level, _type, firstDomain, secondDomain, name, handCancel, autoCancel, ignore,
			injection, originalCode, msg, proposal, method)

		if err != nil {
			c.Fail("新增失败：" + err.Error())
			return
		}

		i++
	}

	c.Success(nil)
	return
}

// EditConfig 修改告警码配置
func (c *AlarmController) EditConfig() {
	var (
		err        error
		editConfig dto.EditConfig
	)

	data := c.Ctx.Input.RequestBody
	err = common.Validate(data, &editConfig)

	if err != nil {
		c.Fail("参数错误," + err.Error())
		return
	}

	id := editConfig.Id
	code := editConfig.Code
	level := editConfig.Level
	_type := editConfig.Type
	firstDomain := editConfig.FirstDomain
	secondDomain := editConfig.SecondDomain
	name := editConfig.Name
	handCancel := editConfig.HandCancel
	autoCancel := editConfig.AutoCancel
	ignore := editConfig.Ignore
	injection := editConfig.Injection
	originalCode := editConfig.OriginalCode
	msg := editConfig.Msg
	proposal := editConfig.Proposal
	method := editConfig.Method

	err = c.service.EditConfig(id, code, level, _type, firstDomain, secondDomain, name, handCancel, autoCancel, ignore,
		injection, originalCode, msg, proposal, method)

	if err != nil {
		c.Fail("修改失败：" + err.Error())
		return
	}

	c.Success(nil)
	return
}

// DeleteConfig 删除配置
func (c *AlarmController) DeleteConfig() {
	var (
		err error
		ids dto.Ids
	)

	data := c.Ctx.Input.RequestBody
	err = common.Validate(data, &ids)

	if err != nil {
		c.Fail("参数错误," + err.Error())
		return
	}

	id := ids.Id
	err = c.service.DeleteConfig(id)
	if err != nil {
		c.Fail("删除失败：" + err.Error())
		return
	}
	c.Success(nil)
	return
}

// ConfigList 配置列表
func (c *AlarmController) ConfigList() {
	code := c.GetString("code")
	name := c.GetString("name")
	firstDomain := c.GetString("first_domain")
	secondDomain := c.GetString("second_domain")
	level, err := c.GetInt8("level", -1)
	_type, err := c.GetInt8("type", -1)
	createdBegin, err := c.GetInt64("created_begin", 0)
	createdEnd, err := c.GetInt64("created_end", 0)
	updatedBegin, err := c.GetInt64("updated_begin", 0)
	updatedEnd, err := c.GetInt64("updated_end", 0)
	limit, err := c.GetInt("limit", 10)
	page, err := c.GetInt("page", 1)

	if err != nil {
		c.Fail("参数错误：" + err.Error())
		return
	}

	data, count, err := c.service.ConfigList(code, name, firstDomain, secondDomain, level, _type, createdBegin, createdEnd,
		updatedBegin, updatedEnd, limit, page)
	if err != nil {
		c.Fail("获取失败：" + err.Error())
		return
	}
	c.Success(map[string]interface{}{
		"data":  data,
		"total": count,
	})
	return
}

// AlarmDevice 告警记录
func (c *AlarmController) AlarmDevice() {
	code := c.GetString("code")
	level, err := c.GetInt8("level", -1)
	_type, err := c.GetInt8("type", -1)
	firstDomain := c.GetString("first_domain")
	secondDomain := c.GetString("second_domain")
	begin, err := c.GetInt64("begin", 0)
	end, err := c.GetInt64("end", 0)
	uid, err := c.GetInt64("uid", 0)
	imsi := c.GetString("imsi")
	limit, err := c.GetInt("limit", 10)
	page, err := c.GetInt("page", 1)

	if err != nil {
		c.Fail("参数错误：" + err.Error())
		return
	}

	data, count, err := c.service.AlarmDevice(code, level, _type, firstDomain, secondDomain, begin, end, uid, imsi, limit, page)
	if err != nil {
		c.Fail("获取失败：" + err.Error())
		return
	}
	c.Success(map[string]interface{}{
		"data":  data,
		"total": count,
	})
	return
}

// DeleteAlarm 删除告警记录
func (c *AlarmController) DeleteAlarm() {
	var (
		err error
		ids dto.Ids
	)

	data := c.Ctx.Input.RequestBody
	err = common.Validate(data, &ids)

	if err != nil {
		c.Fail("参数错误," + err.Error())
		return
	}

	id := ids.Id
	err = c.service.DeleteAlarm(id)
	if err != nil {
		c.Fail("删除失败：" + err.Error())
		return
	}
	c.Success(nil)
	return
}

// SelectAlarm 查询实时告警
func (c *AlarmController) SelectAlarm() {
	imsi := c.GetString("imsi")

	cmdData := map[string]interface{}{
		"imsi":  imsi,
		"cmd":   protocol.CmdMap[29],
		"level": []int{0, 1, 2, 3},
	}

	err := c.deviceServer.SendMessage(cmdData)
	if err != nil {
		c.Fail("发送失败：" + err.Error())
		return
	}

	c.Success(nil)
}

// SaveAlarmLock 保存告警锁定
//
// 请求：POST /api/alarm/save_alarm_lock
// 请求体：
//
//	{
//	  "imsi": "设备IMSI号",
//	  "alarmCode": "告警代码",
//	  "userId": 123 // 可选,不传则为当前登录用户,传值时需要确保当前用户有权限为该用户配置
//	}
func (c *AlarmController) SaveAlarmLock() {
	// 获取当前用户ID
	userInfo, err := c.CheckToken()
	if err != nil {
		c.Fail("获取用户信息失败：" + err.Error())
		return
	}

	// 解析请求参数
	var data struct {
		Imsi      string `json:"imsi"`
		AlarmCode string `json:"alarmCode"`
		UserId    int    `json:"userId,omitempty"`
	}
	if err := json.Unmarshal(c.Ctx.Input.RequestBody, &data); err != nil {
		c.Fail("参数解析失败：" + err.Error())
		return
	}

	// 参数验证
	if data.Imsi == "" || data.AlarmCode == "" {
		c.Fail("IMSI和告警代码不能为空")
		return
	}

	// 确定目标用户ID
	targetUserId := int(userInfo.Id)
	if data.UserId > 0 {
		// 如果指定了userId,需要验证权限
		userList, err := c.userServer.Child(userInfo, "")
		if err != nil {
			c.Fail("获取用户列表失败：" + err.Error())
			return
		}

		hasPermission := false
		for _, user := range userList {
			if user.Id == int64(data.UserId) {
				hasPermission = true
				targetUserId = data.UserId
				break
			}
		}

		if !hasPermission {
			c.Fail("没有权限为该用户配置告警锁定")
			return
		}
	}

	// 创建或更新记录
	o := orm.NewOrm()
	now := time.Now()

	// 先尝试查询是否存在
	existingLock := &pix.UserAlarmLock{}
	err = o.QueryTable(new(pix.UserAlarmLock)).
		Filter("user_id", targetUserId).
		Filter("imsi", data.Imsi).
		Filter("alarm_code", data.AlarmCode).
		One(existingLock)

	if errors.Is(err, orm.ErrNoRows) {
		// 不存在则创建新记录
		lock := &pix.UserAlarmLock{
			UserId:      targetUserId,
			Imsi:        data.Imsi,
			AlarmCode:   data.AlarmCode,
			CreatedTime: now,
			UpdatedTime: now,
		}
		_, err = o.Insert(lock)
	} else if err == nil {
		// 存在则更新时间
		existingLock.UpdatedTime = now
		_, err = o.Update(existingLock, "updated_time")
	}

	if err != nil {
		c.Fail("保存失败：" + err.Error())
		return
	}

	// 发送 MQTT 消息
	msg := map[string]interface{}{
		"opr":       "lock",
		"imsi":      data.Imsi,
		"alarmCode": data.AlarmCode,
	}
	msgBytes, _ := json.Marshal(msg)
	if err := mqtt.PublishMessage("pixmoving/analysis/heartbeat", msgBytes); err != nil {
		log.Error("发送 MQTT 消息失败：" + err.Error())
	}

	c.Success(nil)
}

// DeleteAlarmLock 删除告警锁定
//
// 请求：POST /api/alarm/delete_alarm_lock
// 请求体：
//
//	{
//	  "imsi": "设备IMSI号",
//	  "alarmCode": "告警代码",
//	  "userId": 123 // 可选,不传则为当前登录用户,传值时需要确保当前用户有权限为该用户配置
//	}
func (c *AlarmController) DeleteAlarmLock() {
	// 获取当前用户ID
	userInfo, err := c.CheckToken()
	if err != nil {
		c.Fail("获取用户信息失败：" + err.Error())
		return
	}

	// 解析请求参数
	var data struct {
		Imsi      string `json:"imsi"`
		AlarmCode string `json:"alarmCode"`
		UserId    int    `json:"userId,omitempty"`
	}
	if err := json.Unmarshal(c.Ctx.Input.RequestBody, &data); err != nil {
		c.Fail("参数解析失败：" + err.Error())
		return
	}

	// 参数验证
	if data.Imsi == "" || data.AlarmCode == "" {
		c.Fail("IMSI和告警代码不能为空")
		return
	}

	// 确定目标用户ID
	targetUserId := int(userInfo.Id)
	if data.UserId > 0 {
		// 如果指定了userId,需要验证权限
		userList, err := c.userServer.Child(userInfo, "")
		if err != nil {
			c.Fail("获取用户列表失败：" + err.Error())
			return
		}

		hasPermission := false
		for _, user := range userList {
			if user.Id == int64(data.UserId) {
				hasPermission = true
				targetUserId = data.UserId
				break
			}
		}

		if !hasPermission {
			c.Fail("没有权限为该用户配置告警锁定")
			return
		}
	}

	// 删除记录
	o := orm.NewOrm()
	_, err = o.QueryTable(new(pix.UserAlarmLock)).
		Filter("user_id", targetUserId).
		Filter("imsi", data.Imsi).
		Filter("alarm_code", data.AlarmCode).
		Delete()

	if err != nil {
		c.Fail("删除失败：" + err.Error())
		return
	}

	// 发送 MQTT 消息
	msg := map[string]interface{}{
		"opr":       "unlock",
		"imsi":      data.Imsi,
		"alarmCode": data.AlarmCode,
	}
	msgBytes, _ := json.Marshal(msg)
	if err := mqtt.PublishMessage("pixmoving/analysis/heartbeat", msgBytes); err != nil {
		log.Error("发送 MQTT 消息失败：" + err.Error())
	}

	c.Success(nil)
}

// ListAlarmLock 查询告警锁定列表
//
// 请求：GET /api/alarm/list_alarm_lock
// 参数：
//   - imsi: 设备IMSI号（可选）
//   - alarmCode: 告警代码（可选）
//   - userId: 用户ID（必填，需要验证与当前登录账号的从属关系）
func (c *AlarmController) ListAlarmLock() {
	// 获取当前用户信息
	userInfo, err := c.CheckToken()
	if err != nil {
		c.Fail("获取用户信息失败：" + err.Error())
		return
	}

	// 获取查询参数
	imsi := c.GetString("imsi", "")
	alarmCode := c.GetString("alarmCode", "")
	userId, err := c.GetInt("userId", 0)
	if err != nil || userId <= 0 {
		c.Fail("userId参数错误")
		return
	}

	// 验证权限：
	// 1. 如果查询自己的记录，直接允许
	// 2. 如果查询其他用户的记录，需要验证从属关系
	if userId != int(userInfo.Id) {
		userList, err := c.userServer.Child(userInfo, "")
		if err != nil {
			c.Fail("获取用户列表失败：" + err.Error())
			return
		}

		hasPermission := false
		for _, user := range userList {
			if user.Id == int64(userId) {
				hasPermission = true
				break
			}
		}

		if !hasPermission {
			c.Fail("没有权限查看该用户的告警锁定记录")
			return
		}
	}

	// 构建查询条件
	o := orm.NewOrm()
	qb := o.QueryTable(new(pix.UserAlarmLock))
	cond := qb.Filter("user_id", userId)

	if imsi != "" {
		cond = cond.Filter("imsi__icontains", imsi)
	}
	if alarmCode != "" {
		cond = cond.Filter("alarm_code__icontains", alarmCode)
	}

	// 获取所有数据
	var locks []pix.UserAlarmLock
	_, err = cond.OrderBy("-updated_time").All(&locks, "id", "user_id", "imsi", "alarm_code")

	if err != nil {
		c.Fail("查询失败：" + err.Error())
		return
	}

	// 返回结果
	c.Success(locks)
}
