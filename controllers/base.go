package controllers

import (
	"ccapi/auth"
	"ccapi/common"
	"ccapi/models/dto"
	"ccapi/models/pix"
	log2 "ccapi/server/log"
	"ccapi/server/redis"
	"encoding/json"
	"fmt"
	"io"
	"mime/multipart"
	"strconv"
	"time"

	"github.com/aliyun/aliyun-oss-go-sdk/oss"
	"github.com/beego/beego/v2/client/orm"
	beego "github.com/beego/beego/v2/server/web"
	"github.com/prometheus/client_golang/prometheus"
	"github.com/thinkeridea/go-extend/exnet"
)

var (
	SendSuccessfully = `已下发"%s"的操作指令`
	SendFailed       = `下发"%s"的操作指令失败`
	AddLogFailed     = `添加"%s"操作日志失败: %s`
)

const (
	StatusProcessing   int64 = iota // 0.处理中
	StatusFailed                    // 1.处理失败
	StatusSuccessfully              // 2.处理成功
)

type BaseController struct {
	beego.Controller
}

// Success 成功响应
func (b *BaseController) Success(data interface{}) {
	var response dto.Response
	response.Code = 0
	response.Msg = "success"
	response.Data = data
	b.Data["json"] = response
	b.ServeJSON()
}

// Fail 失败响应
func (b *BaseController) Fail(msg string) {
	var response dto.Response
	response.Code = 1
	response.Msg = msg
	log2.Error(msg)
	b.Data["json"] = response
	b.ServeJSON()
}

// HZSuccess 成功响应
func (b *BaseController) HZSuccess(data interface{}) {
	var response dto.HZResponse
	response.Code = 0
	response.Message = "success"
	response.Data = data
	b.Data["json"] = response
	b.ServeJSON()
}

// HZFail 失败响应
func (b *BaseController) HZFail(msg string) {
	var response dto.HZResponse
	response.Code = 1
	response.Message = msg
	log2.Error(msg)
	b.Data["json"] = response
	b.ServeJSON()
}

// AddOperationLog 记录用户操作日志
func (b *BaseController) AddOperationLog(user dto.UserInfo, message string, model int, oldData string, newData string, imsi string) error {
	nowTime := time.Now().Format("2006-01-02 15:04:05")
	ip := b.GetIp()
	addr, err := common.GetAddr(ip)
	if err != nil {
		return err
	}
	o := orm.NewOrm()

	logData := &pix.LogOperation{
		Uid:         user.Id,
		Username:    user.Username,
		Message:     message,
		CreatedTime: nowTime,
		UpdatedTime: nowTime,
		Model:       model,
		OldData:     oldData,
		NewData:     newData,
		Ip:          ip,
		Addr:        addr,
		Imsi:        imsi,
	}

	_, err = o.Insert(logData)
	return err
}

// AddOperationLogWithId 记录用户操作日志并返回id
func (b *BaseController) AddOperationLogWithId(user dto.UserInfo, message string, model int, oldData string, newData string, imsi string) (int64, error) {
	nowTime := time.Now().Format("2006-01-02 15:04:05")
	o := orm.NewOrm()
	ip := b.GetIp()
	addr, err := common.GetAddr(ip)
	if err != nil {
		return 0, err
	}
	logData := &pix.LogOperation{
		Uid:         user.Id,
		Username:    user.Username,
		Message:     message,
		CreatedTime: nowTime,
		UpdatedTime: nowTime,
		Model:       model,
		OldData:     oldData,
		NewData:     newData,
		Ip:          ip,
		Addr:        addr,
		Imsi:        imsi,
	}

	id, err := o.Insert(logData)
	return id, err
}

// CheckToken 验证登录并获取用户信息
func (b *BaseController) CheckToken() (dto.UserInfo, error) {
	var (
		userInfo dto.UserInfo
		err      error
		uid      int64
		do       map[string]string
	)

	// 开发环境下，只有携带特定请求头才能模拟超级管理员
	if beego.BConfig.RunMode == "dev" {
		devBypass := b.Ctx.Request.Header.Get("X-Dev-Bypass-Auth")
		// 仅供开发调试，严禁生产环境使用
		if devBypass == "let-me-in" {
			userInfo = dto.UserInfo{
				Id:       1,
				Username: "admin",
				Name:     "超级管理员",
				Level:    0, // 实际的超级管理员级别
				Status:   0, // 正常状态
				ParentID: 0, // 父级ID
			}
			return userInfo, nil
		}
		// 未携带特定头，继续正常token校验
	}

	token := b.Ctx.Request.Header.Get("X-Token")
	//
	// uid = redis.Get(fmt.Sprintf(common.RedisSessionMultiTokenUid, token))
	// if uid == "" {
	// 	err = errors.New("登录已失效, 请重新登录")
	// 	return userInfo, err
	// }
	//
	// do, err = redis.HGetAll(common.RedisUserInfof + uid)
	// if err != nil {
	// 	err = fmt.Errorf("用户信息(%v)获取失败, %s", uid, err.Error())
	// 	return userInfo, err
	// } else if do == nil {
	// 	err = fmt.Errorf("数据不存在")
	// 	return userInfo, err
	// }
	uid, err = auth.VerifyToken(token)
	if err != nil {
		return userInfo, err
	}
	do, err = redis.HGetAll(common.RedisUserInfof + strconv.FormatInt(uid, 10))
	id := common.ChangeStringToInt(do["id"])
	level := common.ChangeStringToInt(do["level"])
	status := common.ChangeStringToInt(do["status"])
	parentId := common.ChangeStringToInt(do["parent_id"])
	check := common.ChangeStringToInt(do["check"])

	userInfo.Id = int64(id)
	userInfo.Username = do["username"]
	userInfo.Logo = do["logo"]
	userInfo.Name = do["name"]
	userInfo.HeadImg = do["head_img"]
	userInfo.Tip = do["tip"]
	userInfo.Level = level
	userInfo.Status = int64(status)
	userInfo.ParentID = int64(parentId)
	userInfo.Check = check
	userInfo.Tel = do["tel"]

	return userInfo, nil
}

// DomainURI 获取域名 URI
func (b *BaseController) DomainURI() string {
	test := b.Ctx.Request.Host
	return test
}

// UploadOss 上传文件到oss
func (b *BaseController) UploadOss(endpoint string, accessKeyId string, accessKeySecret string, bucketName string,
	filename string, file multipart.File) error {
	client, err := oss.New(endpoint, accessKeyId, accessKeySecret)
	if err != nil {
		return err
	}
	accConfig := oss.TransferAccConfiguration{}
	accConfig.Enabled = true

	err = client.SetBucketTransferAcc(bucketName, accConfig)
	if err != nil {
		return err
	}
	bucket, err := client.Bucket(bucketName)
	if err != nil {
		return err
	}
	err = bucket.PutObject(filename, file)
	if err != nil {
		return err
	}
	return nil
}

func (c *AdvertController) UploadOssIO(endpoint, accessKeyId, accessKeySecret, bucketName, filename string, file io.Reader) error {
	// 创建OSS客户端
	client, err := oss.New(endpoint, accessKeyId, accessKeySecret)
	if err != nil {
		return fmt.Errorf("创建OSS客户端失败: %v", err)
	}

	// 启用传输加速(我测试一下）
	accConfig := oss.TransferAccConfiguration{
		Enabled: true,
	}
	err = client.SetBucketTransferAcc(bucketName, accConfig)
	if err != nil {
		return fmt.Errorf("设置传输加速失败: %v", err)
	}

	// 获取存储空间
	bucket, err := client.Bucket(bucketName)
	if err != nil {
		return fmt.Errorf("获取存储空间失败: %v", err)
	}

	// 上传文件（直接使用io.Reader）
	err = bucket.PutObject(filename, file)
	if err != nil {
		return fmt.Errorf("上传文件失败: %v", err)
	}

	return nil
}

// EditOss 修改oss文件名
func (b *BaseController) EditOss(endpoint string, accessKeyId string, accessKeySecret string, bucketName string,
	oldName string, newName string) error {
	client, err := oss.New(endpoint, accessKeyId, accessKeySecret)
	if err != nil {
		return err
	}
	accConfig := oss.TransferAccConfiguration{}
	accConfig.Enabled = true

	err = client.SetBucketTransferAcc(bucketName, accConfig)
	if err != nil {
		return err
	}
	bucket, err := client.Bucket(bucketName)
	if err != nil {
		return err
	}
	// 修改文件名
	_, err = bucket.CopyObject(oldName, newName)
	if err != nil {
		return err
	}

	if oldName != newName {
		// 删除原文件
		err = bucket.DeleteObject(oldName)
		if err != nil {
			return err
		}
	}

	return nil
}

func (b *BaseController) GetIp() string {
	// 开发环境下返回模拟IP
	if beego.BConfig.RunMode == "dev" {
		return "127.0.0.1"
	}

	ip := exnet.ClientPublicIP(b.Ctx.Request)
	if ip == "" {
		ip = exnet.ClientIP(b.Ctx.Request)
	}
	return ip
}

var (
	requestCounter = prometheus.NewCounterVec(
		prometheus.CounterOpts{
			Name: "myapp_requests_total",
			Help: "Total number of requests received",
		},
		[]string{"path", "method"},
	)
)

func init() {
	prometheus.MustRegister(requestCounter)
}

func (b *BaseController) Get() {
	b.Data["json"] = "Hello,world!"
	b.ServeJSON()

	requestCounter.With(prometheus.Labels{"path": "/",
		"method": "GET"}).Inc()
}

func (b *BaseController) Validate(key string) string {
	switch key {
	case "Required":
		return "该字段不能为空"
	case "Min":
		return "该字段的最小长度是 %b"
	default:
		return "未知错误"
	}
}

// CustomError 返回自定义错误码和消息
func (c *BaseController) CustomError(code int, msg string) {
	c.Data["json"] = map[string]interface{}{
		"code": code,
		"msg":  msg,
		"data": nil,
	}
	c.ServeJSON()
}

// ParseJSON 解析JSON请求体
func (c *BaseController) ParseJSON(v interface{}) error {
	return json.Unmarshal(c.Ctx.Input.RequestBody, v)
}
