package controllers

import (
	"ccapi/common"
	"ccapi/models/dto"
	"ccapi/service"
	"fmt"
	beego "github.com/beego/beego/v2/server/web"
	"github.com/tjfoc/gmsm/sm4"
	"io/ioutil"
	"os/exec"
	"strings"
	"time"
)

// CertController 证书模块
type CertController struct {
	BaseController
	service service.CertServiceInter
}

// InitCertRouter 初始化路由
func InitCertRouter() beego.LinkNamespace {
	return beego.NSNamespace("/cert",
		beego.NSRouter("/list", &CertController{}, "get:List"), // 证书列表
		// beego.NSRouter("/add", &CertController{}, "post:Add"),          //新增证书
		// beego.NSRouter("/edit", &CertController{}, "post:Edit"),        //修改证书
		// beego.NSRouter("/delete", &CertController{}, "post:Delete"),    //删除证书
		// beego.NSRouter("/download", &CertController{}, "get:Download"), //下载证书
		beego.NSRouter("/disk_no", &CertController{}, "get:DiskNo"), // 获取硬盘序列号
		beego.NSRouter("/upload", &CertController{}, "post:Upload"), // 上传证书
	)
}

// Prepare 注册服务
func (c *CertController) Prepare() {
	c.service = service.NewCertService()
}

// List 证书列表
func (c *CertController) List() {
	userInfo, err := c.CheckToken()
	if err != nil {
		c.Fail("获取用户信息失败：" + err.Error())
		return
	}

	datas, err := c.service.List(userInfo.Id)
	if err != nil {
		c.Fail("获取数据失败：" + err.Error())
		return
	}

	serverDir, _ := beego.AppConfig.String("server_dir")
	keyName := serverDir + "key.pem"
	key, _ := beego.AppConfig.String("certKey")
	iv := make([]byte, sm4.BlockSize)
	data, err := sm4.ReadKeyFromPemFile(keyName, nil)

	if err != nil {
		c.Fail("证书文件读取失败，" + err.Error())
		return
	}
	deData, err := common.Sm4Decrypt([]byte(key), iv, data)
	fmt.Println("deData:", string(deData))

	date := string(deData[0:10])
	dateInt := common.ChangeStringToInt(date)

	c.Success(map[string]interface{}{
		"name":         datas.Name,
		"disk_no":      string(deData[10:30]),
		"max_num":      string(deData[30:]),
		"end_time":     dateInt,
		"id":           datas.Id,
		"created_time": datas.CreatedTime,
		"updated_time": datas.UpdatedTime,
		"uid":          datas.Uid,
	})
	return
}

func (c *CertController) Add() {
	var (
		err error
		add dto.AddCert
	)

	data := c.Ctx.Request.Body
	body, err := ioutil.ReadAll(data)
	err = common.Validate(body, &add)
	if err != nil {
		c.Fail("参数错误," + err.Error())
		return
	}

	now := time.Now().Unix()
	if now > add.Date {
		c.Fail("证书已过期，请重新编辑")
		return
	}

	key, _ := beego.AppConfig.String("certKey")
	iv := make([]byte, sm4.BlockSize)

	datas := fmt.Sprintf("%d%s%d", add.Date, add.DiskNo, add.MaxNum)
	ciphertext, err := common.Sm4Encrypt([]byte(key), iv, []byte(datas))

	id, err := c.service.Add(add.Uid, add.MaxNum, int(add.Date), add.DiskNo)
	if err != nil {
		c.Fail("新增失败：" + err.Error())
		return
	}

	err = sm4.WriteKeyToPemFile(fmt.Sprintf("%d", id)+"key.pem", ciphertext, nil)
	if err != nil {
		c.Fail("WriteKeyToPem error")
		return
	}

	c.Success(nil)
}

func (c *CertController) Edit() {
	var (
		err error
		add dto.EditCert
	)

	data := c.Ctx.Request.Body
	body, err := ioutil.ReadAll(data)
	err = common.Validate(body, &add)
	if err != nil {
		c.Fail("参数错误," + err.Error())
		return
	}

	now := time.Now().Unix()
	if now > add.Date {
		c.Fail("证书已过期，请重新编辑")
		return
	}

	key, _ := beego.AppConfig.String("certKey")
	iv := make([]byte, sm4.BlockSize)

	datas := fmt.Sprintf("%d%s%d", add.Date, add.DiskNo, add.MaxNum)
	ciphertext, err := common.Sm4Encrypt([]byte(key), iv, []byte(datas))

	err = c.service.Edit(add.Id, add.Uid, add.MaxNum, int(add.Date), add.DiskNo)
	if err != nil {
		c.Fail("新增失败：" + err.Error())
		return
	}

	err = sm4.WriteKeyToPemFile(fmt.Sprintf("%d", add.Id)+"key.pem", ciphertext, nil)
	if err != nil {
		c.Fail("WriteKeyToPem error")
		return
	}

	c.Success(nil)
}

func (c *CertController) Download() {
	id := c.GetString("id", "")

	c.Ctx.Output.Download(id+"key.pem", "key.pem")
}

func (c *CertController) Delete() {
	var (
		err error
		add dto.CertId
	)

	data := c.Ctx.Request.Body
	body, err := ioutil.ReadAll(data)
	err = common.Validate(body, &add)
	if err != nil {
		c.Fail("参数错误," + err.Error())
		return
	}

	id := add.Id

	err = c.service.Delete(id)
	if err != nil {
		c.Fail("删除失败：" + err.Error())
		return
	}

	c.Success(nil)
}

// DiskNo 获取硬盘序列号
func (c *CertController) DiskNo() {
	diskName, _ := beego.AppConfig.String("disk_name")
	cmdStr := "udevadm info --query=all --name=" + diskName + " | grep ID_SERIAL | head -n 1"
	fmt.Println("cmd:", cmdStr)
	cmd := exec.Command("/bin/bash", "-c", cmdStr)
	output, err := cmd.StdoutPipe()
	if err != nil {
		fmt.Println("无法获取命令的标准输出管道", err.Error())
		return
	}

	// 执行Linux命令
	if err := cmd.Start(); err != nil {
		fmt.Println("Linux命令执行失败，请检查命令输入是否有误", err.Error())
		return
	}
	// 读取输出
	prints, err := ioutil.ReadAll(output)
	if err != nil {
		fmt.Println("打印异常，请检查")
		return
	}
	if err := cmd.Wait(); err != nil {
		fmt.Println("Wait", err.Error())
		return
	}

	outs := string(prints)

	fmt.Println("out:", outs)
	index := strings.Index(outs, "=")
	fmt.Println("index:", outs)
	diskNo := outs[index+1 : len(outs)-1]
	fmt.Println("diskNo:", diskNo)
	c.Success(diskNo)
}

// Upload 上传证书
func (c *CertController) Upload() {
	serverDir, _ := beego.AppConfig.String("server_dir")

	file, _, err := c.GetFile("file")
	if err != nil {
		c.Fail("获取文件失败：" + err.Error())
		return
	}

	filePath := serverDir + "key.pem"
	defer file.Close()

	err = c.SaveToFile("file", "key.pem")
	if err != nil {
		c.Fail("文件上传失败：" + err.Error())
		return
	}

	key, _ := beego.AppConfig.String("certKey")
	iv := make([]byte, sm4.BlockSize)
	data, err := sm4.ReadKeyFromPemFile("key.pem", nil)

	if err != nil {
		c.Fail("证书文件格式错误")
		return
	}
	deData, err := common.Sm4Decrypt([]byte(key), iv, data)
	fmt.Println("deData:", string(deData))

	date := string(deData[0:10])
	dateInt := common.ChangeStringToInt(date)

	now := time.Now().Unix()
	if now > int64(dateInt) {
		c.Fail("证书已过期，请重新导入")
		return
	}

	diskNo := string(deData[10:30])
	diskName, _ := beego.AppConfig.String("disk_name")
	cmdStr := "udevadm info --query=all --name=" + diskName + " | grep ID_SERIAL | head -n 1"
	fmt.Println("cmd:", cmdStr)
	cmd := exec.Command("/bin/bash", "-c", cmdStr)
	output, err := cmd.StdoutPipe()
	if err != nil {
		fmt.Println("无法获取命令的标准输出管道", err.Error())
		return
	}

	// 执行Linux命令
	if err := cmd.Start(); err != nil {
		fmt.Println("Linux命令执行失败，请检查命令输入是否有误", err.Error())
		return
	}
	// 读取输出
	prints, err := ioutil.ReadAll(output)
	if err != nil {
		fmt.Println("打印异常，请检查")
		return
	}
	if err := cmd.Wait(); err != nil {
		fmt.Println("Wait", err.Error())
		return
	}

	outs := string(prints)

	fmt.Println("out:", outs)
	index := strings.Index(outs, "=")
	fmt.Println("index:", outs)
	diskNoS := outs[index+1 : len(outs)-1]
	if diskNoS != diskNo {
		c.Fail("硬盘序列号错误，请重新导入")
		return
	}

	err = c.SaveToFile("file", filePath)
	if err != nil {
		c.Fail("文件上传失败：" + err.Error())
		return
	}

	userInfo, err := c.CheckToken()
	if err != nil {
		c.Fail("获取用户信息失败：" + err.Error())
		return
	}

	err = c.service.Update(userInfo.Id)
	if err != nil {
		c.Fail("更新失败：" + err.Error())
		return
	}

	c.Success(nil)
}
