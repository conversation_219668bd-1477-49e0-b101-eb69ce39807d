package controllers

import (
	"ccapi/pkg"
	"ccapi/service"
	"fmt"
	beego "github.com/beego/beego/v2/server/web"
	"strings"
	"time"
)

type CommonController struct {
	BaseController
	commonService service.CommonServiceInter
}

func (c *CommonController) Prepare() {
	c.commonService = service.NewCommonService()
}

// Upload 文件上传
func (c *CommonController) Upload() {
	file, header, err := c.GetFile("file")
	if err != nil {
		c.Fail("文件上传失败: " + err.Error())
		return
	}
	defer file.Close()

	// 获取文件信息
	filename := header.Filename
	position := strings.LastIndex(filename, ".")
	fileType := filename[position+1:]

	// 获取OSS配置
	endpoint, _ := beego.AppConfig.String("endpoint")
	accessKeyId, _ := beego.AppConfig.String("accessKeyId")
	accessKeySecret, _ := beego.AppConfig.String("accessKeySecret")
	bucketName, _ := beego.AppConfig.String("bucketName")

	// 生成OSS文件名
	ossFilename := fmt.Sprintf("%s%d", pkg.RandomString("alpha", 5), time.Now().Unix()) + filename[position:]

	// 上传到OSS
	if err = c.commonService.UploadToOSS(endpoint, accessKeyId, accessKeySecret, bucketName, ossFilename, file); err != nil {
		c.Fail("上传到OSS失败: " + err.Error())
		return
	}

	// 获取OSS访问URL
	ossUrl, _ := beego.AppConfig.String("ossUrl")
	fileUrl := ossUrl + ossFilename

	// 返回上传结果
	result := &UploadResult{
		Url:      fileUrl,
		Filename: filename,
		Type:     fileType,
	}

	c.Success(result)
}

func InitCommonRouter() beego.LinkNamespace {
	return beego.NSNamespace("/common",
		beego.NSRouter("/upload", &CommonController{}, "post:Upload"),
	)
}

type UploadResult struct {
	Url      string `json:"url"`      // 文件访问URL
	Filename string `json:"filename"` // 原始文件名
	Type     string `json:"type"`     // 文件类型
}
