package controllers

import (
	"ccapi/common"
	"ccapi/models/dto"
	"ccapi/service"
	beego "github.com/beego/beego/v2/server/web"
)

// CompanyController 公司模块
type CompanyController struct {
	BaseController
	service service.CompanyServiceInter
}

// InitCompanyRouter 初始化路由
func InitCompanyRouter() beego.LinkNamespace {
	return beego.NSNamespace("/company",
		beego.NSRouter("/list", &CompanyController{}, "get:List"),      // 获取公司列表
		beego.NSRouter("/add", &CompanyController{}, "post:Add"),       // 新增公司
		beego.NSRouter("/update", &CompanyController{}, "post:Update"), // 修改公司
		beego.NSRouter("/delete", &CompanyController{}, "post:Delete"), // 删除公司
	)
}

// Prepare 注册服务
func (c *CompanyController) Prepare() {
	c.service = service.NewCompanyService()
}

// List 获取公司列表
func (c *CompanyController) List() {
	name := c.GetString("name", "")
	page, err := c.GetInt("page", 1)
	limit, err := c.GetInt("limit", 10)

	data, count, err := c.service.List(page, limit, name)

	if err != nil {
		c.Fail("获取列表失败：" + err.Error())
		return
	}

	c.Success(map[string]interface{}{
		"items": data,
		"total": count,
	})
}

// Add 新增公司
func (c *CompanyController) Add() {
	var (
		err        error
		addCompany dto.AddCompany
	)

	data := c.Ctx.Input.RequestBody
	err = common.Validate(data, &addCompany)

	if err != nil {
		c.Fail("参数错误," + err.Error())
		return
	}

	name := addCompany.Name
	status := addCompany.Status
	username := addCompany.Username
	password := addCompany.Password

	exist := c.service.CheckName(name, 0)
	if exist {
		c.Fail("该公司名已存在")
		return
	}

	err = c.service.Add(name, status, username, password)
	if err != nil {
		c.Fail("新增失败:" + err.Error())
		return
	}

	c.Success(nil)
	return
}

// Update 修改公司
func (c *CompanyController) Update() {
	var (
		err        error
		addCompany dto.UpdateCompany
	)

	data := c.Ctx.Input.RequestBody
	err = common.Validate(data, &addCompany)

	if err != nil {
		c.Fail("参数错误," + err.Error())
		return
	}

	id := addCompany.Id
	name := addCompany.Name
	status := addCompany.Status

	exist := c.service.CheckName(name, id)
	if exist {
		c.Fail("该公司名已存在")
		return
	}

	err = c.service.Update(id, name, status)
	if err != nil {
		c.Fail("修改失败:" + err.Error())
		return
	}

	c.Success(nil)
	return
}

// Delete 删除公司
func (c *CompanyController) Delete() {
	var (
		err    error
		postId dto.PostId
	)

	data := c.Ctx.Input.RequestBody
	err = common.Validate(data, &postId)

	if err != nil {
		c.Fail("参数错误," + err.Error())
		return
	}

	id := postId.Id

	err = c.service.Delete(id)
	if err != nil {
		c.Fail("删除失败：" + err.Error())
		return
	}

	c.Success(nil)
}
