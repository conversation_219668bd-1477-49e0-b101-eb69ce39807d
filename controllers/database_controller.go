package controllers

import (
	"ccapi/server/mysql"
	beego "github.com/beego/beego/v2/server/web"
)

// DatabaseController 数据库管理控制器
type DatabaseController struct {
	BaseController
}

// InitDatabaseRouter 初始化数据库管理路由
func InitDatabaseRouter() beego.LinkNamespace {
	return beego.NSNamespace("/database",
		beego.NSRouter("/health", &DatabaseController{}, "get:HealthCheck"),
		beego.NSRouter("/stats", &DatabaseController{}, "get:Stats"),
		beego.NSRouter("/stats/:name", &DatabaseController{}, "get:GetConnectionStats"),
		beego.NSRouter("/status", &DatabaseController{}, "get:GetStatus"),

	)
}

// HealthCheck 数据库健康检查
func (c *DatabaseController) HealthCheck() {
	results := mysql.HealthCheck()

	allHealthy := true
	healthDetails := make(map[string]interface{})

	for name, err := range results {
		if err != nil {
			allHealthy = false
			healthDetails[name] = map[string]interface{}{
				"healthy": false,
				"error":   err.Error(),
			}
		} else {
			healthDetails[name] = map[string]interface{}{
				"healthy": true,
				"error":   nil,
			}
		}
	}

	response := map[string]interface{}{
		"healthy": allHealthy,
		"details": healthDetails,
	}

	if allHealthy {
		c.Success(response)
	} else {
		c.Data["json"] = map[string]interface{}{
			"code": 1,
			"msg":  "部分数据库连接异常",
			"data": response,
		}
		c.ServeJSON()
	}
}

// Stats 获取所有数据库连接统计
func (c *DatabaseController) Stats() {
	stats := mysql.GetAllConnectionStats()

	if errorMsg, exists := stats["error"]; exists {
		c.Fail(errorMsg.(string))
		return
	}

	c.Success(stats)
}

// GetConnectionStats 获取指定数据库连接统计
func (c *DatabaseController) GetConnectionStats() {
	name := c.Ctx.Input.Param(":name")
	if name == "" {
		c.Fail("数据库名称不能为空")
		return
	}

	stats, err := mysql.GetConnectionStats(name)
	if err != nil {
		c.Fail(err.Error())
		return
	}

	c.Success(stats)
}

// GetStatus 获取数据库连接状态
func (c *DatabaseController) GetStatus() {
	status := mysql.GetConnectionStatus()

	connected := 0
	pending := 0

	for _, s := range status {
		if s == "connected" {
			connected++
		} else if s == "pending" {
			pending++
		}
	}

	disconnected := 0
	for _, s := range status {
		if s == "disconnected" {
			disconnected++
		}
	}

	response := map[string]interface{}{
		"connections": status,
		"summary": map[string]interface{}{
			"total":        len(status),
			"connected":    connected,
			"disconnected": disconnected,
		},
	}

	c.Success(response)
}


