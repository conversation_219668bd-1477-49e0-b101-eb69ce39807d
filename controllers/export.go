package controllers

import (
	"ccapi/exporters"
	"ccapi/models/dto"
	"ccapi/pkg"
	"ccapi/service"
	"fmt"
	"strconv"
	"time"

	"github.com/beego/beego/v2/core/logs"
)

// ExportController 只负责导出相关接口
// 路由注册可在 operation.go 或单独注册

type ExportController struct {
	BaseController
}

// ExportField 导出字段结构体
type ExportField struct {
	Key   string `json:"key"`
	Label string `json:"label"`
}

// ExportOrderRequest 导出请求结构体，包含所有订单查询参数（嵌套OrderListQuery）
type ExportOrderRequest struct {
	Fields             []ExportField `json:"fields"`
	FileType           string        `json:"fileType"`
	dto.OrderListQuery `json:",inline"`
}

func (c *ExportController) ExportOrder() {
	var req ExportOrderRequest
	if err := pkg.Unmarshal(c.Ctx.Input.RequestBody, &req); err != nil {
		logs.Error("导出订单参数解析失败: %v", err)
		c.Fail("参数格式错误: " + err.Error())
		return
	}

	// 白名单校验
	for _, f := range req.Fields {
		if len(ExportFieldWhiteList) > 0 {
			if !ExportFieldWhiteList[f.Key] {
				c.Fail("包含非法导出字段: " + f.Key)
				return
			}
		}
	}

	query := req.OrderListQuery
	userInfo, err := c.CheckToken()
	if err != nil {
		c.Fail("用户身份校验失败")
		return
	}
	userId := userInfo.Id
	orders, err := service.NewOperationService().OrderListAll(&query, c.Ctx, userId)
	if err != nil {
		c.Fail("获取订单数据失败：" + err.Error())
		return
	}
	if len(req.Fields) == 0 {
		c.Fail("导出字段不能为空")
		return
	}

	// 组装表头和数据
	headers := []string{}
	fieldKeys := []string{}
	for _, f := range req.Fields {
		headers = append(headers, f.Label)
		fieldKeys = append(fieldKeys, f.Key)
	}

	// 构建行数据
	var rows []service.ExportRow
	for _, order := range orders {
		if len(order.TicketItems) == 0 {
			// 如果没有票型信息，使用订单信息填充一行
			price := 0
			if order.Num != 0 {
				price = int(order.OriginalFee) / order.Num
			}
			dummyTicket := dto.OrderTicketItem{
				TicketName: order.TicketName,
				Num:        order.Num,
				Price:      price,
				Subtotal:   int(order.OriginalFee),
			}
			rows = append(rows, BuildExportRow(fieldKeys, order, dummyTicket))
		} else {
			// 如果有票型信息，为每种票型创建一行
			for _, ticket := range order.TicketItems {
				rows = append(rows, BuildExportRow(fieldKeys, order, ticket))
			}
		}
	}

	table := service.ExportTable{
		Headers:   headers,
		FieldKeys: fieldKeys,
		Rows:      rows,
	}

	fileType := req.FileType
	exporter := exporters.GetExporter(fileType)

	// 设置Content-Type，不设置文件名（由前端处理）
	var contentType string
	fontPath := "fonts/NotoSansSC-Regular.ttf"
	if fileType == "pdf" {
		contentType = "application/pdf"
	} else if fileType == "csv" {
		contentType = "text/csv"
	} else {
		contentType = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
	}
	c.Ctx.Output.Header("Content-Type", contentType)
	err = exporter.Export(c.Ctx.ResponseWriter, table, fontPath)
	if err != nil {
		c.Fail("导出失败:" + err.Error())
	}
}

// BuildExportRow 构建单行导出数据（所有值转为string）

func BuildExportRow(fieldKeys []string, order dto.OrderResult, ticket dto.OrderTicketItem) service.ExportRow {
	row := make(service.ExportRow, len(fieldKeys))
	for i, key := range fieldKeys {
		switch key {
		case "id":
			row[i] = strconv.Itoa(order.Id)
		case "orderNo":
			row[i] = order.OrderNo
		case "routeName":
			row[i] = order.RouteName
		case "begin":
			row[i] = order.Begin
		case "end":
			row[i] = order.End
		case "nickname":
			row[i] = order.Nickname
		case "mobile":
			row[i] = order.Mobile
		case "num":
			row[i] = strconv.Itoa(ticket.Num)
		case "carName":
			row[i] = order.CarName
		case "vinCode":
			row[i] = order.VinCode
		case "status":
			// 将数字状态码转换为对应的状态文字描述
			switch order.Status {
			case 0:
				row[i] = "待付款"
			case 1:
				row[i] = "已付款待出行"
			case 2:
				row[i] = "已验票核销"
			case 3:
				row[i] = "已退票"
			case 4:
				row[i] = "已过期"
			default:
				row[i] = strconv.Itoa(order.Status) // 未知状态保持原样
			}
		case "ticketName":
			row[i] = ticket.TicketName
		case "price":
			row[i] = fmt.Sprintf("%.2f", float64(ticket.Price)/100)
		case "subtotal":
			row[i] = fmt.Sprintf("%.2f", float64(ticket.Subtotal)/100)
		case "totalFee":
			row[i] = fmt.Sprintf("%.2f", float64(ticket.Subtotal)/100)
		case "originalFee":
			row[i] = fmt.Sprintf("%.2f", float64(ticket.Subtotal)/100)
		case "discountedFee":
			row[i] = fmt.Sprintf("%.2f", float64(ticket.Subtotal)/100)
		case "couponId":
			row[i] = strconv.FormatInt(order.CouponId, 10)
		case "reserveTimeSlot":
			row[i] = order.ReserveTimeSlot
		case "createdTime":
			if order.CreatedTime > 0 {
				row[i] = time.Unix(order.CreatedTime, 0).Format("2006-01-02 15:04:05")
			} else {
				row[i] = ""
			}
		case "timeEnd":
			if order.TimeEnd > 0 {
				// 将 int 转换为字符串，然后解析为时间
				timeStr := fmt.Sprintf("%d", order.TimeEnd)
				t, err := time.Parse("20060102150405", timeStr)
				if err != nil {
					logs.Error("解析 timeEnd 失败: %v, 原始值: %d", err, order.TimeEnd)
					row[i] = timeStr // 解析失败时返回原始字符串
				} else {
					row[i] = t.Format("2006-01-02 15:04:05")
				}
			} else {
				row[i] = ""
			}
		case "refundSuccessTime":
			row[i] = order.RefundSuccessTime
		case "basePrice":
			row[i] = fmt.Sprintf("%.2f", float64(order.BasePrice)/100)
		default:
			row[i] = ""
		}
	}
	return row
}

// ExportTaskExecutionRequest 导出历史任务请求结构体，包含所有历史任务查询参数
type ExportTaskExecutionRequest struct {
	Fields   []ExportField `json:"fields"`
	FileType string        `json:"fileType"`

	// 字符串字段
	TaskName     string `json:"taskName"`
	Imsi         string `json:"imsi"`
	VehiclePlate string `json:"vehiclePlate"`
	Creator      string `json:"creator"`
	SortField    string `json:"sortField"`
	SortOrder    string `json:"sortOrder"`

	// 整数字段
	TaskType           int   `json:"taskType"`
	Status             int   `json:"status"`
	DepartureTimeBegin int64 `json:"departureTimeBegin"`
	DepartureTimeEnd   int64 `json:"departureTimeEnd"`
	EndTimeBegin       int64 `json:"endTimeBegin"`
	EndTimeEnd         int64 `json:"endTimeEnd"`
	CreatedTimeBegin   int64 `json:"createdTimeBegin"`
	CreatedTimeEnd     int64 `json:"createdTimeEnd"`
	TaskId             int64 `json:"taskId"`
	ChildId            int   `json:"childId"`

	// 时间范围列表格式
	CreatedTime   []int64 `json:"createdTime"`
	DepartureTime []int64 `json:"departureTime"`
	EndTime       []int64 `json:"endTime"`
}

// ExportTaskExecution 导出历史任务Excel或PDF
func (c *ExportController) ExportTaskExecution() {
	var req ExportTaskExecutionRequest
	if err := pkg.UniversalUnmarshal(c, &req); err != nil {
		logs.Error("导出历史任务参数解析失败: %v", err)
		c.Fail("参数格式错误: " + err.Error())
		return
	}

	// 记录导出请求参数
	logs.Debug("导出历史任务请求参数: TaskName=%s, TaskType=%d, Status=%d, Imsi=%s, VehiclePlate=%s, Creator=%s, DepartureTimeBegin=%d, DepartureTimeEnd=%d, EndTimeBegin=%d, EndTimeEnd=%d, CreatedTimeBegin=%d, CreatedTimeEnd=%d, TaskId=%d, ChildId=%d, SortField=%s, SortOrder=%s, FileType=%s, Fields=%v",
		req.TaskName, req.TaskType, req.Status, req.Imsi, req.VehiclePlate, req.Creator, req.DepartureTimeBegin, req.DepartureTimeEnd, req.EndTimeBegin, req.EndTimeEnd, req.CreatedTimeBegin, req.CreatedTimeEnd, req.TaskId, req.ChildId, req.SortField, req.SortOrder, req.FileType, req.Fields)

	// 白名单校验（默认允许所有字段）
	for _, f := range req.Fields {
		if len(TaskExecutionExportFieldWhiteList) > 0 {
			if !TaskExecutionExportFieldWhiteList[f.Key] {
				c.Fail("包含非法导出字段: " + f.Key)
				return
			}
		}
	}

	// 查询参数组装，从req转换为TaskExecutionListRequest
	query := dto.TaskExecutionListRequest{
		TaskName:           req.TaskName,
		Imsi:               req.Imsi,
		VehiclePlate:       req.VehiclePlate,
		Creator:            req.Creator,
		SortField:          req.SortField,
		SortOrder:          req.SortOrder,
		TaskType:           req.TaskType,
		Status:             req.Status,
		DepartureTimeBegin: req.DepartureTimeBegin,
		DepartureTimeEnd:   req.DepartureTimeEnd,
		EndTimeBegin:       req.EndTimeBegin,
		EndTimeEnd:         req.EndTimeEnd,
		CreatedTimeBegin:   req.CreatedTimeBegin,
		CreatedTimeEnd:     req.CreatedTimeEnd,
		TaskId:             req.TaskId,
		ChildId:            req.ChildId,
		CreatedTime:        req.CreatedTime,
		DepartureTime:      req.DepartureTime,
		EndTime:            req.EndTime,
		Page:               1,
		Limit:              10000,
	}
	userInfo, err := c.CheckToken()
	if err != nil {
		c.Fail("用户身份校验失败")
		return
	}
	userId := userInfo.Id



	taskExecutionService := service.NewTaskExecutionService()
	tasks, err := taskExecutionService.GetListForExport(&query, userId)
	if err != nil {
		logs.Error("获取历史任务数据失败: %v", err)
		c.Fail("获取历史任务数据失败：" + err.Error())
		return
	}

	logs.Debug("导出查询到的任务数量: %d", len(tasks))

	if len(req.Fields) == 0 {
		c.Fail("导出字段不能为空")
		return
	}

	// 组装表头和数据
	headers := []string{}
	fieldKeys := []string{}
	for _, f := range req.Fields {
		headers = append(headers, f.Label) // 中文表头
		fieldKeys = append(fieldKeys, f.Key)
	}

	rowsData := service.BuildTaskExecutionExportRows(fieldKeys, tasks)

	// 转换为 ExportRow 类型
	rows := make([]service.ExportRow, len(rowsData))
	for i, row := range rowsData {
		rows[i] = service.ExportRow(row)
	}

	table := service.ExportTable{Headers: headers, FieldKeys: fieldKeys, Rows: rows}

	fileType := req.FileType
	exporter := exporters.GetExporter(fileType)
	filename := "task_executions.xlsx"
	contentType := "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
	fontPath := "fonts/NotoSansSC-Regular.ttf"

	if fileType == "pdf" {
		filename = "task_executions.pdf"
		contentType = "application/pdf"
	} else if fileType == "csv" {
		filename = "task_executions.csv"
		contentType = "text/csv"
	}

	c.Ctx.Output.Header("Content-Type", contentType)
	c.Ctx.Output.Header("Content-Disposition", "attachment; filename="+filename)
	err = exporter.Export(c.Ctx.ResponseWriter, table, fontPath)
	if err != nil {
		c.Fail("导出失败:" + err.Error())
	}
}

// 默认白名单允许所有字段
var ExportFieldWhiteList = map[string]bool{}

// 历史任务导出字段白名单（默认允许所有字段）
var TaskExecutionExportFieldWhiteList = map[string]bool{}
