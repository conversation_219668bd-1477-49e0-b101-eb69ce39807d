package controllers

import (
	"ccapi/common"
	"ccapi/common/protocol"
	"ccapi/models/dto"
	"ccapi/server/redis"
	"ccapi/service"
	"crypto/md5"
	"encoding/hex"
	"encoding/json"
	"fmt"
	beego "github.com/beego/beego/v2/server/web"
	"io"
	"os"
	"strings"
)

// FirmwareController 固件模块
type FirmwareController struct {
	BaseController
	service       service.FirmwareServiceInter
	DeviceService service.DeviceServiceInter
	userService   service.UserServiceInter
}

// InitFirmwareRouter 初始化路由
func InitFirmwareRouter() beego.LinkNamespace {
	return beego.NSNamespace("/firmware",
		beego.NSRouter("/list", &FirmwareController{}, "get:List"),               // 获取固件列表
		beego.NSRouter("/upload", &FirmwareController{}, "post:Upload"),          // 固件文件上传
		beego.NSRouter("/download", &FirmwareController{}, "get:Download"),       // 固件文件下载
		beego.NSRouter("/create", &FirmwareController{}, "post:Create"),          // 新增固件
		beego.NSRouter("/update", &FirmwareController{}, "post:Update"),          // 修改固件
		beego.NSRouter("/delete", &FirmwareController{}, "post:Delete"),          // 删除固件
		beego.NSRouter("/finish", &FirmwareController{}, "post:Finish"),          // 删除固件
		beego.NSRouter("/upgrade", &FirmwareController{}, "post:Upgrade"),        // 固件升级
		beego.NSRouter("/upgrade_vcu", &FirmwareController{}, "post:UpgradeVCU"), // VCU固件升级
		beego.NSRouter("/log", &FirmwareController{}, "get:Log"),                 // 获取升级日志
		beego.NSRouter("/all_log", &FirmwareController{}, "get:AllLog"),          // 获取所有升级日志
		beego.NSRouter("/delete_log", &FirmwareController{}, "post:DeleteLog"),   // 删除升级日志
		beego.NSRouter("/find_new", &FirmwareController{}, "get:FindNew"),        // 查找新固件
	)
}

// Prepare 注册服务
func (c *FirmwareController) Prepare() {
	c.service = service.NewFirmwareService()
	c.DeviceService = service.NewDeviceService()
	c.userService = service.NewUserService()
}

// List 获取固件列表
func (c *FirmwareController) List() {
	page, err := c.GetInt("page", 1)
	limit, err := c.GetInt("limit", 10)
	username := c.GetString("username", "")
	hardwareVer := c.GetString("hardware_ver", "")
	softwareVer := c.GetString("software_ver", "")
	_type, err := c.GetInt("type", -1)
	name := c.GetString("name", "")

	if err != nil {
		c.Fail("参数错误：" + err.Error())
		return
	}

	userInfo, _ := c.CheckToken()

	userList, err := c.userService.Child(userInfo, "")

	var idsStr []string
	for _, v := range userList {
		idsStr = append(idsStr, fmt.Sprintf("%d", v.Id))
	}

	allId := strings.Join(idsStr, ",")

	data, count, err := c.service.List(page, limit, username, hardwareVer, softwareVer, _type, name, allId)
	if err != nil {
		c.Fail("获取列表失败：" + err.Error())
		return
	}

	c.Success(map[string]interface{}{
		"items": data,
		"total": count,
	})
}

// Upload 上传固件文件
func (c *FirmwareController) Upload() {
	file, fileHeader, err := c.GetFile("file")
	if err != nil {
		c.Fail("获取文件失败：" + err.Error())
		return
	}

	filename := fileHeader.Filename
	fileSize := fileHeader.Size

	firmwareOss, err := beego.AppConfig.Int("firmwareOss")
	if err != nil {
		c.Fail("参数不存在，请联系管理员添加")
		return
	}

	fileURL := ""

	hash := md5.New()
	if _, err := io.Copy(hash, file); err != nil {
		c.Fail(err.Error())
		return
	}

	hashValue := hash.Sum(nil)
	hashStr := hex.EncodeToString(hashValue)

	file.Seek(0, 0)
	// io.Copy()方法读取文件内容的时候，会移动文件指针。而在调用完io.Copy()方法之后，文件指针是被移到文件的末尾处的，所以在第二次读取文件文件的内容为空。
	// 将文件指针移到文件开头

	if firmwareOss == 1 {
		endpoint, _ := beego.AppConfig.String("endpoint")
		accessKeyId, _ := beego.AppConfig.String("accessKeyId")
		accessKeySecret, _ := beego.AppConfig.String("accessKeySecret")
		bucketName, _ := beego.AppConfig.String("bucketName")

		err = c.UploadOss(endpoint, accessKeyId, accessKeySecret, bucketName, filename, file)

		if err != nil {
			c.Fail("上传到oss失败:" + err.Error())
			return
		}

		s, _ := beego.AppConfig.String("ossUrl")
		fileURL = s + filename
	} else {
		filePath := "./upload/firmwares/" + filename

		err = c.SaveToFile("file", filePath)
		if err != nil {
			c.Fail("文件上传失败：" + err.Error())
			return
		}
		fileURL = fmt.Sprintf("https://%s/files/firmwares/%s", c.DomainURI(), filename)
	}

	subEndIdx := strings.LastIndex(filename, ".")
	var version = ""
	if subEndIdx != -1 {
		version = filename[:subEndIdx]
	} else {
		version = filename
	}

	c.Success(map[string]interface{}{
		"name":    filename,
		"md5":     hashStr,
		"url":     fileURL,
		"version": version,
		"size":    fileSize,
	})
	return
}

// Download 下载固件文件
func (c *FirmwareController) Download() {
	filename := c.GetString("filename")
	filePath := "./upload/firmwares/" + filename
	c.Ctx.Output.Download(filePath)
	return
}

// Create 新增固件
func (c *FirmwareController) Create() {
	var (
		err         error
		addFirmware dto.AddFirmware
	)

	data := c.Ctx.Input.RequestBody
	err = common.Validate(data, &addFirmware)

	if err != nil {
		c.Fail("参数错误," + err.Error())
		return
	}

	name := addFirmware.Name
	uid := addFirmware.Uid
	_type := addFirmware.Type
	hardwareVer := addFirmware.HardwareVer
	softwareVer := addFirmware.SoftwareVer
	url := addFirmware.Url
	md5 := addFirmware.Md5
	size := addFirmware.Size
	msg := addFirmware.Msg

	exist2 := c.service.CheckVersion(0, softwareVer)
	if exist2 {
		c.Fail("该软件版本号已存在")
		return
	}

	userInfo, err := c.CheckToken()
	if err != nil {
		c.Fail("获取用户信息失败：" + err.Error())
		return
	}

	name = name[:15] + "_" + md5 + ".zip"

	ossUrl, err := beego.AppConfig.String("ossUrl")
	oldName := url[len(ossUrl):]

	endpoint, _ := beego.AppConfig.String("endpoint")
	accessKeyId, _ := beego.AppConfig.String("accessKeyId")
	accessKeySecret, _ := beego.AppConfig.String("accessKeySecret")
	bucketName, _ := beego.AppConfig.String("bucketName")

	err = c.EditOss(endpoint, accessKeyId, accessKeySecret, bucketName, oldName, name)
	if err != nil {
		c.Fail("新增失败：" + err.Error())
		return
	}

	url = ossUrl + name

	_, err = c.service.Create(name, uid, _type, hardwareVer, softwareVer, url, md5, size, msg)
	if err != nil {
		c.Fail("新增失败：" + err.Error())
		return
	}

	// todo
	// 添加升级策略中的版本信息
	// if data, err := common.CreateUpgradeData(dataForm.Name, dataForm.Md5); err == nil {
	//	upgrade.VersionMap.Set(dataForm.Version, data)
	// }

	// 操作日志
	logType := "添加固件"

	newData := map[string]interface{}{
		"name":        name,
		"uid":         uid,
		"type":        _type,
		"hardwareVer": hardwareVer,
		"softwareVer": softwareVer,
		"url":         url,
		"md5":         md5,
		"size":        size,
		"msg":         msg,
	}

	logData, _ := json.Marshal(newData)

	if e := c.AddOperationLog(userInfo, logType, 10, "", string(logData), ""); e != nil {
		msg := fmt.Sprintf(AddLogFailed, logType, e)
		c.Fail(msg)
		return
	}

	c.Success(nil)
	return
}

// Update 修改固件
func (c *FirmwareController) Update() {
	var (
		err          error
		editFirmware dto.EditFirmware
	)

	data := c.Ctx.Input.RequestBody
	err = common.Validate(data, &editFirmware)

	if err != nil {
		c.Fail("参数错误," + err.Error())
		return
	}

	name := editFirmware.Name
	uid := editFirmware.Uid
	_type := editFirmware.Type
	hardwareVer := editFirmware.HardwareVer
	softwareVer := editFirmware.SoftwareVer
	url := editFirmware.Url
	md5 := editFirmware.Md5
	id := editFirmware.Id
	size := editFirmware.Size
	msg := editFirmware.Msg

	exist2 := c.service.CheckVersion(id, softwareVer)
	if exist2 {
		c.Fail("该软件版本号已存在")
		return
	}

	oldFirmware, err := c.service.Find(int64(id))
	if err != nil {
		c.Fail("查找失败：" + err.Error())
		return
	}

	oldData := map[string]interface{}{
		"name":        oldFirmware.Name,
		"uid":         oldFirmware.Uid,
		"type":        oldFirmware.Type,
		"hardwareVer": oldFirmware.HardwareVer,
		"softwareVer": oldFirmware.SoftwareVer,
		"url":         oldFirmware.Url,
		"md5":         oldFirmware.Md5,
		"size":        oldFirmware.Size,
		"msg":         oldFirmware.Msg,
	}

	oldLog, _ := json.Marshal(oldData)

	oldName := oldFirmware.Name
	name = name[:15] + "_" + md5 + ".zip"
	newData := map[string]interface{}{
		"name":        name,
		"uid":         uid,
		"type":        _type,
		"hardwareVer": hardwareVer,
		"softwareVer": softwareVer,
		"url":         url,
		"md5":         md5,
		"size":        size,
		"msg":         msg,
	}
	newLog, _ := json.Marshal(newData)

	endpoint, _ := beego.AppConfig.String("endpoint")
	accessKeyId, _ := beego.AppConfig.String("accessKeyId")
	accessKeySecret, _ := beego.AppConfig.String("accessKeySecret")
	bucketName, _ := beego.AppConfig.String("bucketName")

	err = c.EditOss(endpoint, accessKeyId, accessKeySecret, bucketName, oldName, name)
	if err != nil {
		c.Fail("新增失败：" + err.Error())
		return
	}

	ossUrl, err := beego.AppConfig.String("ossUrl")
	url = ossUrl + name

	err = c.service.Update(id, name, uid, _type, hardwareVer, softwareVer, url, md5, size, msg)
	if err != nil {
		c.Fail("修改失败：" + err.Error())
		return
	}

	// todo
	// 修改升级策略中的版本信息
	// if data, err := common.CreateUpgradeData(dataForm.Name, dataForm.Md5); err == nil {
	//	upgrade.VersionMap.Set(firmware.Version, data)
	// }
	//
	// // 删除旧文件
	// if firmware.Name != dataForm.Name {
	//	filePath := fmt.Sprintf("./upload/firmwares/%s", firmware.Name)
	//	if gfile.Exists(filePath) {
	//		gfile.Remove(filePath)
	//	}
	// }

	// 操作日志
	logType := "修改固件"
	userInfo, err := c.CheckToken()
	if err != nil {
		c.Fail("获取用户信息失败：" + err.Error())
		return
	}
	if e := c.AddOperationLog(userInfo, logType, 10, string(oldLog), string(newLog), ""); e != nil {
		msg := fmt.Sprintf(AddLogFailed, logType, e)
		c.Fail(msg)
		return
	}

	c.Success(nil)
	return
}

// Delete 删除固件
func (c *FirmwareController) Delete() {
	var (
		err    error
		postId dto.PostId
	)

	data := c.Ctx.Input.RequestBody
	err = common.Validate(data, &postId)

	if err != nil {
		c.Fail("参数错误," + err.Error())
		return
	}

	id := postId.Id

	firmware, err := c.service.Find(id)
	if err != nil {
		c.Fail("获取固件失败：" + err.Error())
		return
	}

	err = c.service.Delete(id)
	if err != nil {
		c.Fail("删除失败：" + err.Error())
		return
	}

	filePath := "./upload/firmwares/" + firmware.Name
	if common.PathExists(filePath) {
		err = os.Remove("./upload/firmwares/" + firmware.Name) // 删除文件
		if err != nil {
			c.Fail("删除文件失败：" + err.Error())
			return
		}
	}

	// todo 删除升级策略中的固件

	userInfo, err := c.CheckToken()
	if err != nil {
		c.Fail("获取用户信息失败：" + err.Error())
		return
	}
	// 操作日志
	logType := "删除固件"
	logMsg := fmt.Sprintf("%s(%d): %s(hardware_version), %s(software_version),%s(name), %s(md5)",
		logType,
		firmware.Id,
		firmware.HardwareVer,
		firmware.SoftwareVer,
		firmware.Name,
		firmware.Md5,
	)
	if e := c.AddOperationLog(userInfo, logType, 10, "", logMsg, ""); e != nil {
		msg := fmt.Sprintf(AddLogFailed, logType, e)
		c.Fail(msg)
		return
	}

	c.Success(nil)
}

// Upgrade 固件升级
func (c *FirmwareController) Upgrade() {
	var (
		err      error
		upgrade  dto.Upgrade
		log      []dto.LogUpgrade
		userInfo dto.UserInfo
		ids      []int64
	)

	data := c.Ctx.Input.RequestBody
	err = common.Validate(data, &upgrade)

	if err != nil {
		c.Fail("参数错误," + err.Error())
		return
	}

	userInfo, _ = c.CheckToken()
	deviceId := upgrade.DeviceId
	firmwareId := upgrade.FirmwareId
	mode := upgrade.Mode
	t := upgrade.Time
	info := upgrade.Info

	if len(deviceId) == 0 {
		c.Fail("参数错误，至少选择一台设备")
		return
	}

	log, err, ids = c.service.Upgrade(deviceId, firmwareId, mode, info, userInfo.Id, t)

	if err != nil {
		c.Fail("升级失败," + err.Error())
		return
	}

	if t == 0 {
		for _, v := range log {
			cmdData := map[string]interface{}{
				"imsi": v.DeviceImsi,
				"cmd":  protocol.CmdMap[23],
			}

			err = c.DeviceService.SendMessage(cmdData)
			if err != nil {
				c.Fail("发送消息失败：" + err.Error())
				return
			}

			redisData, _ := json.Marshal(map[string]interface{}{
				"cmd": protocol.CmdMap[23],
			})
			// 设置到 Redis 缓存 (120s
			redisKey := fmt.Sprintf(common.RedisOperationPbMsg, v.DeviceImsi, protocol.CmdMap[23])
			redis.SetEx(redisKey, 120, redisData)

			// 操作日志
			logType := "对终端下发普通升级固件指令"
			if mode == 1 {
				logType = "对终端下发强制升级固件指令"
			}
			logMsg := fmt.Sprintf(`(%s): %s(last_hareware_version), %s(last_software_version)
			,%s(hardware_version),%s(software_version)`,
				v.DeviceImsi,
				v.LastHardwareVersion,
				v.LastSoftwareVersion,
				v.HardwareVersion,
				v.SoftwareVersion,
			)
			if e := c.AddOperationLog(userInfo, logType, 10, "", logMsg, v.DeviceImsi); e != nil {
				msg := fmt.Sprintf(AddLogFailed, logType, e)
				c.Fail(msg)
				return
			}
		}
	} else {
		err = c.service.AddJob(deviceId, t, ids)
		if err != nil {
			c.Fail("xxl-job添加失败：" + err.Error())
			return
		}
	}

	c.Success(nil)
	return
}

// UpgradeVCU VCU固件升级
func (c *FirmwareController) UpgradeVCU() {
	var (
		err      error
		upgrade  dto.Upgrade
		log      []dto.LogUpgrade
		userInfo dto.UserInfo
	)

	data := c.Ctx.Input.RequestBody
	err = common.Validate(data, &upgrade)

	if err != nil {
		c.Fail("参数错误," + err.Error())
		return
	}

	deviceId := upgrade.DeviceId
	firmwareId := upgrade.FirmwareId
	mode := upgrade.Mode
	info := upgrade.Info

	if len(deviceId) == 0 {
		c.Fail("参数错误，至少选择一台设备")
		return
	}

	log, err, _ = c.service.Upgrade(deviceId, firmwareId, mode, info, 0, 0)

	if err != nil {
		c.Fail("升级失败," + err.Error())
		return
	}

	for _, v := range log {
		cmdData := map[string]interface{}{
			"imsi": v.DeviceImsi,
			"cmd":  protocol.CmdMap[12],
		}

		err = c.DeviceService.SendMessage(cmdData)
		if err != nil {
			c.Fail("发送消息失败：" + err.Error())
			return
		}

		redisData, _ := json.Marshal(map[string]interface{}{
			"cmd": protocol.CmdMap[12],
		})
		// 设置到 Redis 缓存 (120s
		redisKey := fmt.Sprintf(common.RedisOperationPbMsg, v.DeviceImsi, protocol.CmdMap[12])
		redis.SetEx(redisKey, 120, redisData)

		// 操作日志
		logType := "对终端下发普通升级VCU固件指令"
		if mode == 1 {
			logType = "对终端下发强制升级VCU固件指令"
		}
		logMsg := fmt.Sprintf(`%s(%s): %s(last_hareware_version), %s(last_software_version)
			,%s(hardware_version),%s(software_version)`,
			logType,
			v.DeviceImsi,
			v.LastHardwareVersion,
			v.LastSoftwareVersion,
			v.HardwareVersion,
			v.SoftwareVersion,
		)
		if e := c.AddOperationLog(userInfo, logType, 10, "", logMsg, v.DeviceImsi); e != nil {
			msg := fmt.Sprintf(AddLogFailed, logType, e)
			c.Fail(msg)
			return
		}
	}
	c.Success(nil)
	return
}

// Log 获取升级日志
func (c *FirmwareController) Log() {
	page, err := c.GetInt("page", 1)
	limit, err := c.GetInt("limit", 10)
	imsi := c.GetString("imsi", "")
	mode, err := c.GetInt("mode", -1)
	deviceName := c.GetString("device_name", "")
	hardwareVer := c.GetString("hardware_ver", "")
	softwareVer := c.GetString("software_ver", "")
	username := c.GetString("username", "")
	_type, err := c.GetInt("type", -1)
	createBegin, err := c.GetInt("create_begin", -1)
	createEnd, err := c.GetInt("create_end", -1)
	upgradeBegin, err := c.GetInt("upgrade_begin", -1)
	upgradeEnd, err := c.GetInt("upgrade_end", -1)
	name := c.GetString("name", "")
	status, err := c.GetInt("status", -1)
	operateName := c.GetString("operate_name", "")
	isCheck, err := c.GetInt("is_check", -1)

	if err != nil {
		c.Fail("参数错误：" + err.Error())
		return
	}

	userInfo, _ := c.CheckToken()

	userList, err := c.userService.Child(userInfo, "")

	var idsStr []string
	for _, v := range userList {
		idsStr = append(idsStr, fmt.Sprintf("%d", v.Id))
	}

	allId := strings.Join(idsStr, ",")

	re, count, err := c.service.Log(page, limit, imsi, mode, deviceName, hardwareVer, softwareVer, username, _type,
		createBegin, createEnd, upgradeBegin, upgradeEnd, name, status, operateName, isCheck, allId)

	if err != nil {
		c.Fail("获取日志失败：" + err.Error())
		return
	}

	c.Success(map[string]interface{}{
		"items": re,
		"total": count,
	})
}

// AllLog 获取所有日志
func (c *FirmwareController) AllLog() {
	page, err := c.GetInt("page", 1)
	limit, err := c.GetInt("limit", 10)
	imsi := c.GetString("imsi", "")
	mode, err := c.GetInt("mode", -1)
	model, err := c.GetInt("model", -1)
	deviceName := c.GetString("device_name", "")
	hardwareVer := c.GetString("hardware_ver", "")
	softwareVer := c.GetString("software_ver", "")
	username := c.GetString("username", "")
	_type, err := c.GetInt("type", -1)
	createBegin, err := c.GetInt("create_begin", -1)
	createEnd, err := c.GetInt("create_end", -1)
	upgradeBegin, err := c.GetInt("upgrade_begin", -1)
	upgradeEnd, err := c.GetInt("upgrade_end", -1)
	name := c.GetString("name", "")
	status, err := c.GetInt("status", -1)
	operateName := c.GetString("operate_name", "")
	isCheck, err := c.GetInt("is_check", -1)

	if err != nil {
		c.Fail("参数错误：" + err.Error())
		return
	}

	userInfo, _ := c.CheckToken()

	userList, err := c.userService.Child(userInfo, "")

	var idsStr []string
	for _, v := range userList {
		idsStr = append(idsStr, fmt.Sprintf("%d", v.Id))
	}

	allId := strings.Join(idsStr, ",")

	re, err := c.service.AllLog(page, limit, imsi, mode, model, deviceName, hardwareVer, softwareVer, username, _type,
		createBegin, createEnd, upgradeBegin, upgradeEnd, name, status, operateName, isCheck, allId)

	if err != nil {
		c.Fail("获取日志失败：" + err.Error())
		return
	}

	c.Success(re)
	return
}

// DeleteLog 删除升级日志
func (c *FirmwareController) DeleteLog() {
	var (
		err        error
		deleteData dto.DeleteData
	)

	data := c.Ctx.Input.RequestBody
	err = common.Validate(data, &deleteData)

	if err != nil {
		c.Fail("参数错误," + err.Error())
		return
	}

	all := deleteData.All
	id := deleteData.Id

	err = c.service.DeleteLog(all, id)
	if err != nil {
		c.Fail("删除失败：" + err.Error())
		return
	}

	c.Success(nil)
	return
}

// Finish 升级完成接口
func (c *FirmwareController) Finish() {
	var (
		err    error
		postId dto.PostId
	)

	data := c.Ctx.Input.RequestBody
	err = common.Validate(data, &postId)

	if err != nil {
		c.Fail("参数错误," + err.Error())
		return
	}

	id := postId.Id

	err = c.service.Finish(id)
	if err != nil {
		c.Fail("失败：" + err.Error())
		return
	}

	c.Success(nil)
}

func (c *FirmwareController) FindNew() {
	imsi := c.GetString("imsi")
	data := c.service.FindNew(imsi)
	c.Success(data)
	return
}
