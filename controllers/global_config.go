package controllers

import (
	"ccapi/models/dto"
	"ccapi/service"
	"encoding/json"

	beego "github.com/beego/beego/v2/server/web"
)

type GlobalConfigController struct {
	BaseController
	service *service.GlobalConfigService
}

// Prepare 初始化
func (c *GlobalConfigController) Prepare() {
	c.service = service.NewGlobalConfigService()
}

// GetScreenDetectorConfig 获取大屏检测系统配置
func (c *GlobalConfigController) GetScreenDetectorConfig() {
	config, err := c.service.GetConfig("ScreenDetectorConfig")
	if err != nil {
		c.Fail(err.Error())
		return
	}

	if config == "" {
		// 创建默认配置
		defaultConfig := &dto.DetectorConfig{}
		defaultConfig.GeneralStats.Enable = true
		defaultConfig.GeneralStats.Fields.TotalVehicles = true
		defaultConfig.GeneralStats.Fields.OnlineVehicles = true
		defaultConfig.GeneralStats.Fields.OfflineVehicles = true
		defaultConfig.GeneralStats.Fields.MonthMileage = true
		defaultConfig.GeneralStats.Fields.MonthOperateTime = true
		defaultConfig.GeneralStats.Fields.TodayMileage = true
		defaultConfig.GeneralStats.Fields.TodayOperateTime = true
		defaultConfig.TaskStats.Enable = true
		defaultConfig.StatusStats.Enable = true
		defaultConfig.TableStats.Enable = true
		defaultConfig.OfflineStats.Enable = true

		c.Success(defaultConfig)
		return
	}

	var detectorConfig dto.DetectorConfig
	err = json.Unmarshal([]byte(config), &detectorConfig)
	if err != nil {
		c.Fail(err.Error())
		return
	}

	c.Success(&detectorConfig)
}

// UpdateScreenDetectorConfig 更新大屏检测系统配置
func (c *GlobalConfigController) UpdateScreenDetectorConfig() {
	var config dto.DetectorConfig
	err := c.ParseJSON(&config)
	if err != nil {
		c.Fail("参数错误: " + err.Error())
		return
	}

	configJson, err := json.Marshal(config)
	if err != nil {
		c.Fail("配置序列化失败: " + err.Error())
		return
	}

	err = c.service.UpdateConfig("ScreenDetectorConfig", string(configJson))
	if err != nil {
		c.Fail("更新失败: " + err.Error())
		return
	}

	c.Success(nil)
}

// InitGlobalConfigRouter 初始化全局配置路由
func InitGlobalConfigRouter() beego.LinkNamespace {
	return beego.NSNamespace("/global",
		beego.NSRouter("/screen-detector", &GlobalConfigController{}, "get:GetScreenDetectorConfig;put:UpdateScreenDetectorConfig"),
	)
}
