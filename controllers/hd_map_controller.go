package controllers

import (
	"ccapi/common"
	"ccapi/models/dto"
	"ccapi/models/pix"
	"ccapi/service"
	"crypto/md5"
	"encoding/hex"
	"encoding/xml"
	"fmt"
	"io"
	"os"
	"strings"

	"github.com/beego/beego/v2/core/logs"
	beego "github.com/beego/beego/v2/server/web"
)

// HdMapController 地图模块
type HdMapController struct {
	BaseController
	service       service.HdMapServiceInter
	userService   service.UserServiceInter
	deviceService service.DeviceServiceInter
}

// InitHdMapRouter 初始化路由
func InitHdMapRouter() beego.LinkNamespace {
	return beego.NSNamespace("/hd_map",
		beego.NSRouter("/list", &HdMapController{}, "get:List"),                    // 获取地图列表
		beego.NSRouter("/add", &HdMapController{}, "post:Add"),                     // 新增地图
		beego.NSRouter("/detail", &HdMapController{}, "get:Detail"),                // 地图详情
		beego.NSRouter("/delete", &HdMapController{}, "post:Delete"),               // 删除地图
		beego.NSRouter("/model_list", &HdMapController{}, "get:ModelList"),         // 获取地图模型列表
		beego.NSRouter("/add_model", &HdMapController{}, "post:AddMapModel"),       // 新增地图模型
		beego.NSRouter("/edit_model", &HdMapController{}, "post:EditMapModel"),     // 修改地图模型
		beego.NSRouter("/delete_model", &HdMapController{}, "post:DeleteMapModel"), // 删除地图模型
		beego.NSRouter("/all_map", &HdMapController{}, "get:AllMap"),               // 获取地图列表(无鉴权)
		beego.NSRouter("/upload", &HdMapController{}, "post:Upload"),               // 地图文件上传
		beego.NSRouter("/download", &HdMapController{}, "get:Download"),            // 地图文件下载
		beego.NSRouter("/sync", &HdMapController{}, "post:Sync"),                   // 同步地图
	)
}

// Prepare 注册服务
func (c *HdMapController) Prepare() {
	c.service = service.NewHdMapService()
	c.userService = service.NewUserService()
	c.deviceService = service.NewDeviceService()
}

// Detail 获取地图详情
func (c *HdMapController) Detail() {
	id, err := c.GetInt("id", 0)
	if err != nil {
		c.Fail(err.Error())
		return
	}

	hdMap, err := c.service.Get(id)

	if err != nil {
		if hdMap.Size == 0 {
			c.Success("该设备为模拟器")
			return
		}
		c.Fail(err.Error())
		return
	}
	c.Success(hdMap)
	return
}

// List 获取地图列表
func (c *HdMapController) List() {
	var (
		err   error
		page  int
		limit int
		hdMap []pix.HdMap
		count int64
	)

	page, err = c.GetInt("page", 1)
	limit, err = c.GetInt("limit", 10)
	name := c.GetString("name", "")

	userInfo, err := c.CheckToken()

	userList, err := c.userService.Child(userInfo, "")

	var ids []int64
	for _, v := range userList {
		ids = append(ids, v.Id)
	}

	if err != nil {
		c.Fail(err.Error())
		return
	}

	hdMap, count, err = c.service.List(page, limit, name, ids)

	if err != nil {
		c.Fail(err.Error())
		return
	}
	c.Success(map[string]interface{}{
		"items": hdMap,
		"total": count,
	})
	return
}

// Add 新增地图
func (c *HdMapController) Add() {
	var (
		err    error
		addMap dto.AddMap
	)

	data := c.Ctx.Input.RequestBody
	err = common.Validate(data, &addMap)

	if err != nil {
		c.Fail("参数错误," + err.Error())
		return
	}

	userInfo, err := c.CheckToken()
	if err != nil {
		c.Fail(err.Error())
		return
	}

	if !common.IsShengJiLevel(userInfo.Level) {
		c.Fail("暂无权限")
		return
	}

	name := addMap.Name
	version := addMap.Version
	mapData := addMap.Data
	_type := addMap.Type
	area := addMap.Area

	exist := c.service.Check(name)
	if exist {
		c.Fail("该地图名已存在")
		return
	}

	err = c.service.Add(name, version, mapData, _type, area, userInfo.Id, 0)
	if err != nil {
		c.Fail("新增失败：" + err.Error())
	}

	// 操作日志
	logType := "添加地图"
	logMsg := fmt.Sprintf("%s: %s(name), %s(version)",
		logType,
		name,
		version,
	)
	if e := c.AddOperationLog(userInfo, logMsg, 11, "", logMsg, ""); e != nil {
		msg := fmt.Sprintf(AddLogFailed, logType, e)
		c.Fail(msg)
		return
	}

	c.Success(nil)
	return
}

// Delete 删除地图
func (c *HdMapController) Delete() {
	var (
		err    error
		postId dto.PostId
	)

	data := c.Ctx.Input.RequestBody
	err = common.Validate(data, &postId)

	if err != nil {
		c.Fail("参数错误," + err.Error())
		return
	}

	id := postId.Id

	err = c.service.Delete(id)
	if err != nil {
		c.Fail("删除失败：" + err.Error())
		return
	}
	c.Success(nil)
	return
}

// ModelList 获取地图模型列表
func (c *HdMapController) ModelList() {
	mapId, err := c.GetInt("map_id", 0)
	if err != nil {
		c.Fail("获取参数失败")
		return
	}

	data, err := c.service.ModelList(mapId)
	if err != nil {
		c.Fail(err.Error())
		return
	}

	c.Success(data)
	return
}

// AddMapModel 新增地图模型
func (c *HdMapController) AddMapModel() {
	var (
		err      error
		dataForm dto.AddMapModel
	)

	data := c.Ctx.Input.RequestBody
	err = common.Validate(data, &dataForm)

	if err != nil {
		c.Fail("参数错误," + err.Error())
		return
	}

	mapId := dataForm.MapId
	name := dataForm.Name
	stationListData := dataForm.StationListData

	err = c.service.AddMapModel(mapId, name, stationListData)
	if err != nil {
		c.Fail("新增失败：" + err.Error())
		return
	}
	c.Success(nil)
	return
}

// EditMapModel 修改地图模型
func (c *HdMapController) EditMapModel() {
	var (
		err      error
		dataForm dto.EditMapModel
	)

	data := c.Ctx.Input.RequestBody
	err = common.Validate(data, &dataForm)

	if err != nil {
		c.Fail("参数错误," + err.Error())
		return
	}

	mapId := dataForm.MapId
	modelId := dataForm.ModelId
	name := dataForm.Name
	stationListData := dataForm.StationListData

	err = c.service.EditMapModel(mapId, modelId, name, stationListData)
	if err != nil {
		c.Fail("修改失败：" + err.Error())
		return
	}
	c.Success(nil)
	return
}

// DeleteMapModel 删除地图模型
func (c *HdMapController) DeleteMapModel() {
	var (
		err    error
		postId dto.PostId
	)

	data := c.Ctx.Input.RequestBody
	err = common.Validate(data, &postId)

	if err != nil {
		c.Fail("参数错误," + err.Error())
		return
	}

	id := postId.Id

	err = c.service.DeleteMapModel(id)
	if err != nil {
		c.Fail("删除失败：" + err.Error())
		return
	}
	c.Success(nil)
	return
}

// AllMap 获取所有地图
func (c *HdMapController) AllMap() {
	id, err := c.GetInt("id", 0)
	if err != nil {
		c.Fail("获取参数失败")
		return
	}

	data, err := c.service.AllMap(id)
	if err != nil {
		c.Fail(err.Error())
		return
	}

	c.Success(data)
	return
}

// Upload 上传并处理 OSM 文件
func (c *HdMapController) Upload() {
	logs.Info("开始处理 OSM 文件上传请求")

	// 1. 获取上传的文件
	file, fileHeader, err := c.GetFile("file")
	if err != nil {
		logs.Error("获取上传文件失败: %v", err)
		c.Fail("获取文件失败：" + err.Error())
		return
	}
	if file == nil {
		logs.Error("上传文件为空")
		c.Fail("上传文件不能为空")
		return
	}
	defer func() {
		if err := file.Close(); err != nil {
			logs.Error("关闭上传文件失败: %v", err)
		}
	}()

	// 2. 基本信息
	filename := fileHeader.Filename
	fileSize := fileHeader.Size
	logs.Info("上传文件信息 - 文件名: %s, 大小: %d bytes", filename, fileSize)

	// 3. 检查文件类型
	if !strings.HasSuffix(strings.ToLower(filename), ".osm") {
		logs.Error("文件格式错误: %s", filename)
		c.Fail("仅支持 .osm 文件格式")
		return
	}

	// 4. 计算 MD5
	hash := md5.New()
	if _, err := io.Copy(hash, file); err != nil {
		logs.Error("计算文件 MD5 失败: %v", err)
		c.Fail("计算文件特征值失败：" + err.Error())
		return
	}
	hashStr := hex.EncodeToString(hash.Sum(nil))
	logs.Info("文件 MD5: %s", hashStr)

	// 重置文件指针
	if _, err := file.Seek(0, 0); err != nil {
		logs.Error("重置文件指针失败: %v", err)
		c.Fail("处理文件失败：" + err.Error())
		return
	}

	// 5. 处理 OSM 文件（验证完整性并根据需要进行坐标转换）
	processedData, err := c.service.ProcessOsmFile(file)
	if err != nil {
		logs.Error("OSM 文件处理失败: %v", err)
		c.Fail("OSM 文件处理失败：" + err.Error())
		return
	}
	logs.Info("OSM 文件处理完成")

	// 6. 获取存储配置
	firmwareOss, err := beego.AppConfig.Int("firmwareOss")
	if err != nil {
		logs.Error("获取存储配置失败: %v", err)
		c.Fail("获取存储配置失败，请联系管理员")
		return
	}

	fileURL := ""

	if firmwareOss == 1 {
		// 7a. 上传到 OSS
		endpoint, _ := beego.AppConfig.String("endpoint")
		accessKeyId, _ := beego.AppConfig.String("accessKeyId")
		accessKeySecret, _ := beego.AppConfig.String("accessKeySecret")
		bucketName, _ := beego.AppConfig.String("bucketName")

		// 创建临时文件
		tempFile, err := os.CreateTemp("", "osm_*.osm")
		if err != nil {
			logs.Error("创建临时文件失败: %v", err)
			c.Fail("创建临时文件失败：" + err.Error())
			return
		}
		tempFilePath := tempFile.Name()
		defer func() {
			if err := os.Remove(tempFilePath); err != nil {
				logs.Error("删除临时文件失败: %v", err)
			}
		}()

		// 将处理后的数据写入临时文件
		encoder := xml.NewEncoder(tempFile)
		encoder.Indent("", "  ")
		if err := encoder.Encode(processedData); err != nil {
			logs.Error("写入临时文件失败: %v", err)
			c.Fail("写入临时文件失败：" + err.Error())
			return
		}
		if err := tempFile.Close(); err != nil {
			logs.Error("关闭临时文件失败: %v", err)
			c.Fail("关闭临时文件失败：" + err.Error())
			return
		}

		// 重新打开文件用于上传
		uploadFile, err := os.Open(tempFilePath)
		if err != nil {
			logs.Error("打开临时文件失败: %v", err)
			c.Fail("打开临时文件失败：" + err.Error())
			return
		}
		defer func() {
			if err := uploadFile.Close(); err != nil {
				logs.Error("关闭上传文件失败: %v", err)
			}
		}()

		logs.Info("开始上传文件到 OSS - Bucket: %s, 文件名: %s", bucketName, filename)
		err = c.UploadOss(endpoint, accessKeyId, accessKeySecret, bucketName, filename, uploadFile)
		if err != nil {
			logs.Error("上传到 OSS 失败: %v", err)
			c.Fail("上传到 OSS 失败：" + err.Error())
			return
		}

		s, _ := beego.AppConfig.String("ossUrl")
		fileURL = s + filename
		logs.Info("文件上传到 OSS 成功 - URL: %s", fileURL)
	} else {
		// 7b. 保存到本地
		uploadDir := "./upload/maps/"
		if err := os.MkdirAll(uploadDir, 0755); err != nil {
			logs.Error("创建上传目录失败: %v", err)
			c.Fail("创建上传目录失败：" + err.Error())
			return
		}

		filePath := uploadDir + filename

		// 创建目标文件
		dst, err := os.Create(filePath)
		if err != nil {
			logs.Error("创建目标文件失败: %v", err)
			c.Fail("创建文件失败：" + err.Error())
			return
		}
		defer func() {
			if err := dst.Close(); err != nil {
				logs.Error("关闭目标文件失败: %v", err)
			}
		}()

		// 将处理后的数据写入文件
		encoder := xml.NewEncoder(dst)
		encoder.Indent("", "  ")
		if err := encoder.Encode(processedData); err != nil {
			logs.Error("保存文件失败: %v", err)
			c.Fail("保存文件失败：" + err.Error())
			return
		}

		fileURL = fmt.Sprintf("https://%s/files/maps/%s", c.DomainURI(), filename)
		logs.Info("文件保存到本地成功 - 路径: %s", filePath)
	}

	// 8. 获取版本信息
	subEndIdx := strings.LastIndex(filename, ".")
	var version = ""
	if subEndIdx != -1 {
		version = filename[:subEndIdx]
	} else {
		version = filename
	}

	// 9. 返回结果
	response := map[string]interface{}{
		"name":    filename,
		"md5":     hashStr,
		"url":     fileURL,
		"version": version,
		"size":    fileSize,
	}
	logs.Info("文件处理完成 - 文件名: %s, 版本: %s", filename, version)
	c.Success(response)
	return
}

// Download 下载转换后的OSM地图
func (c *HdMapController) Download() {
	// TODO: 实现下载功能
	c.Success(nil)
	return
}

// Sync 同步地图
func (c *HdMapController) Sync() {
	// TODO: 实现同步功能
	c.Success(nil)
	return
}
