package controllers

import (
	"ccapi/common"
	"ccapi/models/dto"
	"ccapi/service"
	"fmt"
	beego "github.com/beego/beego/v2/server/web"
	"strings"
)

// LogOperationController 操作日志模块
type LogOperationController struct {
	BaseController
	service     service.LogOperationServiceInter
	userService service.UserServiceInter
}

// InitLogOperationRouter 初始化路由
func InitLogOperationRouter() beego.LinkNamespace {
	return beego.NSNamespace("/operation_log",
		beego.NSRouter("/list", &LogOperationController{}, "get:List"),                 // 获取操作日志列表
		beego.NSRouter("/all", &LogOperationController{}, "get:All"),                   // 获取全部操作日志
		beego.NSRouter("/delete", &LogOperationController{}, "post:Delete"),            // 删除操作日志
		beego.NSRouter("/batch_delete", &LogOperationController{}, "post:DeleteBatch"), // 批量删除操作日志
		beego.NSRouter("/clean", &LogOperationController{}, "post:Clean"),              // 清空操作日志
	)
}

// Prepare 注册服务
func (c *LogOperationController) Prepare() {
	c.service = service.NewLogOperationService()
	c.userService = service.NewUserService()
}

// List 获取操作日志列表
func (c *LogOperationController) List() {
	page, err := c.GetInt("page", 1)
	limit, err := c.GetInt("limit", 10)
	username := c.GetString("username", "")
	beginTime := c.GetString("begin_time", "")
	endTime := c.GetString("end_time", "")
	imsi := c.GetString("imsi", "")
	model, err := c.GetInt("model", 0)
	if err != nil {
		c.Fail("参数错误：" + err.Error())
		return
	}

	if err != nil {
		c.Fail(err.Error())
		return
	}

	userInfo, _ := c.CheckToken()

	userList, err := c.userService.Child(userInfo, "")

	var idsStr []string
	for _, v := range userList {
		idsStr = append(idsStr, fmt.Sprintf("%d", v.Id))
	}

	allId := strings.Join(idsStr, ",")
	data, count, err := c.service.List(page, limit, username, beginTime, endTime, model, allId, imsi)
	if err != nil {
		c.Fail("获取失败：" + err.Error())
		return
	}

	c.Success(map[string]interface{}{
		"items": data,
		"total": count,
	})
	return
}

// All 获取全部操作日志
func (c *LogOperationController) All() {
	username := c.GetString("username", "")
	beginTime := c.GetString("begin_time", "")
	endTime := c.GetString("end_time", "")
	model, err := c.GetInt("model", 0)
	imsi := c.GetString("imsi", "")
	if err != nil {
		c.Fail("参数错误：" + err.Error())
		return
	}

	userInfo, _ := c.CheckToken()

	userList, err := c.userService.Child(userInfo, "")

	var idsStr []string
	for _, v := range userList {
		idsStr = append(idsStr, fmt.Sprintf("%d", v.Id))
	}

	allId := strings.Join(idsStr, ",")

	data, err := c.service.All(username, beginTime, endTime, model, allId, imsi)
	if err != nil {
		c.Fail("获取失败：" + err.Error())
		return
	}

	c.Success(data)
	return
}

// Delete 删除单个日志
func (c *LogOperationController) Delete() {
	var (
		err    error
		postId dto.PostId
	)

	data := c.Ctx.Input.RequestBody
	err = common.Validate(data, &postId)

	if err != nil {
		c.Fail("参数错误," + err.Error())
		return
	}
	id := postId.Id

	err = c.service.Delete(id)

	if err != nil {
		c.Fail("删除失败：" + err.Error())
		return
	}

	userInfo, _ := c.CheckToken()

	// 操作日志
	logType := "删除操作记录"
	logMsg := fmt.Sprintf("%s(%d)",
		logType,
		id,
	)
	if e := c.AddOperationLog(userInfo, logMsg, 12, "", logMsg, ""); e != nil {
		msg := fmt.Sprintf(AddLogFailed, logType, e)
		c.Fail(msg)
		return
	}

	c.Success(nil)
	return
}

// DeleteBatch 批量删除
func (c *LogOperationController) DeleteBatch() {
	var (
		err    error
		postId dto.PostIds
	)

	data := c.Ctx.Input.RequestBody
	err = common.Validate(data, &postId)

	if err != nil {
		c.Fail("参数错误," + err.Error())
		return
	}
	id := postId.Id

	err = c.service.DeleteBatch(id)

	if err != nil {
		c.Fail("删除失败：" + err.Error())
		return
	}

	userInfo, _ := c.CheckToken()

	// 操作日志
	logType := "批量删除操作记录"
	logMsg := fmt.Sprintf("%s(%d)",
		logType,
		id,
	)
	if e := c.AddOperationLog(userInfo, logMsg, 12, "", logMsg, ""); e != nil {
		msg := fmt.Sprintf(AddLogFailed, logType, e)
		c.Fail(msg)
		return
	}

	c.Success(nil)
	return
}

// Clean 清空操作记录表
func (c *LogOperationController) Clean() {
	userInfo, _ := c.CheckToken()
	err := c.service.Clean()
	if err != nil {
		c.Fail("清空失败：" + err.Error())
		return
	}

	// 操作日志
	logType := "清空操作记录"
	if e := c.AddOperationLog(userInfo, logType, 12, "", logType, ""); e != nil {
		msg := fmt.Sprintf(AddLogFailed, logType, e)
		c.Fail(msg)
		return
	}
	c.Success(nil)
	return
}
