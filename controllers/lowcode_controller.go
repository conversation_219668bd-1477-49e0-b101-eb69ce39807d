package controllers

import (
	"ccapi/models/dto"
	"ccapi/service"
	"encoding/json"
	"fmt"
	"strconv"

	beego "github.com/beego/beego/v2/server/web"
)

type LowCodeController struct {
	BaseController
}

var lowCodeService = &service.LowCodeService{}

// GetConfigList 获取配置列表
// @Title 获取低代码配置列表
// @Description 获取低代码配置列表，支持分页和搜索
// @Param page query int false "页码" 1
// @Param page_size query int false "每页数量" 20
// @Param keyword query string false "关键词搜索"
// @Param route_path query string false "路由路径精准搜索"
// @Param status query int false "状态筛选"
// @Success 200 {object} dto.LowCodeConfigListResponse
// @router /lowcode/configs [get]
func (c *LowCodeController) GetConfigList() {
	// 解析查询参数
	req := &dto.LowCodeConfigListRequest{
		Page:     1,
		PageSize: 20,
	}

	if page := c.GetString("page"); page != "" {
		if p, err := strconv.Atoi(page); err == nil && p > 0 {
			req.Page = p
		}
	}

	if pageSize := c.GetString("page_size"); pageSize != "" {
		if ps, err := strconv.Atoi(pageSize); err == nil && ps > 0 && ps <= 100 {
			req.PageSize = ps
		}
	}

	req.Keyword = c.GetString("keyword")
	req.RoutePath = c.GetString("route_path")

	if status := c.GetString("status"); status != "" {
		if s, err := strconv.Atoi(status); err == nil {
			req.Status = &s
		}
	}

	// 调用服务
	result, err := lowCodeService.GetConfigList(req)
	if err != nil {
		c.Fail(err.Error())
		return
	}

	c.Success(result)
}

// GetConfig 获取单个配置
// @Title 获取单个低代码配置
// @Description 根据ID获取低代码配置详情
// @Param id path string true "配置ID"
// @Success 200 {object} dto.LowCodeConfigResponse
// @router /lowcode/configs/:id [get]
func (c *LowCodeController) GetConfig() {
	id := c.Ctx.Input.Param(":id")
	if id == "" {
		c.Fail("配置ID不能为空")
		return
	}

	result, err := lowCodeService.GetConfig(id)
	if err != nil {
		c.Fail(err.Error())
		return
	}

	c.Success(result)
}

// SaveConfig 保存配置
// @Title 保存低代码配置
// @Description 创建或更新低代码配置
// @Param config body dto.LowCodeConfigRequest true "配置信息"
// @Success 200 {object} dto.LowCodeConfigResponse
// @router /lowcode/configs [post]
func (c *LowCodeController) SaveConfig() {
	var req dto.LowCodeConfigRequest
	if err := json.Unmarshal(c.Ctx.Input.RequestBody, &req); err != nil {
		c.Fail("请求参数错误: " + err.Error())
		return
	}

	// 获取当前用户ID
	userID := c.getCurrentUserID()
	if userID == "" {
		userID = "system" // 默认用户
	}

	result, err := lowCodeService.SaveConfig(&req, userID)
	if err != nil {
		c.Fail(err.Error())
		return
	}

	c.Success(result)
}

// UpdateConfig 更新配置
// @Title 更新低代码配置
// @Description 更新指定ID的低代码配置
// @Param id path string true "配置ID"
// @Param config body dto.LowCodeConfigRequest true "配置信息"
// @Success 200 {object} dto.LowCodeConfigResponse
// @router /lowcode/configs/:id [put]
func (c *LowCodeController) UpdateConfig() {
	id := c.Ctx.Input.Param(":id")
	if id == "" {
		c.Fail("配置ID不能为空")
		return
	}

	var req dto.LowCodeConfigRequest
	if err := json.Unmarshal(c.Ctx.Input.RequestBody, &req); err != nil {
		c.Fail("请求参数错误: " + err.Error())
		return
	}

	// 设置ID
	req.ID = id

	// 获取当前用户ID
	userID := c.getCurrentUserID()
	if userID == "" {
		userID = "system" // 默认用户
	}

	result, err := lowCodeService.SaveConfig(&req, userID)
	if err != nil {
		c.Fail(err.Error())
		return
	}

	c.Success(result)
}

// DeleteConfig 删除配置
// @Title 删除低代码配置
// @Description 硬删除指定ID的低代码配置，此操作不可恢复
// @Param id path string true "配置ID"
// @Success 200 {string} string "删除成功"
// @router /lowcode/configs/:id [delete]
func (c *LowCodeController) DeleteConfig() {
	id := c.Ctx.Input.Param(":id")
	if id == "" {
		c.Fail("配置ID不能为空")
		return
	}

	err := lowCodeService.DeleteConfig(id)
	if err != nil {
		c.Fail(err.Error())
		return
	}

	c.Success("删除成功")
}

// CloneConfig 克隆配置
// @Title 克隆低代码配置
// @Description 克隆指定ID的低代码配置
// @Param id path string true "配置ID"
// @Success 200 {object} dto.LowCodeConfigResponse
// @router /lowcode/configs/:id/clone [post]
func (c *LowCodeController) CloneConfig() {
	id := c.Ctx.Input.Param(":id")
	if id == "" {
		c.Fail("配置ID不能为空")
		return
	}

	// 获取当前用户ID
	userID := c.getCurrentUserID()
	if userID == "" {
		userID = "system" // 默认用户
	}

	result, err := lowCodeService.CloneConfig(id, userID)
	if err != nil {
		c.Fail(err.Error())
		return
	}

	c.Success(result)
}

// getCurrentUserID 获取当前用户ID
func (c *LowCodeController) getCurrentUserID() string {
	userInfo, err := c.CheckToken()
	if err != nil {
		return ""
	}
	return fmt.Sprintf("%d", userInfo.Id)
}

// InitLowCodeRouter 初始化低代码路由
func InitLowCodeRouter() beego.LinkNamespace {
	return beego.NSNamespace("/lowcode",
		beego.NSRouter("/configs", &LowCodeController{}, "get:GetConfigList;post:SaveConfig"),
		beego.NSRouter("/configs/:id", &LowCodeController{}, "get:GetConfig;put:UpdateConfig;delete:DeleteConfig"),
		beego.NSRouter("/configs/:id/clone", &LowCodeController{}, "post:CloneConfig"),
	)
}
