package controllers

import (
	"ccapi/common"
	"ccapi/models/dto"
	"ccapi/service"
	"fmt"
	beego "github.com/beego/beego/v2/server/web"
	"strings"
)

// MessageController 消息模块
type MessageController struct {
	BaseController
	service     service.MessageServiceInter
	userService service.UserServiceInter
}

// InitMessageRouter 初始化路由
func InitMessageRouter() beego.LinkNamespace {
	return beego.NSNamespace("/message",
		beego.NSRouter("/list", &MessageController{}, "get:List"),             // 获取消息列表
		beego.NSRouter("/delete", &MessageController{}, "post:Delete"),        // 删除消息
		beego.NSRouter("/delete_all", &MessageController{}, "post:DeleteAll"), // 删除所有消息
		beego.NSRouter("/get", &MessageController{}, "post:GetMessage"),       // 获取车辆过期提醒、烟雾和二氧化碳报警消息
	)
}

// Prepare 注册服务
func (c *MessageController) Prepare() {
	c.service = service.NewMessageService()
	c.userService = service.NewUserService()
}

// List 获取消息列表
func (c *MessageController) List() {
	page, err := c.GetInt("page", 1)
	limit, err := c.GetInt("limit", 15)
	uid, err := c.GetInt("uid", 0)
	_type, err := c.GetInt("type", 0)

	if err != nil {
		c.Fail("参数错误：" + err.Error())
		return
	}

	imsi := c.GetString("imsi", "")
	name := c.GetString("name", "")

	userInfo, _ := c.CheckToken()

	userList, err := c.userService.Child(userInfo, "")

	var idsStr []string
	for _, v := range userList {
		idsStr = append(idsStr, fmt.Sprintf("%d", v.Id))
	}

	allId := strings.Join(idsStr, ",")

	data, count, err := c.service.List(page, limit, uid, _type, imsi, name, allId)
	if err != nil {
		c.Fail("获取数据失败：" + err.Error())
		return
	}
	c.Success(map[string]interface{}{
		"data":  data,
		"total": count,
	})
	return
}

// Delete 删除消息
func (c *MessageController) Delete() {
	var (
		err error
		ids dto.Ids
	)

	data := c.Ctx.Input.RequestBody
	err = common.Validate(data, &ids)

	if err != nil {
		c.Fail("参数错误," + err.Error())
		return
	}

	id := ids.Id
	err = c.service.Delete(id)
	if err != nil {
		c.Fail("删除失败：" + err.Error())
		return
	}
	c.Success(nil)
	return
}

// DeleteAll 删除所有消息
func (c *MessageController) DeleteAll() {
	err := c.service.DeleteAll()
	if err != nil {
		c.Fail("删除失败：" + err.Error())
		return
	}
	c.Success(nil)
	return
}

// GetMessage 获取车辆过期提醒、烟雾和二氧化碳报警消息
func (c *MessageController) GetMessage() {
	userInfo, err := c.CheckToken()

	userList, err := c.userService.Child(userInfo, "")

	var ids []int64
	for _, v := range userList {
		ids = append(ids, v.Id)
	}

	if err != nil {
		c.Fail("获取用户信息失败：" + err.Error())
		return
	}

	data, err := c.service.GetMessage(ids)
	if err != nil {
		c.Fail("获取失败：" + err.Error())
		return
	}

	c.Success(data)
	return
}
