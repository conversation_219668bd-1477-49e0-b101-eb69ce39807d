package controllers

import (
	"ccapi/common"
	"ccapi/models/dto"
	"ccapi/service"
	"fmt"
	beego "github.com/beego/beego/v2/server/web"
	"strings"
)

// NotificationController 紧急通知模块
type NotificationController struct {
	BaseController
	service     service.NotificationServiceInter
	userService service.UserServiceInter
}

// InitNotificationRouter 初始化路由
func InitNotificationRouter() beego.LinkNamespace {
	return beego.NSNamespace("/notification",
		beego.NSRouter("/list", &NotificationController{}, "get:List"),                   // 获取紧急通知列表
		beego.NSRouter("/change_status", &NotificationController{}, "post:ChangeStatus"), // 修改紧急通知状态
		beego.NSRouter("/read", &NotificationController{}, "post:Read"),                  // 标记已读
		beego.NSRouter("/read_all", &NotificationController{}, "post:ReadAll"),           // 全部已读
	)
}

// Prepare 注册服务
func (c *NotificationController) Prepare() {
	c.service = service.NewNotificationService()
	c.userService = service.NewUserService()
}

// List 获取紧急通知列表
func (c *NotificationController) List() {
	page, err := c.GetInt("page", 1)
	limit, err := c.GetInt("limit", 10)
	imsi := c.GetString("imsi", "")
	uid, err := c.GetInt("uid", -1)
	name := c.GetString("name", "")
	status, err := c.GetInt("status", -1)
	_type, err := c.GetInt("type", -1)

	userInfo, _ := c.CheckToken()

	userList, err := c.userService.Child(userInfo, "")

	var idsStr []string
	for _, v := range userList {
		idsStr = append(idsStr, fmt.Sprintf("%d", v.Id))
	}

	allId := strings.Join(idsStr, ",")
	data, count, err, unread, read := c.service.List(page, limit, allId, imsi, uid, name, status, _type)

	if err != nil {
		c.Fail("获取列表失败：" + err.Error())
		return
	}

	c.Success(map[string]interface{}{
		"items":  data,
		"total":  count,
		"unread": unread,
		"read":   read,
	})
}

// ChangeStatus 修改紧急通知状态
func (c *NotificationController) ChangeStatus() {
	var (
		err    error
		postId dto.ChangeStatus
	)

	data := c.Ctx.Input.RequestBody
	err = common.Validate(data, &postId)

	if err != nil {
		c.Fail("参数错误," + err.Error())
		return
	}

	id := postId.Id
	status := postId.Status

	err = c.service.ChangeStatus(id, status)
	if err != nil {
		c.Fail("查看失败：" + err.Error())
		return
	}

	c.Success(nil)
}

// Read 标记已读
func (c *NotificationController) Read() {
	var (
		err error
		ids dto.Ids
	)

	data := c.Ctx.Input.RequestBody
	err = common.Validate(data, &ids)

	if err != nil {
		c.Fail("参数错误," + err.Error())
		return
	}

	id := ids.Id
	err = c.service.Read(id)
	if err != nil {
		c.Fail("修改失败：" + err.Error())
		return
	}
	c.Success(nil)
	return
}

// ReadAll 已读所有消息
func (c *NotificationController) ReadAll() {
	userInfo, _ := c.CheckToken()

	err := c.service.ReadAll(userInfo.Id)
	if err != nil {
		c.Fail("修改失败：" + err.Error())
		return
	}
	c.Success(nil)
	return
}
