package controllers

import (
	"ccapi/common"
	"ccapi/models/dto"
	"ccapi/models/robobus_wx"
	"ccapi/pkg"
	"ccapi/service"
	"strconv"
	"time"

	"github.com/beego/beego/v2/core/logs"
	beego "github.com/beego/beego/v2/server/web"
)

// OperationController 运营管理模块
type OperationController struct {
	BaseController
	service      service.OperationServiceInter
	routeService service.RouteServiceInter // 添加路线服务
}

// InitOperationRouter 初始化路由
func InitOperationRouter() beego.LinkNamespace {
	return beego.NSNamespace("/operation",
		beego.NSRouter("/user_list", &OperationController{}, "get:UserList"),            // 获取乘客列表
		beego.NSRouter("/order_list", &OperationController{}, "get:OrderList"),          // 获取订单列表
		beego.NSRouter("/order_list_flat", &OperationController{}, "get:OrderListFlat"), // 获取扁平化订单列表
		beego.NSRouter("/add_user", &OperationController{}, "post:AddUser"),             // 新增乘客
		beego.NSRouter("/delete_user", &OperationController{}, "post:DeleteUser"),       // 删除乘客
		beego.NSRouter("/edit_user", &OperationController{}, "post:EditUser"),           // 修改乘客
		beego.NSRouter("/ticket_list", &OperationController{}, "get:TicketList"),        // 车票列表
		beego.NSRouter("/route_page", &OperationController{}, "get:RoutePage"),          // 获取路线列表
		beego.NSRouter("/add_route", &OperationController{}, "post:AddRoute"),           // 新增路线
		beego.NSRouter("/update_route", &OperationController{}, "post:UpdateRoute"),     // 修改路线
		beego.NSRouter("/delete_route", &OperationController{}, "post:DeleteRoute"),     // 删除路线
		beego.NSRouter("/export_order", &ExportController{}, "post:ExportOrder"),        // 导出订单Excel/PDF
	)
}

// Prepare 注册服务
func (c *OperationController) Prepare() {
	c.service = service.NewOperationService()
	c.routeService = service.NewRouteService(c.Ctx) // 初始化路线服务
}

// UserList 乘客列表
// func (c *OperationController) UserList() {
// 	username := c.GetString("username", "")
// 	page, err := c.GetInt("page", 1)
// 	limit, err := c.GetInt("limit", 10)
//
// 	if err != nil {
// 		c.Fail("参数错误：" + err.Error())
// 		return
// 	}
//
// 	data, count, err := c.service.List(username, page, limit)
// 	if err != nil {
// 		c.Fail("获取列表失败：" + err.Error())
// 		return
// 	}
// 	c.Success(map[string]interface{}{
// 		"data":  data,
// 		"total": count,
// 	})
// 	return
// }

// RouteList 获取路线列表
func (c *OperationController) RoutePage() {
	routeName := c.GetString("routeName", "")
	mapName := c.GetString("mapName", "")
	city := c.GetString("city", "")
	status, _ := c.GetInt("status", -1)

	data, err := c.routeService.List(routeName, mapName, city, status)
	if err != nil {
		c.Fail("获取路线列表失败：" + err.Error())
		return
	}

	c.Success(data)
}

// AddRoute 新增路线
func (c *OperationController) AddRoute() {
	var (
		err      error
		dataForm robobus_wx.AddRoute
	)

	data := c.Ctx.Input.RequestBody
	err = common.Validate(data, &dataForm)
	if err != nil {
		c.Fail("参数错误," + err.Error())
		return
	}

	// 检查路线名称是否重复
	exist := c.routeService.CheckName(dataForm.Name)
	if exist {
		c.Fail("路线名称已存在")
		return
	}

	err = c.routeService.Add(dataForm)
	if err != nil {
		c.Fail("新增路线失败:" + err.Error())
		return
	}

	c.Success(nil)
}

// UpdateRoute 修改路线
func (c *OperationController) UpdateRoute() {
	var (
		err      error
		dataForm robobus_wx.UpdateRoute
	)

	data := c.Ctx.Input.RequestBody
	err = common.Validate(data, &dataForm)
	if err != nil {
		c.Fail("参数错误," + err.Error())
		return
	}

	// 检查路线名称是否重复
	exist := c.routeService.CheckName(dataForm.Name)
	if exist {
		c.Fail("路线名称已存在")
		return
	}

	err = c.routeService.Update(dataForm)
	if err != nil {
		c.Fail("修改路线失败:" + err.Error())
		return
	}

	c.Success(nil)
}

// DeleteRoute 删除路线
func (c *OperationController) DeleteRoute() {
	var (
		err      error
		dataForm dto.PostId
	)

	data := c.Ctx.Input.RequestBody
	err = common.Validate(data, &dataForm)
	if err != nil {
		c.Fail("参数错误," + err.Error())
		return
	}

	err = c.routeService.Delete(dataForm.Id)
	if err != nil {
		c.Fail("删除路线失败:" + err.Error())
		return
	}

	c.Success(nil)
}

func (c *OperationController) UserList() {
	nickname := c.GetString("nickname", "")
	openID := c.GetString("openID", "")
	mobile := c.GetString("mobile", "")
	// createdTime, err := c.GetInt("registerTime")
	// 获取registerTime参数
	registerTimes := c.GetStrings("registerTime[]")

	// 定义开始和结束时间变量
	var registerTimeBegin, registerTimeEnd int64

	// 检查是否有两个有效的时间参数
	if len(registerTimes) == 2 {
		var err error
		registerTimeBegin, err = strconv.ParseInt(registerTimes[0], 10, 64)
		if err != nil {
			c.Fail("开始时间参数解析错误：" + err.Error())
			return
		}
		registerTimeEnd, err = strconv.ParseInt(registerTimes[1], 10, 64)
		if err != nil {
			c.Fail("结束时间参数解析错误：" + err.Error())
			return
		}

		// 确保开始时间小于等于结束时间
		if registerTimeBegin > registerTimeEnd {
			c.Fail("开始时间不能大于结束时间")
			return
		}
	} else if len(registerTimes) > 0 {
		// 如果提供了参数但不是两个，提示错误
		c.Fail("请提供两个时间参数")
		return
	}

	// 获取lastLoginTime参数
	lastLoginTimes := c.GetStrings("lastLoginTime[]")

	// 定义开始和结束时间变量
	var lastLoginTimeBegin, lastLoginTimeEnd int64

	// 检查是否有两个有效的时间参数
	if len(lastLoginTimes) == 2 {
		var err error
		lastLoginTimeBegin, err = strconv.ParseInt(lastLoginTimes[0], 10, 64)
		if err != nil {
			c.Fail("最后登录开始时间参数解析错误：" + err.Error())
			return
		}
		lastLoginTimeEnd, err = strconv.ParseInt(lastLoginTimes[1], 10, 64)
		if err != nil {
			c.Fail("最后登录结束时间参数解析错误：" + err.Error())
			return
		}

		// 确保开始时间小于等于结束时间
		if lastLoginTimeBegin > lastLoginTimeEnd {
			c.Fail("最后登录开始时间不能大于结束时间")
			return
		}
	} else if len(lastLoginTimes) > 0 {
		// 如果提供了参数但不是两个，提示错误
		c.Fail("请提供两个最后登录时间参数")
		return
	}

	// updatedTime, err := c.GetInt("lastLoginTime")
	page, err := c.GetInt("page", 1)
	limit, err := c.GetInt("limit", 10)

	if err != nil {
		c.Fail("参数错误：" + err.Error())
		return
	}

	data, count, err := c.service.List(nickname, openID, mobile, registerTimeBegin, registerTimeEnd, lastLoginTimeBegin, lastLoginTimeEnd, page, limit)
	if err != nil {
		c.Fail("获取列表失败：" + err.Error())
		return
	}
	c.Success(map[string]interface{}{
		"data":  data,
		"total": count,
	})

	return
}

// 注意：OrderListQuery 和 OrderResult 结构体已移至 models/dto/OrderResult.go
// 这里删除重复定义，直接使用 dto.OrderListQuery 和 dto.OrderResult

// OrderList 订单列表
func (c *OperationController) OrderList() {
	// 获取createdTime参数
	createdTimes := c.GetStrings("createdTime[]")

	// 调试日志：显示获取到的数组参数
	logs.Debug("订单接口获取到的数组参数: createdTimes=%v", createdTimes)

	var query dto.OrderListQuery
	err := pkg.UnmarshalQuery(c.GetString, &query)
	if err != nil {
		return
	}

	// 定义开始和结束时间变量
	var createdTimeBegin, createdTimeEnd int64

	// 检查是否有两个有效的时间参数
	if len(createdTimes) == 2 {
		var err error
		createdTimeBegin, err = strconv.ParseInt(createdTimes[0], 10, 64)
		if err != nil {
			c.Fail("创建时间开始参数解析错误：" + err.Error())
			return
		}
		createdTimeEnd, err = strconv.ParseInt(createdTimes[1], 10, 64)
		if err != nil {
			c.Fail("创建时间结束参数解析错误：" + err.Error())
			return
		}

		// 确保开始时间小于等于结束时间
		if createdTimeBegin > createdTimeEnd {
			c.Fail("创建时间开始不能大于结束时间")
			return
		}

		query.CreatedTimeBegin = createdTimeBegin
		query.CreatedTimeEnd = createdTimeEnd
	} else if len(createdTimes) > 0 {
		// 如果提供了参数但不是两个，提示错误
		c.Fail("请提供两个创建时间参数")
		return
	}

	// 获取当前登录用户id
	userInfo, err := c.CheckToken()
	if err != nil {
		c.Fail("用户身份校验失败：" + err.Error())
		return
	}
	userId := userInfo.Id

	data, err := c.service.OrderList(&query, c.Ctx, userId)
	if err != nil {
		c.Fail("获取订单列表失败：" + err.Error())
		return
	}

	c.Success(data)
}

// OrderListFlat 扁平化订单列表（将ticketItems展开为多条记录）
func (c *OperationController) OrderListFlat() {
	// 获取createdTime参数
	createdTimes := c.GetStrings("createdTime[]")

	// 调试日志：显示获取到的数组参数
	logs.Debug("扁平化订单接口获取到的数组参数: createdTimes=%v", createdTimes)

	var query dto.OrderListQuery
	err := pkg.UnmarshalQuery(c.GetString, &query)
	if err != nil {
		return
	}

	// 处理时间参数
	if len(createdTimes) >= 2 {
		// 解析开始时间
		if createdTimes[0] != "" {
			startTime, err := time.Parse("2006-01-02 15:04:05", createdTimes[0])
			if err != nil {
				logs.Error("解析开始时间失败: %v", err)
			} else {
				query.CreatedTimeBegin = startTime.Unix()
				logs.Debug("解析开始时间成功: %s -> %d", createdTimes[0], query.CreatedTimeBegin)
			}
		}

		// 解析结束时间
		if createdTimes[1] != "" {
			endTime, err := time.Parse("2006-01-02 15:04:05", createdTimes[1])
			if err != nil {
				logs.Error("解析结束时间失败: %v", err)
			} else {
				query.CreatedTimeEnd = endTime.Unix()
				logs.Debug("解析结束时间成功: %s -> %d", createdTimes[1], query.CreatedTimeEnd)
			}
		}
	}

	logs.Debug("扁平化订单接口最终查询参数: %+v", query)

	// 获取当前登录用户id
	userInfo, err := c.CheckToken()
	if err != nil {
		c.Fail("用户身份校验失败：" + err.Error())
		return
	}
	userId := userInfo.Id

	data, err := c.service.OrderListFlat(&query, c.Ctx, userId)
	if err != nil {
		c.Fail("获取扁平化订单列表失败：" + err.Error())
		return
	}

	c.Success(data)
}

// AddUser 新增乘客
func (c *OperationController) AddUser() {
	var (
		err      error
		dataForm dto.AddWrcUser
	)

	data := c.Ctx.Input.RequestBody
	err = common.Validate(data, &dataForm)

	if err != nil {
		c.Fail("参数错误," + err.Error())
		return
	}

	username := dataForm.Username
	nickname := dataForm.Nickname
	gender := dataForm.Gender
	birthday := dataForm.Birthday
	email := dataForm.Email
	bio := dataForm.Bio
	country := dataForm.Country
	registerType := dataForm.RegisterType

	exist := c.service.CheckName(username, 0)
	if exist {
		c.Fail("用户名重复")
		return
	}

	err = c.service.AddUser(username, nickname, gender, birthday, email, bio, country, registerType)

	if err != nil {
		c.Fail("新增失败:" + err.Error())
		return
	}
	c.Success(nil)
	return
}

// EditUser 修改乘客
func (c *OperationController) EditUser() {
	var (
		err      error
		dataForm dto.EditWrcUser
	)

	data := c.Ctx.Input.RequestBody
	err = common.Validate(data, &dataForm)

	if err != nil {
		c.Fail("参数错误," + err.Error())
		return
	}

	id := dataForm.Id
	nickname := dataForm.Nickname
	gender := dataForm.Gender
	birthday := dataForm.Birthday
	email := dataForm.Email
	bio := dataForm.Bio
	country := dataForm.Country
	registerType := dataForm.RegisterType

	err = c.service.EditUser(id, nickname, gender, birthday, email, bio, country, registerType)

	if err != nil {
		c.Fail("修改失败：" + err.Error())
		return
	}
	c.Success(nil)
	return
}

// DeleteUser 删除乘客
func (c *OperationController) DeleteUser() {
	var (
		err      error
		dataForm dto.PostId
	)

	data := c.Ctx.Input.RequestBody
	err = common.Validate(data, &dataForm)

	if err != nil {
		c.Fail("参数错误," + err.Error())
		return
	}

	id := dataForm.Id
	err = c.service.DeleteUser(id)
	if err != nil {
		c.Fail("删除失败：" + err.Error())
		return
	}

	c.Success(nil)
	return
}

// TicketList 车票列表
func (c *OperationController) TicketList() {
	username := c.GetString("username", "")
	ticketNo := c.GetString("ticket_no", "")
	vehicle := c.GetString("vehicle", "")
	page, err := c.GetInt("page", 1)
	limit, err := c.GetInt("limit", 10)

	if err != nil {
		c.Fail("参数错误")
		return
	}

	data, count, err := c.service.TicketList(username, ticketNo, vehicle, page, limit)

	if err != nil {
		c.Fail("获取列表失败：" + err.Error())
		return
	}

	c.Success(map[string]interface{}{
		"data":  data,
		"total": count,
	})
	return
}
