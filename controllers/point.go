package controllers

import (
	"ccapi/service"
	beego "github.com/beego/beego/v2/server/web"
)

// PointController 区域模块
type PointController struct {
	BaseController
	service service.PointServiceInter
}

// InitPointRouter 初始化路由
func InitPointRouter() beego.LinkNamespace {
	return beego.NSNamespace("/point",
		beego.NSRouter("/list", &PointController{}, "get:List"), // 获取区域列表
	)
}

// Prepare 注册服务
func (c *PointController) Prepare() {
	c.service = service.NewPointService()
}

// List 获取区域列表
func (c *PointController) List() {
	page, err := c.GetInt("page", 1)
	limit, err := c.GetInt("limit", 10)

	if err != nil {
		c.Fail("参数错误：" + err.Error())
		return
	}

	data, count, err := c.service.List(page, limit)
	if err != nil {
		c.Fail("获取失败：" + err.Error())
		return
	}
	c.Success(map[string]interface{}{
		"items": data,
		"total": count,
	})
}
