package controllers

import (
	"ccapi/common"
	"ccapi/models/dto"
	"ccapi/service"
	"fmt"
	beego "github.com/beego/beego/v2/server/web"
)

// RecordSignalController 心跳日志模块
type RecordSignalController struct {
	BaseController
	service service.RecordSignalServiceInter
}

// InitRecordSignalRouter 初始化路由
func InitRecordSignalRouter() beego.LinkNamespace {
	return beego.NSNamespace("/signal_record",
		beego.NSRouter("/list", &RecordSignalController{}, "get:List"),      // 获取心跳日志列表
		beego.NSRouter("/delete", &RecordSignalController{}, "post:Delete"), // 删除日志
	)
}

// Prepare 注册服务
func (c *RecordSignalController) Prepare() {
	c.service = service.NewRecordSignalService()
}

// List 获取心跳日志列表
func (c *RecordSignalController) List() {
	imsi := c.GetString("imsi", "")
	beginTime, err := c.GetInt("begin_time", 0)
	endTime, err := c.GetInt("end_time", 0)
	page, err := c.GetInt("page", 1)
	limit, err := c.GetInt("limit", 10)
	paging, err := c.GetInt("paging", 1)
	order, err := c.GetInt("order", 0)

	if err != nil {
		c.Fail("参数错误：" + err.Error())
		return
	}

	data, notification, total, count, tm, err := c.service.HeartBeatLog(imsi, page, limit, beginTime, endTime, paging, order)
	if err != nil {
		c.Fail("获取数据失败：" + err.Error())
		return
	}

	c.Success(map[string]interface{}{
		"data":         data,
		"total":        total,
		"notification": notification,
		"count":        count,
		"tm":           tm,
	})
	return
}

// Delete 删除心跳日志
func (c *RecordSignalController) Delete() {
	var (
		err        error
		deleteData dto.DeleteData
	)

	data := c.Ctx.Input.RequestBody
	err = common.Validate(data, &deleteData)

	if err != nil {
		c.Fail("参数错误," + err.Error())
		return
	}

	all := deleteData.All
	id := deleteData.Id
	userInfo, _ := c.CheckToken()

	err = c.service.Delete(all, id)
	if err != nil {
		c.Fail("删除失败：" + err.Error())
		return
	}

	// 操作日志
	logType := "删除终端信号记录"
	logMsg := fmt.Sprintf("%s(%d)",
		logType,
		id,
	)
	if e := c.AddOperationLog(userInfo, logType, 2, "", logMsg, ""); e != nil {
		c.Fail(fmt.Sprintf(AddLogFailed, logType, e))
		return
	}

	c.Success(nil)
	return
}
