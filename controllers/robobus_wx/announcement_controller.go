// controller/announcement_service.go

package robobus_wx

import (
	"ccapi/controllers"
	"ccapi/models/dto"
	"ccapi/models/robobus_wx"
	"ccapi/pkg"
	robobus_wx_service "ccapi/service/robobus_wx"
	beego "github.com/beego/beego/v2/server/web"
	"strings"
)

type AnnouncementController struct {
	controllers.BaseController
	announcementService robobus_wx_service.AnnouncementServiceInter // 使用接口类型
}

// Prepare 注册服务
func (c *AnnouncementController) Prepare() {
	c.announcementService = robobus_wx_service.NewAnnouncementService()
}

// Page 列表查询
func (c *AnnouncementController) Page() {
	var req dto.ListAnnouncementReq
	if err := pkg.UnmarshalQuery(c.GetString, &req); err != nil {
		c.Fail(err.Error())
		return
	}

	page, err := c.announcementService.Page(req, c.Ctx)
	if err != nil {
		c.Fail(err.Error())
		return
	}
	c.Success(page)
}

// Save 新增或更新
func (c *AnnouncementController) Save() {
	var req dto.AnnouncementCreateDTO
	err := pkg.Unmarshal(c.Ctx.Input.RequestBody, &req)
	if err != nil {
		c.Fail(err.Error())
		return
	}

	// 参数验证
	if err := req.Validate(); err != nil {
		c.Fail(err.Error())
		return
	}

	// 转换为 PO 对象
	announcement := &robobus_wx.Announcement{
		Title:     req.Title,
		Content:   req.Content,
		StartTime: req.StartTime, // 直接使用 Time 类型
		EndTime:   req.EndTime,   // 直接使用 Time 类型
		Images:    strings.Join(req.Images, ","),
		Status:    1, // 默认启用
	}

	err = c.announcementService.Save(announcement)
	if err != nil {
		c.Fail(err.Error())
		return
	}

	c.Success(nil)
}

// Update 更新公告
func (c *AnnouncementController) Update() {
	var req dto.AnnouncementUpdateDTO
	err := pkg.Unmarshal(c.Ctx.Input.RequestBody, &req)
	if err != nil {
		c.Fail(err.Error())
		return
	}

	// 参数验证
	if err := req.Validate(); err != nil {
		c.Fail(err.Error())
		return
	}

	// 转换为 PO 对象
	announcement := &robobus_wx.Announcement{
		ID:        req.ID, // 更新需要 ID
		Title:     req.Title,
		Content:   req.Content,
		StartTime: req.StartTime,
		EndTime:   req.EndTime,
		Images:    strings.Join(req.Images, ","),
		Status:    req.Status,
	}

	err = c.announcementService.Update(announcement)
	if err != nil {
		c.Fail(err.Error())
		return
	}

	c.Success(nil)
}

func (c *AnnouncementController) Delete() {
	id, _ := c.GetInt64("id")

	err := c.announcementService.Delete(id)
	if err != nil {
		c.Fail(err.Error())
		return
	}

	c.Success(nil)
}

func InitAnnouncementRouter() beego.LinkNamespace {
	return beego.NSNamespace("/operation/announcement",
		beego.NSRouter("/save", &AnnouncementController{}, "post:Save"),       // 获取乘客列表
		beego.NSRouter("/update", &AnnouncementController{}, "put:Update"),    // 获取乘客列表
		beego.NSRouter("/delete", &AnnouncementController{}, "delete:Delete"), // 获取乘客列表
		beego.NSRouter("/page", &AnnouncementController{}, "get:Page"),        // 获取乘客列表
	)
}
