package robobus_wx

import (
	"ccapi/common"
	"ccapi/controllers"
	"ccapi/models/robobus_wx"
	"ccapi/pkg"
	"ccapi/pkg/orm_helper"
	"encoding/json"
	"errors"
	"github.com/beego/beego/v2/client/orm"
	beego "github.com/beego/beego/v2/server/web"
	"strconv"
	"time"
)

type CouponController struct {
	controllers.BaseController
}

// CouponTemplatePage 获取所有优惠券模板
func (c *CouponController) CouponTemplatePage() {

	// 解析查询参数
	var query robobus_wx.CouponTemplateQuery

	status, _ := c.GetInt("status", -1)
	id, _ := c.GetInt64("id", -1)
	typeN, _ := c.GetInt("type", -1)
	query.Status = status
	query.Id = id
	query.Type = typeN
	createdTime, _ := c.GetInt64("createdTime", 0)
	query.CreatedTime = createdTime

	o := orm.NewOrmUsingDB("robobusWX")

	sql := `SELECT 
    id, 
    name, 
    type, 
    discount_value, 
    min_amount, 
    description, 
    status, 
    UNIX_TIMESTAMP(created_time) AS created_time,
    UNIX_TIMESTAMP(updated_time) AS updated_time from coupon_template where is_delete = 0`

	page, err := orm_helper.NewQueryBuilder[robobus_wx.CouponTemplateDto](o, c.Ctx, sql).Eq("status", query.Status).Eq("id", query.Id).Eq("type", query.Type).Like("name", query.Name).
		GeIf(query.CreatedTime > 0, "created_time", time.Unix(query.CreatedTime, 0)).Page()
	if err != nil {
		c.Fail(err.Error())
	} else {
		c.Success(page)
	}
}

// Get 获取单个优惠券模板
func (c *CouponController) Get() {
	idStr := c.Ctx.Input.Param(":id")
	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		c.Fail("Invalid ID")
		return
	}

	o := orm.NewOrm()
	coupon := robobus_wx.CouponTemplateDto{Id: id}
	err = o.Read(&coupon)
	if err != nil {
		c.Fail(err.Error())
	} else {
		c.Success(coupon)
	}
}

// AddCouponTemplate  创建优惠券模板
func (c *CouponController) AddCouponTemplate() {
	var couponDto robobus_wx.CouponTemplateDto
	if err := json.Unmarshal(c.Ctx.Input.RequestBody, &couponDto); err != nil {
		c.Fail(err.Error())
		return
	}

	coupon := robobus_wx.CouponTemplate{
		Status:        1,
		Type:          couponDto.Type,
		Name:          couponDto.Name,
		DiscountValue: couponDto.DiscountValue,
		Description:   couponDto.Description,
		MinAmount:     couponDto.MinAmount,
		CreatedTime:   time.Now(),
		UpdatedTime:   time.Now(),
	}

	o := orm.NewOrmUsingDB("robobusWX")
	_, err := o.Insert(&coupon)
	if err != nil {
		c.Fail(err.Error())
	} else {
		c.Success(coupon.Id)
	}
}

// EditCouponTemplate 编辑优惠券模板
func (c *CouponController) EditCouponTemplate() {
	var updateData robobus_wx.CouponTemplateDto
	if err := pkg.Unmarshal(c.Ctx.Input.RequestBody, &updateData); err != nil {
		c.Fail("请求数据格式错误: " + err.Error())
		return
	}

	// 检查 ID 是否有效
	if updateData.Id <= 0 {
		c.Fail("无效的ID")
		return
	}

	o := orm.NewOrmUsingDB("robobusWX")

	// 先获取现有数据
	existingCoupon := robobus_wx.CouponTemplate{Id: updateData.Id}
	if err := o.Read(&existingCoupon); err != nil {
		c.Fail("优惠券模板不存在")
		return
	}
	existingCoupon.MinAmount = updateData.MinAmount
	existingCoupon.Description = updateData.Description
	existingCoupon.DiscountValue = updateData.DiscountValue
	existingCoupon.Type = updateData.Type
	existingCoupon.Name = updateData.Name

	if updateData.Status != common.Invalid {
		existingCoupon.Status = updateData.Status
	}

	// 更新指定字段
	_, err := o.Update(&existingCoupon)
	if err != nil {
		c.Fail("更新失败: " + err.Error())
		return
	}

	c.Success(nil)
}

// Delete 删除优惠券模板（软删除）
func (c *CouponController) Delete() {
	// 从 URL 查询参数中获取 id
	id, _ := c.GetInt64("id", -1)
	if id == common.Invalid {
		c.Fail("ID不能为空")
		return
	}

	o := orm.NewOrmUsingDB("robobusWX")

	// 先查询记录是否存在
	template := &robobus_wx.CouponTemplate{Id: id}
	if err := o.Read(template); err != nil {
		if errors.Is(err, orm.ErrNoRows) {
			c.Fail("优惠券模板不存在")
		} else {
			c.Fail(err.Error())
		}
		return
	}

	// 更新 is_delete 字段为已删除状态
	template.IsDelete = common.IsDeleted
	num, err := o.Update(template, "IsDelete")
	if err != nil {
		c.Fail(err.Error())
		return
	}
	if num == 0 {
		c.Fail("删除失败")
		return
	}

	c.Success(id)
}

// 分页响应结构体
type PagedResponse struct {
	Total int                       `json:"total"`
	List  []robobus_wx.UserCouponVO `json:"list"`
	Page  int                       `json:"page"`
	Limit int                       `json:"limit"`
}

func (c *CouponController) UserCouponPage() {
	// 初始化查询参数
	params := robobus_wx.UserCouponQuery{}

	// 解析查询参数
	if err := c.ParseForm(&params); err != nil {
		c.Fail("参数解析失败: " + err.Error())
		return
	}

	if params.Limit == 0 {
		params.Limit = 10
	}
	if params.Page == 0 {
		params.Page = 1
	}
	// 构建基础SQL
	sql := `SELECT
       uc.id,
       uc.coupon_activity_id as coupon_id,
       ct.id as template_id,
       uc.user_id,
       wu.mobile,
       ct.name as template_name,
       o.id as order_id,
       uc.status,
       ct.type as discount_type,
       ct.min_amount,
       ct.discount_value,
       UNIX_TIMESTAMP(uc.start_time) as start_time,
       UNIX_TIMESTAMP(uc.end_time) as end_time,
       uc.num,
       UNIX_TIMESTAMP(uc.created_time) as created_time,
       UNIX_TIMESTAMP(uc.updated_time) as updated_time
   FROM
       user_coupon uc
       LEFT JOIN coupon_activity_relation car ON uc.coupon_activity_id = car.id
       LEFT JOIN coupon_template ct ON ct.id = car.template_id
       LEFT JOIN wx_user wu ON wu.id = uc.user_id
       LEFT JOIN ` + "`order`" + ` o ON o.wx_id = uc.user_id AND o.coupon_id = uc.coupon_activity_id
   WHERE 1=1`

	// 动态添加查询条件
	var args []interface{}

	if params.Mobile != "" {
		sql += " AND wu.mobile LIKE ?"
		args = append(args, "%"+params.Mobile+"%")
	}

	if params.Status > 0 {
		sql += " AND uc.status = ?"
		args = append(args, params.Status)
	}

	if params.CouponId > 0 {
		sql += " AND uc.coupon_activity_id = ?"
		args = append(args, params.CouponId)
	}

	if params.TemplateName != "" {
		sql += " AND ct.name LIKE ?"
		args = append(args, "%"+params.TemplateName+"%")
	}

	if params.DiscountType > 0 {
		sql += " AND ct.type = ?"
		args = append(args, params.DiscountType)
	}

	if params.OrderId > 0 {
		sql += " AND o.id = ?"
		args = append(args, params.OrderId)
	}

	sql += `
   GROUP BY uc.id
   ORDER BY created_time DESC`

	o := orm.NewOrmUsingDB("robobusWX")
	var coupons []robobus_wx.UserCouponVO

	_, err := o.Raw(sql, args...).QueryRows(&coupons)
	if err != nil {
		c.Fail("查询失败: " + err.Error())
		return
	}

	// 构建分页响应
	pageData := struct {
		Total int                       `json:"total"`
		List  []robobus_wx.UserCouponVO `json:"list"`
		Page  int                       `json:"page"`
		Limit int                       `json:"limit"`
	}{
		Total: len(coupons),
		List:  coupons,
		Page:  params.Page,
		Limit: params.Limit,
	}

	// 返回分页结果
	c.Success(pageData)
}

func InitCoupon() beego.LinkNamespace {
	return beego.NSNamespace("/operation",
		beego.NSRouter("/add_coupon_template", &CouponController{}, "post:AddCouponTemplate"),  // 获取乘客列表
		beego.NSRouter("/coupon_template_page", &CouponController{}, "get:CouponTemplatePage"), // 获取乘客列表
		beego.NSRouter("/edit_coupon_template", &CouponController{}, "put:EditCouponTemplate"),
		beego.NSRouter("/delete_coupon_template", &CouponController{}, "delete:Delete"),
		beego.NSRouter("/user_coupon_page", &CouponController{}, "get:UserCouponPage"),
	)
}
