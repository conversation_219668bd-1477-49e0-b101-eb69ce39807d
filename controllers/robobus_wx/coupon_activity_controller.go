package robobus_wx

import (
	"ccapi/common"
	"ccapi/controllers"
	"ccapi/models/robobus_wx"
	"ccapi/pkg"
	"ccapi/pkg/orm_helper"
	"ccapi/pkg/types"
	rs "ccapi/service/robobus_wx"
	"encoding/json"
	"errors"
	"fmt"
	"time"

	"github.com/beego/beego/v2/client/orm"
	"github.com/beego/beego/v2/core/logs"
	beego "github.com/beego/beego/v2/server/web"
	"github.com/go-playground/validator"
)

type CouponActivityController struct {
	controllers.BaseController
	activityService *rs.CouponActivityService
}

// CouponActivityPage 分页查询活动
func (c *CouponActivityController) CouponActivityPage() {
	var query robobus_wx.CouponActivityQuery
	err := pkg.UnmarshalQuery(c.GetString, &query)
	if err != nil {
		c.Fail(err.Error())
		return
	}

	o := orm.NewOrmUsingDB("robobusWX")

	sql := `SELECT 
        ca.id,
        ca.status,
        ca.name,
        ca.description,
        ct.name as coupon_name,
        ca.start_time,
        ca.end_time,
        ca.created_time,
        ca.updated_time
    FROM coupon_activity ca 
    LEFT JOIN coupon_activity_relation car ON ca.id = car.activity_id AND car.is_delete = 0
    LEFT JOIN coupon_template ct ON car.template_id = ct.id AND ct.is_delete = 0
    where ca.is_delete = 0
    `

	qb := orm_helper.NewQueryBuilder[robobus_wx.CouponActivityVO](o, c.Ctx, sql)
	page, err := qb.Where().
		Eq("ca.id", query.Id).
		Like("ca.name", query.Name).
		Like("ct.name", query.CouponName).
		Le("ca.start_time", query.StartTime).
		Ge("ca.end_time", query.EndTime).
		Eq("ca.status", query.Status).
		Page()
	if err != nil {
		c.Fail(err.Error())
		return // 添加return语句
	}

	c.Success(page)
}

func (c *CouponActivityController) GetActivityDetail() {
	couponActivityId, _ := c.GetInt64("activityId", 0)
	if couponActivityId == 0 {
		c.Fail("couponActivityId 不能为空")
		return
	}

	// 主活动查询SQL
	mainSql := `select 
                id, status, 
                start_time,  -- 直接查询时间字段
                end_time,    -- 直接查询时间字段
                name, description 
                from coupon_activity 
                where id = ? and is_delete = 0`

	// 优惠券查询SQL
	couponSql := `select 
                 ca.id, ca.status, ca.start_time, ca.end_time,
                 ca.name, ca.description, 
                 car.id as coupon_id, carr.region_id as region_id, carr.route_id as route_id,
                 ct.id as coupon_template_id, ct.name as coupon_name, 
                 cgr.grant_mode as grant_mode,
                 cgr.receive_period as receive_period, cgr.receive_limit as receive_limit,
                 cgr.total_quantity as total_quantity, cgr.valid_days as valid_days,
                 cgr.remaining_quantity as remaining_quantity,
                 (cgr.total_quantity-cgr.remaining_quantity) as sended_quantity,
                 car.status as status
                 from coupon_activity ca 
                 left join coupon_activity_relation car on ca.id = car.activity_id and car.is_delete = 0
                 left join coupon_activity_restriction carr on car.id = carr.coupon_activity_id and carr.is_delete = 0
                 left join coupon_template ct on ct.id = car.template_id and ct.is_delete = 0
                 left join coupon_grant_rule cgr on cgr.coupon_activity_id = car.id and cgr.is_delete = 0
                 where ca.id = ? and ca.is_delete = 0`

	db := orm.NewOrmUsingDB("robobusWX")

	// 1. 查询主活动信息
	var (
		id          int64
		status      int
		startTime   time.Time
		endTime     time.Time
		name        string
		description string
	)

	err := db.Raw(mainSql, couponActivityId).QueryRow(&id, &status, &startTime, &endTime, &name, &description)
	if err != nil {
		c.Fail(err.Error())
		return
	}

	// 2. 手动构造活动详情对象，时间戳格式转换
	activityDetail := robobus_wx.CouponActivityDetailVO{
		Id:          id,
		Status:      status,
		Name:        name,
		Description: description,
		StartTime:   types.MyUnixDateTime{MyTimestamp: startTime}, // 正确初始化MyUnixDateTime
		EndTime:     types.MyUnixDateTime{MyTimestamp: endTime},   // 正确初始化MyUnixDateTime
		Coupons:     make([]*robobus_wx.CouponDetailVO, 0),        // 初始化空切片
	}

	// 3. 查询关联的优惠券信息
	var coupons []*robobus_wx.CouponDetailVO
	_, err = db.Raw(couponSql, couponActivityId).QueryRows(&coupons)
	if err != nil {
		c.Fail(err.Error())
		return
	}

	// 4. 设置关联的优惠券
	activityDetail.Coupons = coupons

	c.Success(&activityDetail)
}

func (c *CouponActivityController) EditCouponStatus() {
	var req robobus_wx.EditCouponStatusReq
	if err := json.Unmarshal(c.Ctx.Input.RequestBody, &req); err != nil {
		c.Fail("参数解析失败")
		return
	}

	// 参数校验
	validate := validator.New()
	if err := validate.Struct(&req); err != nil {
		c.Fail(err.Error())
		return
	}

	// 执行更新
	o := orm.NewOrmUsingDB("robobusWX")
	sql := "UPDATE coupon_activity_relation SET status = ? WHERE id = ?"
	_, err := o.Raw(sql, req.Status, req.CouponId).Exec()
	if err != nil {
		logs.Error("修改优惠券状态失败:", err)
		c.Fail("修改优惠券状态失败")
		return
	}

	c.Success(nil)
}

// AddCouponActivity 新建活动
func (c *CouponActivityController) AddCouponActivity() {
	var addActivity robobus_wx.AddCouponActivity
	if err := json.Unmarshal(c.Ctx.Input.RequestBody, &addActivity); err != nil {
		c.Fail(err.Error())
		return
	}

	o := orm.NewOrmUsingDB("robobusWX")
	tx, err := o.Begin() // 开始事务
	if err != nil {
		c.Fail("无法开始事务")
		return
	}

	defer func() {
		if err != nil {
			rollbackErr := tx.Rollback() // 回滚事务
			if rollbackErr != nil {
				logs.Error("回滚事务失败:", rollbackErr)
			}
			if err.Error() != "" {
				c.Fail(err.Error())
			} else {
				c.Fail("操作失败")
			}
		} else {
			commitErr := tx.Commit() // 提交事务
			if commitErr != nil {
				logs.Error("提交事务失败:", commitErr)
				c.Fail("提交事务失败")
				return
			}
			c.Success(nil)
		}
	}()

	qs := tx.QueryTable(new(robobus_wx.CouponActivity))
	count, err := qs.Filter("name", addActivity.Name).Count()
	if err != nil {
		return
	}
	if count > 0 {
		err = fmt.Errorf("活动已存在")
		return
	}

	// 创建活动
	activity := robobus_wx.CouponActivity{
		Name:        addActivity.Name,
		Description: addActivity.Description,
		StartTime:   time.Unix(addActivity.StartTime, 0),
		EndTime:     time.Unix(addActivity.EndTime, 0),
	}
	_, err = tx.Insert(&activity)
	if err != nil {
		return
	}

	for _, coupon := range addActivity.Coupons {
		// 创建关联关系
		var couponActivityRelation robobus_wx.CouponActivityRelation
		err := tx.QueryTable("coupon_activity_relation").Filter("template_id", coupon.CouponTemplateId).Filter("activity_id", activity.Id).One(&couponActivityRelation)
		if err != nil && !errors.Is(err, orm.ErrNoRows) {
			c.Fail(err.Error())
			return
		}
		if err != nil && errors.Is(err, orm.ErrNoRows) {
			couponActivityRelation = robobus_wx.CouponActivityRelation{
				ActivityId:  activity.Id,
				TemplateId:  coupon.CouponTemplateId,
				Status:      1,
				CreatedTime: time.Now(),
				UpdatedTime: time.Now(),
			}
			_, err = tx.Insert(&couponActivityRelation)
			if err != nil {
				c.Fail(err.Error())
				return
			}
		}

		// 创建发放限制
		couponGrantRule := robobus_wx.CouponGrantRule{
			CouponActivityId:  couponActivityRelation.Id,
			GrantMode:         2,
			ReceiveLimit:      coupon.ReceiveLimit,
			ReceivePeriod:     coupon.ReceivePeriod,
			ValidDays:         coupon.ValidDays,
			TotalQuantity:     coupon.TotalQuantity,
			Status:            1,
			CreatedTime:       time.Now(),
			UpdatedTime:       time.Now(),
			RemainingQuantity: coupon.TotalQuantity, //保证才新增的时候剩余数量和发放总量应该是相等的
		}
		_, err = tx.Insert(&couponGrantRule)
		if err != nil {
			return
		}

		// 创建区域限制
		couponActivityRestriction := robobus_wx.CouponActivityRestriction{
			CouponActivityId: couponActivityRelation.Id,
			RegionId:         coupon.RegionId,
			RouteId:          coupon.RouteId,
			Status:           1,
			CreatedTime:      time.Now(),
			UpdatedTime:      time.Now(),
		}
		_, err = tx.Insert(&couponActivityRestriction)
		if err != nil {
			return
		}
	}
	c.Success(activity.Id)
}

// EditCouponActivity 编辑活动
func (c *CouponActivityController) EditCouponActivity() {
	var updateActivity robobus_wx.AddCouponActivity
	if err := pkg.Unmarshal(c.Ctx.Input.RequestBody, &updateActivity); err != nil {
		c.Fail(err.Error())
		return
	}
	if updateActivity.Id == 0 {
		c.Fail("id不能为空")
		return
	}

	o := orm.NewOrmUsingDB("robobusWX")
	tx, err := o.Begin() // 开始事务
	if err != nil {
		c.Fail("无法开始事务")
		return
	}

	var txErr error
	defer func() {
		if r := recover(); r != nil {
			_ = tx.Rollback()
			panic(r)
		}

		if txErr != nil {
			logs.Error("事务执行失败: %v", txErr)
			_ = tx.Rollback()
			c.Fail(txErr.Error())
		} else {
			if err := tx.Commit(); err != nil {
				logs.Error("事务提交失败: %v", err)
				_ = tx.Rollback()
				c.Fail(fmt.Sprintf("提交事务失败: %v", err))
			} else {
				logs.Info("事务提交成功")
				c.Success(nil)
			}
		}
	}()

	// 1. 检查活动名称重复
	existingActivity, err := c.activityService.CheckNameDuplicate(tx, updateActivity.Name, updateActivity.Id)
	if err != nil {
		txErr = err
		c.Fail(err.Error())
		return
	}
	if existingActivity != nil {
		txErr = fmt.Errorf("活动名称 '%s' 已存在", updateActivity.Name)
		c.Fail(txErr.Error())
		return
	}

	// 2. 更新活动基本信息
	activity := robobus_wx.CouponActivity{}
	err = tx.QueryTable(new(robobus_wx.CouponActivity)).
		Filter("id", updateActivity.Id).
		Filter("is_delete", common.IsNotDeleted).
		One(&activity)
	if err != nil {
		if errors.Is(err, orm.ErrNoRows) {
			txErr = fmt.Errorf("找不到ID为%d的有效活动", updateActivity.Id)
		} else {
			txErr = fmt.Errorf("查询活动失败: %v", err)
		}
		c.Fail(txErr.Error())
		return
	}

	// 检查活动时间
	if activity.StartTime.Before(time.Now()) {
		txErr = fmt.Errorf("活动已开始，无法修改")
		c.Fail(txErr.Error())
		return
	}

	activity.Name = updateActivity.Name
	activity.Description = updateActivity.Description
	activity.StartTime = time.Unix(updateActivity.StartTime, 0)
	activity.EndTime = time.Unix(updateActivity.EndTime, 0)

	// 检查开始时间和结束时间
	if activity.StartTime.After(activity.EndTime) {
		txErr = fmt.Errorf("活动开始时间不能晚于结束时间")
		c.Fail(txErr.Error())
		return
	}

	_, err = tx.Update(&activity, "name", "description", "start_time", "end_time", "updated_time")
	if err != nil {
		txErr = fmt.Errorf("更新活动信息失败: %v", err)
		c.Fail(txErr.Error())
		return
	}

	// 3. 处理优惠券关联
	if err = c.activityService.ProcessActivityCoupons(tx, activity.Id, updateActivity.Coupons); err != nil {
		txErr = err
		c.Fail(txErr.Error())
		return
	}
}

func (c *CouponActivityController) UpdateActivityStatus() {
	var req struct {
		ID     int64 `json:"id" validate:"required"`
		Status int   `json:"status" validate:"required,oneof=0 1"` // 0-禁用 1-启用
	}

	if err := json.Unmarshal(c.Ctx.Input.RequestBody, &req); err != nil {
		c.Fail("参数解析失败")
		return
	}

	// 参数校验
	validate := validator.New()
	if err := validate.Struct(&req); err != nil {
		c.Fail(err.Error())
		return
	}

	o := orm.NewOrmUsingDB("robobusWX")
	tx, err := o.Begin() // 开始事务
	if err != nil {
		c.Fail("无法开始事务")
		return
	}

	var txErr error
	defer func() {
		if r := recover(); r != nil {
			_ = tx.Rollback()
			panic(r)
		}

		if txErr != nil {
			logs.Error("事务执行失败: %v", txErr)
			_ = tx.Rollback()
			c.Fail(txErr.Error())
		} else {
			if err := tx.Commit(); err != nil {
				logs.Error("事务提交失败: %v", err)
				_ = tx.Rollback()
				c.Fail(fmt.Sprintf("提交事务失败: %v", err))
			} else {
				c.Success(nil)
			}
		}
	}()

	// 查询活动是否存在
	activity := robobus_wx.CouponActivity{Id: req.ID}
	err = tx.Read(&activity)
	if err != nil {
		if errors.Is(err, orm.ErrNoRows) {
			txErr = fmt.Errorf("活动不存在")
		} else {
			txErr = fmt.Errorf("查询活动失败: %v", err)
		}
		return
	}

	// 检查活动是否已删除
	if activity.IsDelete == common.IsDeleted {
		txErr = fmt.Errorf("活动已被删除")
		return
	}

	// 更新活动状态
	activity.Status = req.Status
	activity.UpdatedTime = time.Now()
	_, err = tx.Update(&activity, "status", "updated_time")
	if err != nil {
		txErr = fmt.Errorf("更新活动状态失败: %v", err)
		return
	}
}

// DeleteCouponActivity 删除活动
func (c *CouponActivityController) DeleteCouponActivity() {
	// 获取活动ID
	activityId, err := c.GetInt64("id")
	if err != nil {
		c.Fail("无效的活动ID")
		return
	}

	o := orm.NewOrmUsingDB("robobusWX")
	tx, err := o.Begin() // 开始事务
	if err != nil {
		c.Fail("无法开始事务")
		return
	}

	defer func() {
		if err != nil {
			rollbackErr := tx.Rollback() // 回滚事务
			if rollbackErr != nil {
				logs.Error("回滚事务失败:", rollbackErr)
			}
			if err.Error() != "" {
				c.Fail(err.Error())
			} else {
				c.Fail("操作失败")
			}
		} else {
			commitErr := tx.Commit() // 提交事务
			if commitErr != nil {
				logs.Error("提交事务失败:", commitErr)
				c.Fail("提交事务失败")
				return
			}
			c.Success(nil)
		}
	}()

	// 1. 查询活动是否存在
	activity := robobus_wx.CouponActivity{Id: activityId}
	err = tx.Read(&activity)
	if err != nil {
		if errors.Is(err, orm.ErrNoRows) {
			err = fmt.Errorf("活动不存在")
		}
		return
	}

	// // 2. 查询关联的优惠券活动关系
	// var relations []robobus_wx.CouponActivityRelation
	// _, err = tx.QueryTable(new(robobus_wx.CouponActivityRelation)).
	// 	Filter("activity_id", activityId).
	// 	All(&relations)
	// if err != nil && !errors.Is(err, orm.ErrNoRows) {
	// 	return
	// }
	//
	// // 3. 删除关联的数据
	// for _, relation := range relations {
	// 	// 删除发放限制
	// 	_, err = tx.QueryTable(new(robobus_wx.CouponGrantRule)).
	// 		Filter("coupon_activity_id", relation.Id).
	// 		Delete()
	// 	if err != nil {
	// 		return
	// 	}
	//
	// 	// 删除区域限制
	// 	_, err = tx.QueryTable(new(robobus_wx.CouponActivityRestriction)).
	// 		Filter("coupon_activity_id", relation.Id).
	// 		Delete()
	// 	if err != nil {
	// 		return
	// 	}
	//
	// 	// 删除活动关联关系
	// 	_, err = tx.Delete(&relation)
	// 	if err != nil {
	// 		return
	// 	}
	// }
	//
	// // 4. 删除活动
	// _, err = tx.Delete(&activity)
	// if err != nil {
	// 	return
	// }

	// 检查活动是否已被删除
	if activity.IsDelete == common.IsDeleted {
		err = fmt.Errorf("活动已被删除")
		return
	}

	// 2. 查询关联的优惠券活动关系
	var relations []robobus_wx.CouponActivityRelation
	_, err = tx.QueryTable(new(robobus_wx.CouponActivityRelation)).
		Filter("activity_id", activityId).
		Filter("is_delete", common.IsNotDeleted).
		All(&relations)
	if err != nil && !errors.Is(err, orm.ErrNoRows) {
		return
	}

	// 3. 软删除关联的数据
	for _, relation := range relations {
		// 软删除发放限制
		_, err = tx.QueryTable(new(robobus_wx.CouponGrantRule)).
			Filter("coupon_activity_id", relation.Id).
			Filter("is_delete", common.IsNotDeleted).
			Update(orm.Params{
				"is_delete":    common.IsDeleted,
				"updated_time": time.Now(),
			})
		if err != nil {
			return
		}

		// 软删除区域限制
		_, err = tx.QueryTable(new(robobus_wx.CouponActivityRestriction)).
			Filter("coupon_activity_id", relation.Id).
			Filter("is_delete", common.IsNotDeleted).
			Update(orm.Params{
				"is_delete":    common.IsDeleted,
				"updated_time": time.Now(),
			})
		if err != nil {
			return
		}

		// 软删除活动关联关系
		relation.IsDelete = common.IsDeleted
		relation.UpdatedTime = time.Now()
		_, err = tx.Update(&relation, "is_delete", "updated_time")
		if err != nil {
			return
		}
	}

	// 4. 软删除活动
	activity.IsDelete = common.IsDeleted
	activity.UpdatedTime = time.Now()
	_, err = tx.Update(&activity, "is_delete", "updated_time")
	if err != nil {
		return
	}
}

func InitCouponActivity() beego.LinkNamespace {
	return beego.NSNamespace("/operation",
		beego.NSRouter("/coupon_activity_page", &CouponActivityController{}, "get:CouponActivityPage"),
		beego.NSRouter("/add_coupon_activity", &CouponActivityController{}, "post:AddCouponActivity"),
		beego.NSRouter("/edit_coupon_activity", &CouponActivityController{}, "put:EditCouponActivity"),
		beego.NSRouter("/update_activity_status", &CouponActivityController{}, "put:UpdateActivityStatus"),
		beego.NSRouter("/del_coupon_activity", &CouponActivityController{}, "delete:DeleteCouponActivity"),
		beego.NSRouter("/get_activity_detail", &CouponActivityController{}, "get:GetActivityDetail"),
		beego.NSRouter("/edit_coupon_status", &CouponActivityController{}, "put:EditCouponStatus"),
	)
}
