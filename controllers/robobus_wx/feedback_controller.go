package robobus_wx

import (
	"ccapi/controllers"
	"ccapi/models/dto"
	"ccapi/pkg"
	robobus_wx_service "ccapi/service/robobus_wx"
	beego "github.com/beego/beego/v2/server/web"
)

// FeedbackController 反馈控制器
type FeedbackController struct {
	controllers.BaseController
	feedbackService robobus_wx_service.FeedbackServiceInter
}

func (c *FeedbackController) Prepare() {
	c.feedbackService = robobus_wx_service.NewFeedbackService()
}

// Page 列表查询
func (c *FeedbackController) Page() {
	var req dto.ListFeedbackReq
	if err := pkg.UnmarshalQuery(c.GetString, &req); err != nil {
		c.Fail(err.Error())
		return
	}

	page, err := c.feedbackService.Page(req, c.Ctx)
	if err != nil {
		c.<PERSON>ail(err.<PERSON>rror())
		return
	}
	c.Success(page)
}

func (c *FeedbackController) Delete() {
	id, err := c.GetInt64("id")
	if err != nil {
		c.Fail(err.Error())
		return
	}

	err = c.feedbackService.Delete(id)
	if err != nil {
		c.Fail(err.Error())
		return
	}

	c.Success(nil)
}

func InitFeedbackRouter() beego.LinkNamespace {
	return beego.NSNamespace("/operation/feedback",
		beego.NSRouter("/page", &FeedbackController{}, "get:Page"),        // 获取乘客列表
		beego.NSRouter("/delete", &FeedbackController{}, "delete:Delete"), // 获取乘客列表
	)
}
