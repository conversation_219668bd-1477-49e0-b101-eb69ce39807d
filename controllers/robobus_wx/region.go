package robobus_wx

import (
	"ccapi/controllers"
	"ccapi/models/robobus_wx"

	"github.com/beego/beego/v2/client/orm"
	beego "github.com/beego/beego/v2/server/web"
)

type RegionController struct {
	controllers.BaseController
}

// GetRegions 获取区域列表，支持多种查询条件
//
// 请求参数：
//   - level: int     可选，区域层级(1:省级 2:市级 3:区县级)
//   - parentId: int  可选，父级区域ID
//   - name: string   可选，区域名称搜索关键字
//
// 使用示例：
//   - 获取所有区域：GET /api/operation/regions
//   - 获取所有省级区域：GET /api/operation/regions?level=1
//   - 获取广东省下的所有市：GET /api/operation/regions?parentId=440000
//   - 获取广东省下的所有市级区域：GET /api/operation/regions?parentId=440000&level=2
//   - 搜索名称包含"广州"的区域：GET /api/operation/regions?name=广州
//   - 组合查询：GET /api/operation/regions?parentId=440000&level=2&name=广州
func (c *RegionController) GetRegions() {
	o := orm.NewOrmUsingDB("robobusWX")
	var regions []robobus_wx.Region

	// 构建查询
	query := o.QueryTable(new(robobus_wx.Region))

	// 层级查询
	level, err := c.GetInt("level", 0)
	if err == nil && level > 0 && level <= 3 {
		query = query.Filter("level", level)
	}

	// 父级ID查询
	parentId, err := c.GetInt("parentId", 0)
	if err == nil && parentId > 0 {
		query = query.Filter("parent_id", parentId)
	}

	// 名称搜索
	name := c.GetString("name", "")
	if name != "" {
		query = query.Filter("name__contains", name)
	}

	// 执行查询
	_, err = query.OrderBy("id").All(&regions)
	if err != nil {
		c.Fail("获取区域列表失败：" + err.Error())
		return
	}

	c.Success(regions)
}

// GetCities 获取城市列表，只返回 id 和 name，name 包含 '市'
func (c *RegionController) GetCities() {
	o := orm.NewOrmUsingDB("robobusWX")
	var regions []robobus_wx.Region

	// 查询条件：name 包含 '市' 且 level 为 2（市级）
	_, err := o.QueryTable(new(robobus_wx.Region)).
		Filter("name__contains", "市").
		All(&regions, "Id", "Name")
	if err != nil {
		return
	}
	c.Success(regions)
}

func InitRegion() beego.LinkNamespace {
	return beego.NSNamespace("/operation",
		beego.NSRouter("/cities", &RegionController{}, "get:GetCities"),   // 获取城市列表
		beego.NSRouter("/regions", &RegionController{}, "get:GetRegions"), // 获取区域列表
	)
}
