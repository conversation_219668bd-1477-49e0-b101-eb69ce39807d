package robobus_wx

import (
	"ccapi/controllers"
	"ccapi/models/robobus_wx"
	"ccapi/pkg"
	"ccapi/pkg/types"
	rs "ccapi/service/robobus_wx"
	"encoding/json"
	"errors"
	"fmt"
	"github.com/beego/beego/v2/client/orm"
	"github.com/beego/beego/v2/core/logs"
	beego "github.com/beego/beego/v2/server/web"
	"time"
)

type RouteTimeSlotController struct {
	controllers.BaseController
	timeSlotService *rs.RouteTimeSlotService
}

func (r *RouteTimeSlotController) TimeSlotPage() {
	routeId, _ := r.GetInt64("routeId", -1)
	if routeId == -1 {
		r.Fail("不传ID,默认不返回数据")
		return
	}

	o := orm.NewOrmUsingDB("robobusWX")

	// 使用ORM的Raw查询方式
	sql := `SELECT DISTINCT rts.id, rts.route_id, rts.weekday, rts.name, 
                   rbr.booking_start_time, rbr.booking_end_time,
                   rts.start_time, rts.end_time, rbr.max_orders_per_user, 
                   rbr.max_tickets_per_order, rts.max_tickets,
                   rts.ticket_type, rts.base_price, rts.base_distance
            FROM route_time_slot rts
            LEFT JOIN route_booking_rule rbr ON rts.id = rbr.time_slot_id
            LEFT JOIN route_ticket_stock rtss ON rtss.time_slot_id = rts.id
            WHERE rts.route_id = ? AND rts.is_delete = 0`

	var timeSlots []robobus_wx.RouteTimeSlotVO
	_, err := o.Raw(sql, routeId).QueryRows(&timeSlots)
	if err != nil {
		r.Fail(err.Error())
		return
	}

	// 获取所有阶梯价格数据（针对票类型2）
	var feeSteps []robobus_wx.RouteFeeStep
	_, err = o.QueryTable("route_fee_step").
		Filter("is_delete", 0).
		All(&feeSteps)
	if err != nil {
		r.Fail(fmt.Sprintf("查询阶梯价格失败: %v", err))
		return
	}

	// 创建时间槽ID到阶梯价格的映射
	feeStepMap := make(map[int]robobus_wx.RouteFeeStep)
	for _, fs := range feeSteps {
		feeStepMap[fs.TimeSlotId] = fs
	}

	// 处理结果，填充阶梯价格字段
	uniqueSlots := make(map[int64]robobus_wx.RouteTimeSlotVO)
	for _, slot := range timeSlots {
		if slot.TicketType == 2 {
			if feeStep, exists := feeStepMap[int(slot.Id)]; exists {
				slot.StepPrice = feeStep.StepPrice
				slot.StepDistance = feeStep.StepDistance
			} else {
				logs.Warning("未找到时间段ID=%d的阶梯价格信息", slot.Id)
				slot.StepPrice = 0
				slot.StepDistance = 0
			}
		} else {
			slot.StepPrice = 0
			slot.StepDistance = 0
		}
		uniqueSlots[slot.Id] = slot
	}

	// 转换为最终结果切片
	var result []robobus_wx.RouteTimeSlotVO
	for _, slot := range uniqueSlots {
		result = append(result, slot)
	}

	r.Success(result)
}

func (r *RouteTimeSlotController) EditTimeSlot() {
	var editTimeSlot robobus_wx.AddRouteTimeSlot
	if err := pkg.Unmarshal(r.Ctx.Input.RequestBody, &editTimeSlot); err != nil {
		r.Fail(err.Error())
		return
	}

	o := orm.NewOrmUsingDB("robobusWX")
	tx, err := o.Begin()
	if err != nil {
		r.Fail(err.Error())
		return
	}

	var txErr error
	defer func() {
		if r := recover(); r != nil {
			_ = tx.Rollback()
			panic(r)
		}

		if txErr != nil {
			logs.Error("事务执行失败: %v", txErr)
			_ = tx.Rollback()
			r.Fail(txErr.Error())
		} else {
			if err := tx.Commit(); err != nil {
				logs.Error("事务提交失败: %v", err)
				_ = tx.Rollback()
				r.Fail(fmt.Sprintf("提交事务失败: %v", err))
			} else {
				logs.Info("事务提交成功")
				r.Success(editTimeSlot.RouteId)
			}
		}
	}()

	// 1. 获取数据库中现有的未删除的时间段列表
	var existingTimeSlots []robobus_wx.RouteTimeSlot
	if _, err := tx.QueryTable("route_time_slot").
		Filter("route_id", editTimeSlot.RouteId).
		Filter("is_delete", 0).
		All(&existingTimeSlots); err != nil {
		txErr = fmt.Errorf("查询现有时间段失败: %v", err)
		return
	}
	logs.Info("查询到现有时间段数量: %d", len(existingTimeSlots))

	// 2. 创建映射，标记要保留和要删除的记录
	existingMap := make(map[int64]robobus_wx.RouteTimeSlot)
	toDeleteIds := make(map[int64]bool)
	newMap := make(map[int64]bool)

	// 记录新参数中的ID
	logs.Info("开始处理新参数列表，共 %d 条记录", len(editTimeSlot.Timeslots))
	for i, t := range editTimeSlot.Timeslots {
		logs.Info("处理第 %d 条记录: ID=%d, Name=%s, Weekday=%d", i+1, t.Id, t.Name, t.Weekday)
		if t.Id > 0 {
			newMap[t.Id] = true
		}
	}

	// 标记要删除的记录
	for _, ts := range existingTimeSlots {
		existingMap[int64(ts.Id)] = ts
		if !newMap[int64(ts.Id)] {
			logs.Info("标记要删除的记录: ID=%d, Name=%s", ts.Id, ts.Name)
			toDeleteIds[int64(ts.Id)] = true
		}
	}

	// 3. 检查时段名称重复和时间重叠
	for _, t := range editTimeSlot.Timeslots {
		operation := "新增"
		if t.Id > 0 {
			operation = "编辑"
		}

		// 3.1 检查名称重复
		existingSlot, err := r.timeSlotService.CheckNameDuplicate(
			tx,
			editTimeSlot.RouteId,
			t.Weekday,
			t.Name,
			t.Id,
			toDeleteIds,
		)
		if err != nil {
			txErr = err
			return
		}
		if existingSlot != nil {
			txErr = fmt.Errorf(r.timeSlotService.FormatNameDuplicateError(
				operation,
				t.Weekday,
				t.Name,
				existingSlot,
			))
			return
		}

		// 3.2 检查时间重叠
		overlappingSlot, err := r.timeSlotService.CheckTimeOverlap(
			tx,
			editTimeSlot.RouteId,
			t.Weekday,
			t.StartTime,
			t.EndTime,
			t.Id,
			toDeleteIds,
		)
		if err != nil {
			txErr = err
			return
		}
		if overlappingSlot != nil {
			txErr = fmt.Errorf(r.timeSlotService.FormatTimeOverlapError(
				operation,
				t.Weekday,
				t.Name,
				t.StartTime,
				t.EndTime,
				overlappingSlot,
			))
			return
		}
	}

	// 4. 执行软删除操作
	for id := range toDeleteIds {
		logs.Info("执行软删除操作: ID=%d", id)
		existing := existingMap[id]
		existing.IsDelete = 1
		if _, err := tx.Update(&existing, "IsDelete", "UpdatedTime"); err != nil {
			txErr = fmt.Errorf("软删除时间段失败: %v", err)
			return
		}

		// 软删除对应的预订规则
		bookingRule := robobus_wx.RouteBookingRule{TimeSlotId: int64(id), RouteId: editTimeSlot.RouteId}
		if err := tx.Read(&bookingRule, "TimeSlotId", "RouteId"); err == nil {
			bookingRule.IsDelete = 1
			if _, err := tx.Update(&bookingRule, "IsDelete", "UpdatedTime"); err != nil {
				txErr = fmt.Errorf("软删除预订规则失败: %v", err)
				return
			}
			logs.Info("软删除对应的预订规则成功: TimeSlotId=%d", id)
		}

		// 软删除对应的阶梯价格规则（如果存在）
		if _, err := tx.QueryTable("route_fee_step").
			Filter("time_slot_id", id).
			Filter("is_delete", 0).
			Update(orm.Params{
				"is_delete":    1,
				"updated_time": time.Now(),
			}); err != nil {
			txErr = fmt.Errorf("软删除阶梯价格规则失败: %v", err)
			return
		}
		logs.Info("软删除对应的阶梯价格规则成功: TimeSlotId=%d", id)
	}

	// 5. 处理新增和更新操作
	for _, t := range editTimeSlot.Timeslots {
		if t.Id <= 0 {
			// 新增操作
			logs.Info("执行新增操作: Name=%s, Weekday=%d", t.Name, t.Weekday)
			timeSlot := robobus_wx.RouteTimeSlot{
				RouteId:      int(editTimeSlot.RouteId),
				Weekday:      int(t.Weekday),
				Name:         t.Name,
				StartTime:    t.StartTime.String(),
				EndTime:      t.EndTime.String(),
				MaxTickets:   t.MaxTickets,
				TicketType:   t.TicketType,
				BasePrice:    t.BasePrice,
				BaseDistance: t.BaseDistance,
				Status:       1,
				IsDelete:     0,
			}

			id, err := tx.Insert(&timeSlot)
			if err != nil {
				txErr = fmt.Errorf("新增时间段失败: %v", err)
				return
			}
			logs.Info("新增时间段成功，ID=%d", id)

			// 新增预订规则
			bookingRule := robobus_wx.RouteBookingRule{
				TimeSlotId:         int64(id),
				RouteId:            int64(editTimeSlot.RouteId),
				BookingStartTime:   t.BookingStartTime,
				BookingEndTime:     t.BookingEndTime,
				MaxOrdersPerUser:   t.MaxOrdersPerUser,
				MaxTicketsPerOrder: t.MaxTicketsPerOrder,
				Status:             1,
				IsDelete:           0,
			}

			if _, err := tx.Insert(&bookingRule); err != nil {
				txErr = fmt.Errorf("新增预订规则失败: %v", err)
				return
			}
			logs.Info("新增预订规则成功")

			// 如果是阶梯票价类型，新增阶梯价格规则
			if t.TicketType == 2 {
				feeStep := robobus_wx.RouteFeeStep{
					TimeSlotId:    int(id),
					StartDistance: t.BaseDistance,
					EndDistance:   99999,
					StepPrice:     t.StepPrice,
					StepDistance:  t.StepDistance,
					Status:        1,
					IsDelete:      0,
					CreatedTime:   time.Now(),
					UpdatedTime:   time.Now(),
				}

				if _, err := tx.Insert(&feeStep); err != nil {
					txErr = fmt.Errorf("新增阶梯价格规则失败: %v", err)
					return
				}
				logs.Info("新增阶梯价格规则成功")
			}
		} else {
			// 更新操作
			logs.Info("执行更新操作: ID=%d, Name=%s", t.Id, t.Name)
			timeSlot := robobus_wx.RouteTimeSlot{Id: int(t.Id)}
			if err := tx.Read(&timeSlot); err != nil {
				txErr = fmt.Errorf("读取时间段失败: %v", err)
				return
			}

			timeSlot.Weekday = int(t.Weekday)
			timeSlot.Name = t.Name
			timeSlot.StartTime = t.StartTime.String()
			timeSlot.EndTime = t.EndTime.String()
			timeSlot.MaxTickets = t.MaxTickets
			timeSlot.TicketType = t.TicketType
			timeSlot.BasePrice = t.BasePrice
			timeSlot.BaseDistance = t.BaseDistance

			if _, err := tx.Update(&timeSlot,
				"Weekday",
				"Name",
				"StartTime",
				"EndTime",
				"MaxTickets",
				"TicketType",
				"BasePrice",
				"BaseDistance",
				"UpdatedTime"); err != nil {
				txErr = fmt.Errorf("更新时间段失败: %v", err)
				return
			}
			logs.Info("更新时间段成功")

			// 处理预订规则
			bookingRule := robobus_wx.RouteBookingRule{TimeSlotId: int64(t.Id), RouteId: int64(editTimeSlot.RouteId)}
			if err := tx.Read(&bookingRule, "TimeSlotId", "RouteId"); err != nil {
				if errors.Is(err, orm.ErrNoRows) {
					// 创建新的预订规则
					bookingRule = robobus_wx.RouteBookingRule{
						TimeSlotId:         int64(t.Id),
						RouteId:            int64(editTimeSlot.RouteId),
						BookingStartTime:   t.BookingStartTime,
						BookingEndTime:     t.BookingEndTime,
						MaxOrdersPerUser:   t.MaxOrdersPerUser,
						MaxTicketsPerOrder: t.MaxTicketsPerOrder,
						Status:             1,
						IsDelete:           0,
					}

					if _, err := tx.Insert(&bookingRule); err != nil {
						txErr = fmt.Errorf("创建预订规则失败: %v", err)
						return
					}
					logs.Info("新增预订规则成功")
				} else {
					txErr = fmt.Errorf("读取预订规则失败: %v", err)
					return
				}
			} else {
				bookingRule.BookingStartTime = t.BookingStartTime
				bookingRule.BookingEndTime = t.BookingEndTime
				bookingRule.MaxOrdersPerUser = t.MaxOrdersPerUser
				bookingRule.MaxTicketsPerOrder = t.MaxTicketsPerOrder

				if _, err := tx.Update(&bookingRule, "BookingStartTime", "BookingEndTime", "MaxOrdersPerUser", "MaxTicketsPerOrder", "UpdatedTime"); err != nil {
					txErr = fmt.Errorf("更新预订规则失败: %v", err)
					return
				}
				logs.Info("更新预订规则成功")
			}

			// 处理阶梯价格规则（仅当票类型为2时）
			if t.TicketType == 2 {
				var feeStep robobus_wx.RouteFeeStep
				err := tx.QueryTable("route_fee_step").
					Filter("time_slot_id", t.Id).
					Filter("is_delete", 0).
					One(&feeStep)

				if err != nil {
					if errors.Is(err, orm.ErrNoRows) {
						// 创建新的阶梯价格规则
						feeStep = robobus_wx.RouteFeeStep{
							TimeSlotId:    int(t.Id),
							StartDistance: t.BaseDistance,
							EndDistance:   99999,
							StepPrice:     t.StepPrice,
							StepDistance:  t.StepDistance,
							Status:        1,
							IsDelete:      0,
							CreatedTime:   time.Now(),
							UpdatedTime:   time.Now(),
						}

						if _, err := tx.Insert(&feeStep); err != nil {
							txErr = fmt.Errorf("创建阶梯价格规则失败: %v", err)
							return
						}
						logs.Info("新增阶梯价格规则成功")
					} else {
						txErr = fmt.Errorf("查询阶梯价格规则失败: %v", err)
						return
					}
				} else {
					// 更新现有的阶梯价格规则
					feeStep.StartDistance = t.BaseDistance
					feeStep.StepPrice = t.StepPrice
					feeStep.StepDistance = t.StepDistance
					feeStep.UpdatedTime = time.Now()

					if _, err := tx.Update(&feeStep, "StartDistance", "StepPrice", "StepDistance", "UpdatedTime"); err != nil {
						txErr = fmt.Errorf("更新阶梯价格规则失败: %v", err)
						return
					}
					logs.Info("更新阶梯价格规则成功")
				}
			} else {
				// 如果票类型从2变为1，需要删除原有的阶梯价格规则
				if _, err := tx.QueryTable("route_fee_step").
					Filter("time_slot_id", t.Id).
					Filter("is_delete", 0).
					Update(orm.Params{
						"is_delete":    1,
						"updated_time": time.Now(),
					}); err != nil {
					txErr = fmt.Errorf("删除阶梯价格规则失败: %v", err)
					return
				}
				logs.Info("删除阶梯价格规则成功（票类型变更）")
			}
		}
	}
}

func (r *RouteTimeSlotController) TimeSlotTicketPage() {
	userInfo, err := r.CheckToken()
	if err != nil {
		r.Fail("用户身份校验失败：" + err.Error())
		return
	}
	userId := userInfo.Id

	// 修正SQL查询，使用正确的SQL注释语法
	sql := `SELECT 
            rt_stock.id AS id,
            route.id AS route_id,
            route.name AS route_name,  -- 确保获取路线名称
            rt_stock.id AS stock_id,
            rts.name AS time_slot_name,
            rt_stock.travel_date AS travel_date,
            rts.start_time AS start_time,
            rts.end_time AS end_time,
            rt_stock.total_stock AS total_stock,
            rt_stock.sold_count AS sold_count,
            (rt_stock.total_stock - rt_stock.sold_count) AS remain_count
        FROM route 
        INNER JOIN route_time_slot rts ON route.id = rts.route_id
        INNER JOIN route_ticket_stock rt_stock ON rt_stock.time_slot_id = rts.id
        WHERE rts.is_delete = 0 AND route.is_delete = 0`

	// 数据权限控制
	if userId != 1 {
		sql += fmt.Sprintf(" AND route.fms_user_id = %d", userId)
	}

	// 处理查询参数
	var query robobus_wx.TicketStockQuery
	if err = pkg.UnmarshalQuery(r.GetString, &query); err != nil {
		r.Fail(err.Error())
		return
	}

	// 手动构建条件
	var params []interface{}
	if query.RouteName != "" {
		sql += " AND route.name LIKE ?"
		params = append(params, "%"+query.RouteName+"%")
	}

	// 处理日期条件 - 确保正确处理前端传来的Unix时间戳
	if query.TravelDate.MyTimestamp.Unix() > 0 {
		sql += " AND rt_stock.travel_date = ?"
		dateStr := query.TravelDate.MyTimestamp.Format("2006-01-02")
		params = append(params, dateStr)
	}

	// 调试输出SQL
	fmt.Println("Executing SQL:", sql)
	fmt.Println("With params:", params)

	// 执行查询
	o := orm.NewOrmUsingDB("robobusWX")

	// 使用中间结构体接收结果
	type QueryResult struct {
		Id           int64     `orm:"column(id)"`
		RouteId      int64     `orm:"column(route_id)"`
		RouteName    string    `orm:"column(route_name)"`
		StockId      int64     `orm:"column(stock_id)"`
		TimeSlotName string    `orm:"column(time_slot_name)"`
		TravelDate   time.Time `orm:"column(travel_date)"`
		StartTime    string    `orm:"column(start_time)"`
		EndTime      string    `orm:"column(end_time)"`
		TotalStock   int       `orm:"column(total_stock)"`
		SoldCount    int       `orm:"column(sold_count)"`
		RemainCount  int       `orm:"column(remain_count)"`
	}

	var queryResults []QueryResult
	_, err = o.Raw(sql, params...).QueryRows(&queryResults)
	if err != nil {
		r.Fail("查询失败：" + err.Error())
		return
	}

	// 转换为前端需要的格式
	routeMap := make(map[int64]*robobus_wx.TicketStockVO)
	for _, item := range queryResults {
		// 初始化路线信息
		if _, exists := routeMap[item.RouteId]; !exists {
			routeMap[item.RouteId] = &robobus_wx.TicketStockVO{
				Id:        item.RouteId,
				RouteName: item.RouteName,
				Tickets:   []*robobus_wx.TicketStockListVO{},
			}
		}

		// 转换时间字段
		travelDate := types.MyUnixDate{MyTimestamp: item.TravelDate}
		startTime, _ := time.Parse("15:04:05", item.StartTime)
		endTime, _ := time.Parse("15:04:05", item.EndTime)

		// 添加票务信息
		ticket := &robobus_wx.TicketStockListVO{
			Id:                item.Id,
			RouteId:           item.RouteId,
			StockId:           item.StockId,
			TimeSlotName:      item.TimeSlotName,
			TravelDate:        travelDate,
			TimeSlotStartTime: types.MyUnixTime{MyTimestamp: startTime},
			TimeSlotEndTime:   types.MyUnixTime{MyTimestamp: endTime},
			TotalStock:        item.TotalStock,
			SoldCount:         item.SoldCount,
			RemainCount:       item.RemainCount,
		}

		routeMap[item.RouteId].Tickets = append(routeMap[item.RouteId].Tickets, ticket)
	}

	// 转换为列表
	var list []robobus_wx.TicketStockVO
	for _, vo := range routeMap {
		list = append(list, *vo)
	}

	// 分页处理
	page := map[string]interface{}{
		"list":  list,
		"total": len(list),
	}
	r.Success(page)
}

func (r *RouteTimeSlotController) EditTimeSlotTicket() {
	var editTicketStock robobus_wx.TicketStockListVO
	err := json.Unmarshal(r.Ctx.Input.RequestBody, &editTicketStock)
	if err != nil {
		r.Fail(err.Error())
		return
	}
	o := orm.NewOrmUsingDB("robobusWX")

	// 修改：不再使用QueryBuilder，直接使用标准ORM API获取对象
	var slotTicket robobus_wx.RouteTicketStock
	err = o.QueryTable("route_ticket_stock").Filter("id", editTicketStock.Id).One(&slotTicket)
	if err != nil {
		r.Fail(err.Error())
		return
	}

	remainCount := slotTicket.TotalStock - slotTicket.SoldCount
	slotTicket.TotalStock = slotTicket.TotalStock + (editTicketStock.RemainCount - remainCount)
	_, err = o.Update(&slotTicket, "total_stock")
	if err != nil {
		r.Fail(err.Error())
		return
	}
	r.Success(editTicketStock.Id)
}

func InitRouteTimeSlot() beego.LinkNamespace {
	return beego.NSNamespace("/operation",
		beego.NSRouter("/time_slot_page", &RouteTimeSlotController{}, "get:TimeSlotPage"),
		//beego.NSRouter("/add_time_slot", &RouteTimeSlotController{}, "post:AddTimeSlot"),
		beego.NSRouter("/edit_time_slot", &RouteTimeSlotController{}, "put:EditTimeSlot"),
		beego.NSRouter("/ticket_page", &RouteTimeSlotController{}, "get:TimeSlotTicketPage"),
		beego.NSRouter("/edit_timeslot_ticket", &RouteTimeSlotController{}, "put:EditTimeSlotTicket"),
	)
}
