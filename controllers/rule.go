package controllers

import (
	"ccapi/service"
	beego "github.com/beego/beego/v2/server/web"
)

// RuleController 权限模块
type RuleController struct {
	BaseController
	service service.RuleServiceInter
}

// InitRuleRouter 初始化路由
func InitRuleRouter() beego.LinkNamespace {
	return beego.NSNamespace("/rule",
		beego.NSRouter("/list", &RuleController{}, "get:List"), // 获取权限列表
	)
}

// Prepare 注册服务
func (c *RuleController) Prepare() {
	c.service = service.NewRuleService()
}

// List 获取权限列表
func (c *RuleController) List() {
	parentId, err := c.GetInt("parent_id", 0)
	if err != nil {
		c.Fail(err.Error())
		return
	}

	data, err := c.service.List(parentId)
	if err != nil {
		c.Fail("获取失败：" + err.<PERSON>rror())
		return
	}
	c.Success(map[string]interface{}{
		"items": data,
	})
}
