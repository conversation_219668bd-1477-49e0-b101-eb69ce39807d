package controllers

import (
	"ccapi/common"
	"ccapi/common/protocol"
	"ccapi/models/dto"
	"ccapi/models/enums"
	"ccapi/models/pix"
	"ccapi/server/redis"
	"ccapi/service"
	"encoding/json"
	"fmt"
	"strings"
	"time"

	"github.com/beego/beego/v2/client/orm"
	beego "github.com/beego/beego/v2/server/web"
)

// TaskController 任务模块
type TaskController struct {
	BaseController
	service                service.TaskServiceInter
	userService            service.UserServiceInter
	deviceService          service.DeviceServiceInter
	executionReportService service.TaskExecutionReportServiceInter
}

// InitTaskRouter 初始化路由
func InitTaskRouter() beego.LinkNamespace {
	return beego.NSNamespace("/task",
		beego.NSRouter("/list", &TaskController{}, "get:List"),                              // 获取任务列表
		beego.NSRouter("/log", &TaskController{}, "get:Log"),                                // 获取任务执行日志
		beego.NSRouter("/add", &TaskController{}, "post:Add"),                               // 新增任务
		beego.NSRouter("/update", &TaskController{}, "post:Update"),                         // 修改任务
		beego.NSRouter("/delete", &TaskController{}, "post:Delete"),                         // 删除任务
		beego.NSRouter("/delete_all", &TaskController{}, "post:DeleteAll"),                  // 删除选中任务
		beego.NSRouter("/do", &TaskController{}, "post:Do"),                                 // 下发任务
		beego.NSRouter("/do_all", &TaskController{}, "post:DoAll"),                          // 解封选中任务
		beego.NSRouter("/stop_all", &TaskController{}, "post:StopAll"),                      // 冻结选中任务
		beego.NSRouter("/send_all", &TaskController{}, "post:SendAll"),                      // 下发选中任务
		beego.NSRouter("/get_times", &TaskController{}, "get:GetTimes"),                     // 获取下发次数
		beego.NSRouter("/create_and_dispatch", &TaskController{}, "post:CreateAndDispatch"), // 创建并下发站点任务
	)
}

// Prepare 注册服务
func (c *TaskController) Prepare() {
	c.service = service.NewTaskService()
	c.userService = service.NewUserService()
	c.deviceService = service.NewDeviceService()
	c.executionReportService = service.NewTaskExecutionReportService()
}

// List 获取任务列表
func (c *TaskController) List() {
	name := c.GetString("name", "")
	_type, err := c.GetInt("type", -1)
	status, err := c.GetInt("status", -1)
	uid, err := c.GetInt("uid", -1)
	imsi := c.GetString("imsi", "")
	page, err := c.GetInt("page", 1)
	limit, err := c.GetInt("limit", 10)

	userInfo, _ := c.CheckToken()

	userList, err := c.userService.Child(userInfo, "")

	var idsStr []string
	for _, v := range userList {
		idsStr = append(idsStr, fmt.Sprintf("%d", v.Id))
	}

	allId := strings.Join(idsStr, ",")

	data, count, err := c.service.List(c.Ctx, page, limit, name, status, allId, _type, uid, imsi)

	if err != nil {
		c.Fail("获取列表失败：" + err.Error())
		return
	}

	c.Success(map[string]interface{}{
		"items": data,
		"total": count,
	})
}

// Log 获取任务日志
func (c *TaskController) Log() {
	name := c.GetString("name", "")
	imsi := c.GetString("imsi", "")
	_type, err := c.GetInt("type", -1)
	status, err := c.GetInt("status", -1)
	uid, err := c.GetInt("uid", -1)
	deviceName := c.GetString("device_name", "")
	hdMapId, err := c.GetInt("hd_map_id", -1)
	operationId, err := c.GetInt("operation_id", -1)
	page, err := c.GetInt("page", 1)
	limit, err := c.GetInt("limit", 10)

	userInfo, _ := c.CheckToken()

	userList, err := c.userService.Child(userInfo, "")

	var idsStr []string
	for _, v := range userList {
		idsStr = append(idsStr, fmt.Sprintf("%d", v.Id))
	}

	allId := strings.Join(idsStr, ",")

	data, count, err := c.service.Log(page, limit, name, status, imsi, allId, _type, uid, deviceName, hdMapId, operationId)

	if err != nil {
		c.Fail("获取列表失败：" + err.Error())
		return
	}

	c.Success(map[string]interface{}{
		"items": data,
		"total": count,
	})
}

// Add 新增任务
func (c *TaskController) Add() {
	var (
		err     error
		addTask dto.AddTask
	)

	data := c.Ctx.Input.RequestBody
	err = common.Validate(data, &addTask)

	if err != nil {
		c.Fail("参数错误," + err.Error())
		return
	}

	imsi := addTask.Imsi
	// 检查设备是否有冲突的执行实例
	exist := c.service.CheckDeviceExecutionConflict(imsi, 0)
	if exist {
		c.Fail("该设备存在将要执行、正在执行或已暂停的任务，无法新增")
		return
	}

	name := addTask.Name
	desp := addTask.Desp
	uid := addTask.Uid
	_type := addTask.Type
	year := addTask.Year
	month := addTask.Month
	day := addTask.Day
	week := addTask.Week
	hour := addTask.Hour
	minute := addTask.Minute
	second := addTask.Second
	taskChild := addTask.TaskChild
	mapId := addTask.MapId

	id, err := c.service.Add(name, desp, uid, imsi, _type, year, month, day, week, hour, minute, second, taskChild, mapId)
	if err != nil {
		c.Fail("新增失败:" + err.Error())
		return
	}

	c.Success(id)
	return
}

// Update 修改任务
func (c *TaskController) Update() {
	var (
		err        error
		updateTask dto.UpdateTask
	)

	data := c.Ctx.Input.RequestBody
	err = common.Validate(data, &updateTask)

	if err != nil {
		c.Fail("参数错误," + err.Error())
		return
	}

	id := updateTask.Id
	name := updateTask.Name
	desp := updateTask.Desp
	uid := updateTask.Uid
	imsi := updateTask.Imsi
	_type := updateTask.Type
	year := updateTask.Year
	month := updateTask.Month
	day := updateTask.Day
	week := updateTask.Week
	hour := updateTask.Hour
	minute := updateTask.Minute
	second := updateTask.Second
	taskChild := updateTask.TaskChild
	isActive := updateTask.IsActive
	mapId := updateTask.MapId

	// exist := c.service.CheckDevice(id, imsi)
	// if exist {
	//	c.Fail("该任务已经执行，无法修改")
	//	return
	// }

	err = c.service.Update(id, name, desp, uid, imsi, _type, year, month, day, week, hour, minute, second, isActive,
		taskChild, mapId)
	if err != nil {
		c.Fail("修改失败:" + err.Error())
		return
	}

	c.Success(nil)
	return
}

// Delete 删除任务
func (c *TaskController) Delete() {
	var (
		err    error
		postId dto.PostId
	)

	userInfo, err := c.CheckToken()
	if err != nil {
		c.Fail("获取用户信息失败：" + err.Error())
		return
	}

	data := c.Ctx.Input.RequestBody
	err = common.Validate(data, &postId)

	if err != nil {
		c.Fail("参数错误," + err.Error())
		return
	}

	id := postId.Id

	task, err := c.service.Find(id)
	if err != nil {
		c.Fail("查询任务失败：" + err.Error())
		return
	}
	err = c.service.Delete(id)
	if err != nil {
		c.Fail("删除失败：" + err.Error())
		return
	}

	// 操作日志
	logType := "删除任务"

	newData := map[string]interface{}{
		"taskId":   id,
		"taskName": task.Name,
	}

	jsonData, _ := json.Marshal(newData)

	if e := c.AddOperationLog(userInfo, logType, 4, "", string(jsonData), ""); e != nil {
		msg := fmt.Sprintf(AddLogFailed, logType, e)
		c.Fail(msg)
		return
	}

	c.Success(nil)
}

// DeleteAll 删除选中任务
func (c *TaskController) DeleteAll() {
	var (
		err    error
		postId dto.PostIds
	)

	data := c.Ctx.Input.RequestBody
	err = common.Validate(data, &postId)

	if err != nil {
		c.Fail("参数错误," + err.Error())
		return
	}

	id := postId.Id

	err = c.service.DeleteAll(id)
	if err != nil {
		c.Fail("删除失败：" + err.Error())
		return
	}

	c.Success(nil)
}

// Do 下发任务
func (c *TaskController) Do() {
	var (
		err        error
		postId     dto.PostId
		actionList = make([]dto.TaskContent, 0)
	)

	data := c.Ctx.Input.RequestBody
	err = common.Validate(data, &postId)

	if err != nil {
		c.Fail("参数错误," + err.Error())
		return
	}

	id := postId.Id

	// 获取用户信息
	userInfo, _ := c.CheckToken()

	task, err := c.service.Find(id)
	if err != nil {
		c.Fail(err.Error())
		return
	}

	imsi := task.Imsi
	// 使用新的执行实例冲突检查方法
	exist := c.service.CheckDeviceExecutionConflict(imsi, 0)
	if exist {
		c.Fail("该设备存在将要执行、正在执行或已暂停的任务，无法下发新任务")
		return
	}

	child, err := c.service.Child(id)
	if err != nil {
		c.Fail("任务下发失败，暂无可下发的子任务")
		return
	}

	err = c.service.Cancel(id)
	if err != nil {
		c.Fail("任务取消失败：" + err.Error())
		return
	}

	// 🆕 创建任务执行实例记录
	err = c.createExecutionReport(&task, &child, &userInfo)
	if err != nil {
		c.Fail("创建执行实例失败：" + err.Error())
		return
	}

	nextChild, err := c.service.NextChild(child)

	actionList = append(actionList, dto.TaskContent{
		Action:  child.Action,
		Content: child.Content,
		PST:     int(time.Now().Unix()),
		PET:     0,
	})

	if err == nil {
		actionList = append(actionList, dto.TaskContent{
			Action:  nextChild.Action,
			Content: nextChild.Content,
			PST:     int(time.Now().Unix()),
			PET:     0,
		})
	}

	// 🆕 获取执行实例信息
	executionReport, err := c.executionReportService.GetExecutionReport(child.Id, task.Times)
	if err != nil {
		c.Fail("获取执行实例失败：" + err.Error())
		return
	}

	cmdData := map[string]interface{}{
		"imsi":       task.Imsi,
		"cmd":        protocol.CmdMap[9],
		"taskId":     executionReport.Id, // 重构：现在taskId字段实际包含执行实例ID，保持向后兼容
		"routeId":    task.MapId,
		"name":       task.Name,
		"desp":       task.Desp,
		"actionList": actionList,
	}

	err = c.deviceService.SendMessage(cmdData)

	if err != nil {
		c.Fail("下发消息失败：" + err.Error())
		return
	}

	err = c.service.UpdateDevice(task.Imsi, task.MapId, int64(child.Id))
	if err != nil {
		c.Fail("地图id更新失败，" + err.Error())
		return
	}

	// 操作日志
	logType := protocol.LogMap[9]
	lastHbJsonStr, err := redis.HGet(common.RedisLastHeartbeat, task.Imsi)
	if err != nil {
		lastHbJsonStr = ""
	}
	var heartData dto.RedisHeart
	if lastHbJsonStr != "" {
		err = json.Unmarshal([]byte(lastHbJsonStr), &heartData)
		if err != nil {
			c.Fail("心跳数据解析失败：" + err.Error())
			return
		}
	}

	logMsg := fmt.Sprintf("终端(%s)%s: Lng:%.6f, Lat:%.6f",
		task.Imsi,
		logType,
		heartData.Lng,
		heartData.Lat,
	)

	_, err = c.AddOperationLogWithId(userInfo, logType, 4, "", logMsg, task.Imsi)

	if err != nil {
		msg := fmt.Sprintf(AddLogFailed, logType, err)
		c.Fail(msg)
		return
	}

	redisData, _ := json.Marshal(map[string]interface{}{
		"cmd": protocol.CmdMap[9],
	})
	// 设置到 Redis 缓存 (120s
	redisKey := fmt.Sprintf(common.RedisOperationPbMsg, task.Imsi, protocol.CmdMap[9])
	redis.SetEx(redisKey, 120, redisData)

	// cmdData2 := map[string]interface{}{
	//	"imsi": task.Imsi,
	//	"cmd":  protocol.CmdMap[16],
	// }
	//
	// err = c.deviceService.SendMessage(cmdData2)
	// if err != nil {
	//	c.Fail("发送消息失败：" + err.Error())
	//	return
	// }
	//
	// // 操作日志
	// logType2 := protocol.LogMap[16]
	//
	// logMsg2 := fmt.Sprintf("终端(%s)%s: Lng:%.6f, Lat:%.6f",
	//	task.Imsi,
	//	logType2,
	//	heartData.Lng,
	//	heartData.Lat,
	// )
	//
	// _, err = c.AddOperationLogWithId(task.Imsi, userInfo, logMsg2, StatusSuccessfully)
	// if err != nil {
	//	msg := fmt.Sprintf(AddLogFailed, logType, err)
	//	c.Fail(msg)
	//	return
	// }
	//
	// redisData2, _ := json.Marshal(map[string]interface{}{
	//	"cmd": protocol.CmdMap[16],
	// })
	// // 设置到 Redis 缓存 (120s
	// redisKey2 := fmt.Sprintf(common.RedisOperationPbMsg, task.Imsi, protocol.CmdMap[16])
	// redis.SetEx(redisKey2, 120, redisData2)
	//
	// err = c.service.AddLog(task.Imsi, task.Id, child.Id, "指令下发：执行", userInfo.Id)
	// if err != nil {
	//	c.Fail("新增任务日志失败，" + err.Error())
	//	return
	// }

	// err = c.service.UpdateJob(id, 1)
	// if err != nil {
	//	c.Fail("xxl_jog状态更新失败，" + err.Error())
	//	return
	// }

	var condition struct {
		Type int `json:"type"`
		Time int `json:"time"`
	}

	err = json.Unmarshal([]byte(child.Condition), &condition)
	if err != nil {
		c.Fail("json解析失败")
		return
	}

	if condition.Type == 2 {
		go func() {
			time.Sleep(time.Duration(condition.Time) * time.Second)
			cmdData2 := map[string]interface{}{
				"imsi": task.Imsi,
				"cmd":  protocol.CmdMap[16],
			}
			c.deviceService.SendMessage(cmdData2)
			c.service.AddLog(imsi, task.Id, child.Id, 2, "执行任务", userInfo.Id, task.Name)
		}()
	}

	err = c.service.AddLog(imsi, task.Id, child.Id, 1, "下发任务", userInfo.Id, task.Name)
	if err != nil {
		c.Fail("新增日志失败：" + err.Error())
		return
	}

	allChild, err := c.service.AllChild(id)

	type stations struct {
		Order   int    `json:"order"`
		Name    string `json:"name"`
		Content string `json:"content"`
	}

	var station []stations
	for _, s := range allChild {
		station = append(station, stations{
			Order:   s.Order,
			Name:    s.Name,
			Content: s.Content,
		})
	}
	cmdData3 := map[string]interface{}{
		"imsi":     task.Imsi,
		"cmd":      0x0020,
		"stations": station,
	}

	c.deviceService.SendMessage(cmdData3)

	c.Success(nil)
}

// SendAll 下发选中任务
func (c *TaskController) SendAll() {
	var (
		err    error
		postId dto.PostIds
	)

	data := c.Ctx.Input.RequestBody
	err = common.Validate(data, &postId)

	if err != nil {
		c.Fail("参数错误," + err.Error())
		return
	}

	ids := postId.Id
	userInfo, _ := c.CheckToken()

	for _, v := range ids {
		id := int64(v)
		task, err := c.service.Find(id)

		if err != nil {
			c.Fail(err.Error())
			return
		}

		child, err := c.service.Child(id)
		if err != nil {
			c.Fail("任务下发失败，暂无可下发的子任务")
			return
		}

		err = c.service.Cancel(id)
		if err != nil {
			c.Fail("任务取消失败：" + err.Error())
			return
		}

		// 🆕 创建任务执行实例记录
		err = c.createExecutionReport(&task, &child, &userInfo)
		if err != nil {
			c.Fail("创建执行实例失败：" + err.Error())
			return
		}

		nextChild, err := c.service.NextChild(child)

		actionList := make([]dto.TaskContent, 0)
		actionList = append(actionList, dto.TaskContent{
			Action:  child.Action,
			Content: child.Content,
			PST:     int(time.Now().Unix()),
			PET:     0,
		})

		if err == nil {
			actionList = append(actionList, dto.TaskContent{
				Action:  nextChild.Action,
				Content: nextChild.Content,
				PST:     int(time.Now().Unix()),
				PET:     0,
			})
		}

		// 🆕 获取执行实例信息
		executionReport, err := c.executionReportService.GetExecutionReport(child.Id, task.Times)
		if err != nil {
			c.Fail("获取执行实例失败：" + err.Error())
			return
		}

		cmdData := map[string]interface{}{
			"imsi":       task.Imsi,
			"cmd":        protocol.CmdMap[9],
			"taskId":     executionReport.Id, // 重构：现在taskId字段实际包含执行实例ID，保持向后兼容
			"routeId":    task.MapId,
			"name":       task.Name,
			"desp":       task.Desp,
			"actionList": actionList,
		}

		err = c.deviceService.SendMessage(cmdData)

		if err != nil {
			c.Fail("下发消息失败：" + err.Error())
			return
		}

		err = c.service.UpdateDevice(task.Imsi, task.MapId, id)
		if err != nil {
			c.Fail("地图id更新失败，" + err.Error())
			return
		}

		// 操作日志
		logType := protocol.LogMap[9]
		lastHbJsonStr, err := redis.HGet(common.RedisLastHeartbeat, task.Imsi)
		if err != nil {
			lastHbJsonStr = ""
		}
		var heartData dto.RedisHeart
		if lastHbJsonStr != "" {
			err = json.Unmarshal([]byte(lastHbJsonStr), &heartData)
			if err != nil {
				c.Fail("心跳数据解析失败：" + err.Error())
				return
			}
		}

		logMsg := fmt.Sprintf("终端(%s)%s: Lng:%.6f, Lat:%.6f",
			task.Imsi,
			logType,
			heartData.Lng,
			heartData.Lat,
		)

		_, err = c.AddOperationLogWithId(userInfo, logType, 4, "", logMsg, task.Imsi)
		if err != nil {
			msg := fmt.Sprintf(AddLogFailed, logType, err)
			c.Fail(msg)
			return
		}

		redisData, _ := json.Marshal(map[string]interface{}{
			"cmd": protocol.CmdMap[9],
		})
		// 设置到 Redis 缓存 (120s
		redisKey := fmt.Sprintf(common.RedisOperationPbMsg, task.Imsi, protocol.CmdMap[9])
		redis.SetEx(redisKey, 120, redisData)

		c.service.AddLog(task.Imsi, task.Id, child.Id, 1, "下发任务", userInfo.Id, task.Name)
		var condition struct {
			Type int `json:"type"`
			Time int `json:"time"`
		}

		err = json.Unmarshal([]byte(child.Condition), &condition)
		if err != nil {
			c.Fail("json解析失败")
			return
		}
		if condition.Type == 2 {
			go func() {
				time.Sleep(time.Duration(condition.Time) * time.Second)
				cmdData2 := map[string]interface{}{
					"imsi": task.Imsi,
					"cmd":  protocol.CmdMap[16],
				}
				c.deviceService.SendMessage(cmdData2)
				c.service.AddLog(task.Imsi, task.Id, child.Id, 2, "执行任务", userInfo.Id, task.Name)
			}()
		}
	}
	c.Success(nil)
}

// DoAll 解冻选中任务
func (c *TaskController) DoAll() {
	var (
		err    error
		postId dto.PostIds
	)

	data := c.Ctx.Input.RequestBody
	err = common.Validate(data, &postId)

	if err != nil {
		c.Fail("参数错误," + err.Error())
		return
	}

	ids := postId.Id

	for _, v := range ids {
		id := int64(v)

		err = c.service.Active(id, 1)
		if err != nil {
			c.Fail("更新失败：" + err.Error())
			return
		}

		err = c.service.UpdateJob(id, 0)
		if err != nil {
			c.Fail("xxl_jog状态更新失败，" + err.Error())
			return
		}
	}

	c.Success(nil)
	return
}

// CreateAndDispatch 创建并下发站点任务
func (c *TaskController) CreateAndDispatch() {
	var (
		err     error
		request dto.CreateAndDispatchTask
	)

	data := c.Ctx.Input.RequestBody
	err = common.Validate(data, &request)

	if err != nil {
		c.Fail("参数错误: " + err.Error())
		return
	}

	// 额外的参数验证
	if request.TargetStationName == "" {
		c.Fail("参数错误: 站点名称不能为空")
		return
	}

	// 获取用户信息
	userInfo, err := c.CheckToken()
	if err != nil {
		c.Fail("获取用户信息失败: " + err.Error())
		return
	}

	// 验证设备是否存在
	device, err := c.deviceService.FindByImsi(request.Imsi)
	if err != nil {
		c.Fail("设备不存在: " + err.Error())
		return
	}

	// 检查用户权限 - 验证设备是否属于当前用户或其子用户
	userList, err := c.userService.Child(userInfo, "")
	if err != nil {
		c.Fail("获取用户权限失败: " + err.Error())
		return
	}

	// 检查设备权限
	hasPermission := false
	if userInfo.Id == 1 { // 超级管理员
		hasPermission = true
	} else {
		for _, user := range userList {
			if int64(device.Uid) == user.Id {
				hasPermission = true
				break
			}
		}
	}

	if !hasPermission {
		c.Fail("权限不足，无法操作该设备")
		return
	}

	// 创建并下发任务
	taskId, err := c.service.CreateAndDispatchStationTask(request.Imsi, request.TargetStationId, request.TargetStationName, userInfo)
	if err != nil {
		c.Fail("创建并下发任务失败: " + err.Error())
		return
	}

	// 操作日志
	logType := "创建并下发站点任务"
	logData := map[string]interface{}{
		"taskId":          taskId,
		"imsi":            request.Imsi,
		"targetStationId": request.TargetStationId,
		"deviceName":      device.Name,
	}
	jsonData, _ := json.Marshal(logData)

	if e := c.AddOperationLog(userInfo, logType, 2, "", string(jsonData), request.Imsi); e != nil {
		// 日志记录失败不影响主流程
		fmt.Printf("记录操作日志失败: %v\n", e)
	}

	c.Success(map[string]interface{}{
		"taskId":   taskId,
		"deviceId": device.Id,
		"message":  "任务创建并下发成功",
	})
}

// StopAll 冻结选中任务
func (c *TaskController) StopAll() {
	var (
		err    error
		postId dto.PostIds
	)

	data := c.Ctx.Input.RequestBody
	err = common.Validate(data, &postId)

	if err != nil {
		c.Fail("参数错误," + err.Error())
		return
	}

	ids := postId.Id

	for _, v := range ids {
		id := int64(v)

		err = c.service.Active(id, 0)
		if err != nil {
			c.Fail("更新失败：" + err.Error())
			return
		}

		err = c.service.UpdateJob(id, 0)
		if err != nil {
			c.Fail("xxl_jog状态更新失败，" + err.Error())
			return
		}
		// cmd := 0
		// logType := ""
		// if task.Status == 0 || task.Status == 1 {
		//	cmd = protocol.CmdMap[17]
		//	logType = protocol.LogMap[17]
		// } else {
		//	cmd = protocol.CmdMap[18]
		//	logType = protocol.LogMap[18]
		// }
		//
		// cmdData := map[string]interface{}{
		//	"imsi": task.Imsi,
		//	"cmd":  cmd,
		// }
		//
		// err = c.deviceService.SendMessage(cmdData)
		// if err != nil {
		//	c.Fail("发送消息失败：" + err.Error())
		//	return
		// }
		//
		// userInfo, _ := c.CheckToken()
		//
		// // 操作日志
		// lastHbJsonStr, err := redis.HGet(common.RedisLastHeartbeat, task.Imsi)
		// if err != nil {
		//	lastHbJsonStr = ""
		// }
		// var heartData dto.RedisHeart
		// if lastHbJsonStr != "" {
		//	err = json.Unmarshal([]byte(lastHbJsonStr), &heartData)
		//	if err != nil {
		//		c.Fail("心跳数据解析失败：" + err.Error())
		//		return
		//	}
		// }
		//
		// logMsg := fmt.Sprintf("终端(%s)%s: Lng:%.6f, Lat:%.6f",
		//	task.Imsi,
		//	logType,
		//	heartData.Lng,
		//	heartData.Lat,
		// )
		//
		// _, err = c.AddOperationLogWithId(task.Imsi, userInfo, logMsg, StatusSuccessfully)
		// if err != nil {
		//	msg := fmt.Sprintf(AddLogFailed, logType, err)
		//	c.Fail(msg)
		//	return
		// }
		//
		// redisData, _ := json.Marshal(map[string]interface{}{
		//	"cmd": cmd,
		// })
		// // 设置到 Redis 缓存 (120s
		// redisKey := fmt.Sprintf(common.RedisOperationPbMsg, task.Imsi, cmd)
		// redis.SetEx(redisKey, 120, redisData)
	}

	c.Success(nil)
	return
}

// GetTimes 获取设备当天下发次数
func (c *TaskController) GetTimes() {
	imsi := c.GetString("imsi")
	err, times := c.service.GetTimes(imsi)
	if err != nil {
		c.Fail(err.Error())
		return
	}

	c.Success(times)
	return
}

// createExecutionReport 创建任务执行实例记录
func (c *TaskController) createExecutionReport(task *pix.Task, child *pix.TaskChild, userInfo *dto.UserInfo) error {
	// 获取设备信息 - 通过All方法查询单个设备
	devices, err := c.deviceService.All(0, task.Imsi, -1, "", false, -1)
	var device pix.Device
	if err != nil || len(devices) == 0 {
		// 如果获取设备失败，使用默认值
		device = pix.Device{Type: 0} // 默认为RoboBus
	} else {
		device = devices[0]
	}

	// 🆕 自动计算execution_no（统一处理逻辑）
	o := orm.NewOrm()
	var maxExecutionNo int
	err = o.Raw("SELECT COALESCE(MAX(execution_no), -1) FROM task_execution_report WHERE child_id = ?", child.Id).QueryRow(&maxExecutionNo)
	if err != nil {
		return fmt.Errorf("查询最大execution_no失败: %v", err)
	}
	executionNo := maxExecutionNo + 1

	// 创建执行实例记录
	executionReport := &pix.TaskExecutionReport{
		TaskId:       int64(task.Id),
		ChildId:      child.Id,
		ExecutionNo:  executionNo, // 🆕 使用计算出的execution_no
		Imsi:         task.Imsi,
		RunningTask:  fmt.Sprintf("%d-%d", child.Id, executionNo), // 🆕 使用计算出的execution_no
		Status:       int(enums.ExecutionStatusWillExecute),       // 1 = 将要执行
		VehiclePlate: task.Imsi,
		VehicleType:  pix.GetVehicleType(device.Type),
		TaskType:     child.Type, // 直接使用 child.Type 作为数字类型
		VehicleUsage: pix.GetVehicleUsage(device.Type),
		Creator:      userInfo.Username,
		CreatedTime:  time.Now().Unix(), // 任务下发时间
	}

	// 解析任务内容，填充地点信息
	err = c.executionReportService.ParseTaskContent(executionReport, child.Content, task.MapId)
	if err != nil {
		return fmt.Errorf("解析任务内容失败: %v", err)
	}

	// 保存到数据库
	err = c.executionReportService.CreateExecutionReport(executionReport)
	if err != nil {
		return fmt.Errorf("保存执行实例失败: %v", err)
	}

	return nil
}
