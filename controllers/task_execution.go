package controllers

import (
	"ccapi/models/dto"
	"ccapi/service"
	"strconv"

	"github.com/beego/beego/v2/core/logs"
	beego "github.com/beego/beego/v2/server/web"
)

type TaskExecutionController struct {
	BaseController
	service service.TaskExecutionServiceInterface
}

func NewTaskExecutionController() *TaskExecutionController {
	return &TaskExecutionController{
		service: service.NewTaskExecutionService(),
	}
}

// InitTaskExecutionRouter 初始化历史任务路由
func InitTaskExecutionRouter() beego.LinkNamespace {
	return beego.NSNamespace("/task-execution",
		beego.NSRouter("/list", &TaskExecutionController{}, "get:List"),
		beego.NSRouter("/export", &ExportController{}, "post:ExportTaskExecution"), // 导出历史任务Excel/PDF/CSV
	)
}

// Prepare 注册服务
func (c *TaskExecutionController) Prepare() {
	c.service = service.NewTaskExecutionService()
}

// List 历史任务列表查询
func (c *TaskExecutionController) List() {
	// 获取用户ID
	userInfo, err := c.CheckToken()
	if err != nil {
		c.Fail("用户身份校验失败")
		return
	}
	userId := userInfo.Id

	logs.Debug("TaskExecutionController.List 用户ID: %d", userId)

	// 解析查询参数
	req := &dto.TaskExecutionListRequest{}

	// 获取数组参数并转换为 int64
	req.CreatedTime = convertStringArrayToInt64Array(c.GetStrings("createdTime[]"))
	req.DepartureTime = convertStringArrayToInt64Array(c.GetStrings("departureTime[]"))
	req.EndTime = convertStringArrayToInt64Array(c.GetStrings("endTime[]"))

	logs.Debug("获取到的数组参数: createdTime[]=%v, departureTime[]=%v, endTime[]=%v", req.CreatedTime, req.DepartureTime, req.EndTime)

	// 基础查询参数
	req.TaskName = c.GetString("taskName")
	req.Imsi = c.GetString("imsi")
	req.VehiclePlate = c.GetString("vehiclePlate")
	req.Creator = c.GetString("creator")
	req.SortField = c.GetString("sortField")
	req.SortOrder = c.GetString("sortOrder")

	// 数值参数
	if taskType, err := c.GetInt("taskType"); err == nil {
		req.TaskType = taskType
	} else {
		req.TaskType = -1
	}

	if status, err := c.GetInt("status"); err == nil {
		req.Status = status
	} else {
		req.Status = -1
	}

	// 时间范围参数
	if departureTimeBegin, err := c.GetInt64("departureTimeBegin"); err == nil {
		req.DepartureTimeBegin = departureTimeBegin
	} else {
		req.DepartureTimeBegin = -1
	}

	if departureTimeEnd, err := c.GetInt64("departureTimeEnd"); err == nil {
		req.DepartureTimeEnd = departureTimeEnd
	} else {
		req.DepartureTimeEnd = -1
	}

	if endTimeBegin, err := c.GetInt64("endTimeBegin"); err == nil {
		req.EndTimeBegin = endTimeBegin
	} else {
		req.EndTimeBegin = -1
	}

	if endTimeEnd, err := c.GetInt64("endTimeEnd"); err == nil {
		req.EndTimeEnd = endTimeEnd
	} else {
		req.EndTimeEnd = -1
	}

	if createdTimeBegin, err := c.GetInt64("createdTimeBegin"); err == nil {
		req.CreatedTimeBegin = createdTimeBegin
	} else {
		req.CreatedTimeBegin = -1
	}

	if createdTimeEnd, err := c.GetInt64("createdTimeEnd"); err == nil {
		req.CreatedTimeEnd = createdTimeEnd
	} else {
		req.CreatedTimeEnd = -1
	}

	if taskId, err := c.GetInt64("taskId"); err == nil {
		req.TaskId = taskId
	} else {
		req.TaskId = -1
	}

	if childId, err := c.GetInt("childId"); err == nil {
		req.ChildId = childId
	} else {
		req.ChildId = -1
	}

	// 分页参数
	if page, err := c.GetInt("page"); err == nil {
		req.Page = page
	} else {
		req.Page = 1
	}

	if limit, err := c.GetInt("limit"); err == nil {
		req.Limit = limit
	} else {
		req.Limit = 10
	}

	logs.Debug("TaskExecutionController.List 查询参数: %+v", req)

	// 调用服务层
	result, err := c.service.GetList(req, userId)
	if err != nil {
		logs.Error("TaskExecutionController.List 查询失败: %v", err)
		c.Fail("获取历史任务列表失败：" + err.Error())
		return
	}

	c.Success(result)
}

// convertStringArrayToInt64Array 将字符串数组转换为 int64 数组
func convertStringArrayToInt64Array(arr []string) []int64 {
	if len(arr) == 0 {
		return nil
	}
	result := make([]int64, 0, len(arr))
	for _, s := range arr {
		if i, err := strconv.ParseInt(s, 10, 64); err == nil {
			result = append(result, i)
		}
	}
	return result
}