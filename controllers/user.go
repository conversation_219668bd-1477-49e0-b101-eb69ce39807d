package controllers

import (
	"ccapi/common"
	"ccapi/models/dto"
	"ccapi/models/pix"
	"ccapi/server/redis"
	"ccapi/service"
	"encoding/json"
	"fmt"
	"strconv"
	"strings"
	"time"

	"image/color"

	"ccapi/auth"

	dysmsapi20170525 "github.com/alibabacloud-go/dysmsapi-20170525/v3/client"
	"github.com/alibabacloud-go/tea/tea"
	"github.com/beego/beego/v2/core/logs"
	beego "github.com/beego/beego/v2/server/web"
	"github.com/mojocn/base64Captcha"
)

// UserController 用户模块
type UserController struct {
	BaseController
	service       service.UserServiceInter
	DeviceService service.DeviceServiceInter
	HdMapService  service.HdMapServiceInter
	authService   auth.Service
}

// InitUserRouter 初始化路由
func InitUserRouter() beego.LinkNamespace {
	return beego.NSNamespace("/user",
		beego.NSRouter("/login", &UserController{}, "post:Login"),                           // 旧的登录接口(已废弃)
		beego.NSRouter("/logout", &UserController{}, "post:Logout"),                         // 旧的登出接口(已废弃)
		beego.NSRouter("/externalCheckToken", &UserController{}, "post:ExternalCheckToken"), // 旧的登出接口(已废弃)
		beego.NSRouter("/v2/login", &UserController{}, "post:LoginJWT"),                     // 新版登录接口
		beego.NSRouter("/v2/logout", &UserController{}, "post:LogoutJWT"),                   // 新版登出接口
		beego.NSRouter("/v2/token/refresh", &UserController{}, "post:RefreshToken"),         // 刷新token
		beego.NSRouter("/info", &UserController{}, "get:Info"),                              // 获取登录用户信息
		beego.NSRouter("/list", &UserController{}, "get:List"),                              // 用户列表
		beego.NSRouter("/create", &UserController{}, "post:Add"),                            // 新增用户
		beego.NSRouter("/delete", &UserController{}, "post:Delete"),                         // 删除用户
		beego.NSRouter("/update", &UserController{}, "post:Update"),                         // 修改用户
		beego.NSRouter("/password", &UserController{}, "post:UpdatePwd"),                    // 修改密码与昵称
		beego.NSRouter("/order", &UserController{}, "post:Order"),                           // 修改用户排序
		beego.NSRouter("/xj_list", &UserController{}, "get:XJList"),                         // 获取市级管理员列表
		beego.NSRouter("/detail", &UserController{}, "get:Detail"),                          // 获取用户详情
		beego.NSRouter("/child", &UserController{}, "get:Child"),                            // 获取所有子节点
		beego.NSRouter("/team_list", &UserController{}, "get:XJList"),                       // 获取市级管理员列表(无鉴权)
		beego.NSRouter("/get_map", &UserController{}, "get:GetMap"),                         // 获取用户的所有地图
		beego.NSRouter("/change_logo", &UserController{}, "post:ChangeLogo"),                // 修改logo
		beego.NSRouter("/update_config", &UserController{}, "post:UpdateConfig"),            // 修改视频配置
		beego.NSRouter("/get_config", &UserController{}, "get:GetConfig"),                   // 获取视频配置
		beego.NSRouter("/check_child", &UserController{}, "post:CheckChild"),                // 判断是否有子用户
		beego.NSRouter("/get_setting", &UserController{}, "get:GetSetting"),                 // 获取配置
		beego.NSRouter("/update_setting", &UserController{}, "post:UpdateSetting"),          // 更新配置
		beego.NSRouter("/send_sms", &UserController{}, "get:SendSms"),                       // 发送短信验证码
		beego.NSRouter("/check_code", &UserController{}, "get:CheckCode"),                   // 验证验证码
		beego.NSRouter("/get_captcha", &UserController{}, "get:GetCaptcha"),                 // 获取验证码
		beego.NSRouter("/verify_captcha", &UserController{}, "post:VerifyCaptcha"),          // 验证图形验证码
	)
}

// Prepare 注册服务
func (c *UserController) Prepare() {
	c.service = service.NewUserService()
	c.DeviceService = service.NewDeviceService()
	c.HdMapService = service.NewHdMapService()
	c.authService = auth.NewAuthService(c.service)
}

// Login 用户登录
func (c *UserController) Login() {
	var (
		loginData dto.Login
		err       error
		code      int
		msg       string
		userInfo  dto.UserInfo
	)
	data := c.Ctx.Input.RequestBody
	err = common.Validate(data, &loginData)
	if err != nil {
		c.Fail("参数错误," + err.Error())
		return
	}

	username := loginData.Username
	password := loginData.Password

	code, msg, err, userInfo = c.service.Login(username, password)
	if code == 1 {
		c.Fail(msg)
		return
	}

	// 操作日志
	logType := "用户登录"
	logMsg := fmt.Sprintf("%s: %s",
		logType,
		username,
	)

	if err = c.AddOperationLog(userInfo, logType, 3, "", logMsg, ""); err != nil {
		errStr := fmt.Sprintf(AddLogFailed, logType, err.Error())
		c.Fail(errStr)
		return
	}

	c.Success(map[string]interface{}{
		"token": msg,
	})
	return
}

// Logout 用户登出
func (c *UserController) Logout() {
	token := c.Ctx.Request.Header.Get("X-Token")
	redis.Del(fmt.Sprintf(common.RedisSessionMultiTokenUid, token))

	c.Success(nil)
	return
}

// ExternalCheckToken 外部验证token
func (c *UserController) ExternalCheckToken() {
	// 在方法中定义结构体
	type RequestData struct {
		Token string `json:"token" required:"true"`
	}
	var reqData RequestData
	data := c.Ctx.Input.RequestBody
	err := common.Validate(data, &reqData)
	if err != nil {
		c.Fail(err.Error())
		return
	}

	// 使用统一的token验证方法
	uid, err := auth.VerifyToken(reqData.Token)
	if err != nil {
		c.Fail(err.Error())
		return
	}

	c.Success(uid)
}

// Info 获取用户信息
func (c *UserController) Info() {
	token := c.GetString("token")
	if token == "" {
		token = c.Ctx.Request.Header.Get("X-Token")
		if token == "" {
			c.Fail("未登录")
			return
		}
	}

	// 验证token
	uid, err := auth.VerifyToken(token)
	if err != nil {
		c.Fail(err.Error())
		return
	}

	// 获取用户信息
	var userInfo dto.UserInfo
	userInfo.Id = uid

	// 获取用户规则
	rule := redis.Get(fmt.Sprintf("%s%d", common.RedisUserRule, userInfo.Id))
	userInfo.Rule, err = c.service.GetRule(rule)
	if err != nil {
		c.Fail(err.Error())
		return
	}

	// 获取用户详细信息
	user, _ := c.service.GetUser(userInfo.Id)
	userInfo.NetDiskLeave = user.NetDisk - user.DiskUsed
	userInfo.FlowLeave = user.Flow - user.FlowUsed
	userInfo.FlowTime = user.FlowTime
	userInfo.Name = user.Name
	userInfo.Username = user.Username
	userInfo.Tel = user.Tel
	userInfo.HeadImg = user.HeadImg
	userInfo.Level = user.Level
	userInfo.Logo = user.Logo
	userInfo.ParentID = int64(user.ParentId)
	userInfo.Status = int64(user.Status)
	userInfo.Check = user.Check

	c.Success(userInfo)
	return
}

// List 获取用户列表
func (c *UserController) List() {
	var (
		err      error
		userList []dto.UserList
		count    int64
		name     string
		level    int
		status   int
		page     int
		limit    int
	)

	name = c.GetString("name", "")
	level, err = c.GetInt("level", -1)
	status, err = c.GetInt("status", -1)
	page, err = c.GetInt("page", 1)
	limit, err = c.GetInt("limit", 10)

	if err != nil {
		c.Fail(err.Error())
		return
	}

	userList, count, err = c.service.List(name, level, status, page, limit)

	if err != nil {
		c.Fail(err.Error())
		return
	}
	c.Success(map[string]interface{}{
		"items": userList,
		"total": count,
	})
	return
}

// Add 添加用户
func (c *UserController) Add() {
	var (
		err      error
		addUser  dto.AddUser
		userInfo dto.UserInfo
		id       int64
	)

	data := c.Ctx.Input.RequestBody
	err = common.Validate(data, &addUser)

	if err != nil {
		c.Fail("参数错误," + err.Error())
		return
	}

	username := addUser.Username
	password := addUser.Password
	level := addUser.Level
	status := addUser.Status
	name := addUser.Name
	tip := addUser.Tip
	parentId := addUser.ParentId
	hdMapId := addUser.HdMapId
	roleId := addUser.RoleId
	realName := addUser.RealName
	tel := addUser.Tel
	email := addUser.Email
	_type := addUser.Type
	mapIds := addUser.MapIds

	var idsStr []string
	for _, v := range mapIds {
		idsStr = append(idsStr, fmt.Sprintf("%d", v))
	}

	allId := strings.Join(idsStr, ",")

	userInfo, err = c.CheckToken()
	if err != nil {
		c.Fail(err.Error())
		return
	}

	ex := c.service.CheckUsername(username, 0)
	if ex {
		c.Fail("该用户名已被使用")
		return
	}

	ex1 := c.service.CheckParent(parentId, 0, 0)
	if !ex1 {
		c.Fail("该父级无法添加子级")
		return
	}

	ex2 := c.service.CheckName(name, 0)
	if ex2 {
		c.Fail("该名称已被使用")
		return
	}

	ex3 := c.service.CheckTel(name, 0)
	if ex3 {
		c.Fail("该手机号已被使用")
		return
	}

	id, err = c.service.Add(username, password, level, status, name, tip, parentId, hdMapId, roleId, realName, tel,
		email, _type, allId)
	if err != nil {
		c.Fail(err.Error())
		return
	}

	newData := map[string]interface{}{
		"username":  username,
		"level":     level,
		"status":    status,
		"name":      name,
		"tip":       tip,
		"parent_id": parentId,
		"hd_map_id": hdMapId,
		"role_id":   roleId,
		"tel":       tel,
		"email":     email,
		"type":      _type,
		"map_ids":   mapIds,
	}

	jsonData, _ := json.Marshal(newData)
	// 操作日志
	logType := "添加用户数据"
	if err = c.AddOperationLog(userInfo, logType, 3, "", string(jsonData), ""); err != nil {
		message := fmt.Sprintf(AddLogFailed, logType, err.Error())
		c.Fail(message)
		return
	}

	c.Success(map[string]interface{}{
		"id":         id,
		"name":       name,
		"created_at": time.Now().Format("2006-01-02 15:04:05"),
	})
	return
}

// Delete 删除用户
func (c *UserController) Delete() {
	var (
		err         error
		postId      dto.PostId
		id          int64
		user        pix.User
		userInfo    dto.UserInfo
		delUsername string
	)

	data := c.Ctx.Input.RequestBody
	err = common.Validate(data, &postId)

	if err != nil {
		c.Fail("参数错误," + err.Error())
		return
	}
	id = postId.Id
	user, err = c.service.GetUser(id)
	if err != nil {
		c.Fail(err.Error())
		return
	}

	delUsername = user.Username
	// userInfo, _ = c.CheckToken()
	// if err != nil {
	//	c.Fail(err.Error())
	//	return
	// }
	//
	// //xjUserDeleted := false
	//
	userInfo.Id = id
	userList, err := c.service.Child(userInfo, "")
	//
	var idsStr []string
	for _, v := range userList {
		common.DelOwnDevices(v.Id)

		stringId := fmt.Sprintf("%d", v.Id)
		// 从 Redis 中删除用户信息
		delUserToken, _ := redis.HGet(common.RedisSessionUidTokenHash, stringId)
		if delUserToken != "" {
			redis.HDel(common.RedisSessionTokenToUid, delUserToken)
		}

		delUserInfoKey := fmt.Sprintf("%s%d", common.RedisUserInfof, v.Id)
		redis.HDel(common.RedisSessionUidTokenHash, stringId)
		redis.HDel(common.RedisSessionExpireTsHash, stringId)
		redis.Del(delUserInfoKey)

		idsStr = append(idsStr, fmt.Sprintf("%d", v.Id))
	}

	allId := strings.Join(idsStr, ",")

	// if userInfo.Level == common.SGLevel {
	//	if user.Level == common.XGLevel {
	//		err = c.service.Delete("parent_id = " + fmt.Sprintf("%d", id))
	//		if err != nil {
	//			c.Fail("删除管理员名下的账户失败:" + err.Error())
	//			return
	//		}
	//
	//		if err != nil {
	//			c.Fail("设备所属用户更新失败:" + err.Error())
	//			return
	//		}
	//		//xjUserDeleted = true
	//	}
	// } else {
	//	where += fmt.Sprintf(" and parent_id = %d", userInfo.Id)
	// }

	err = c.service.Delete(allId)
	err = c.DeviceService.SetNull(allId)

	if err != nil {
		c.Fail("删除用户失败:" + err.Error())
		return
	}

	// if xjUserDeleted { // 市级管理员被删除
	//	// 删除且更新市级管理员数据库缓存
	// err = c.service.ResetUserCache()
	// if err != nil {
	//	c.Fail("设置市级管理员缓存失败," + err.Error())
	//	return
	// }
	// }

	// 操作日志
	logType := "删除用户"
	logMsg := fmt.Sprintf("%s: %s(%d)",
		logType,
		delUsername,
		id,
	)
	if e := c.AddOperationLog(userInfo, logType, 3, "", logMsg, ""); e != nil {
		msg := fmt.Sprintf(AddLogFailed, logType, e)
		c.Fail(msg)
		return
	}

	c.Success(nil)
	return
}

// Update 更新用户
func (c *UserController) Update() {
	var (
		err      error
		editUser dto.EditUser
		userInfo dto.UserInfo
	)

	data := c.Ctx.Input.RequestBody
	err = common.Validate(data, &editUser)

	if err != nil {
		c.Fail("参数错误," + err.Error())
		return
	}

	id := editUser.Id
	username := editUser.Username
	password := editUser.Password
	_type := editUser.Type
	status := editUser.Status
	name := editUser.Name
	tip := editUser.Tip
	parentId := editUser.ParentId
	hdMapId := editUser.HdMapId
	roleId := editUser.RoleId
	realName := editUser.RealName
	tel := editUser.Tel
	email := editUser.Email
	mapIds := editUser.MapIds
	netDisk := editUser.NetDisk
	flow := editUser.Flow
	flowTime := editUser.FlowTime

	var idsStr []string
	for _, v := range mapIds {
		idsStr = append(idsStr, fmt.Sprintf("%d", v))
	}

	oldUser := c.service.Find(id)

	oldData := map[string]interface{}{
		"username":  oldUser.Username,
		"level":     oldUser.Level,
		"status":    oldUser.Status,
		"name":      oldUser.Name,
		"tip":       oldUser.Tip,
		"parent_id": oldUser.ParentId,
		"hd_map_id": oldUser.HdMapId,
		"tel":       oldUser.Tel,
		"email":     oldUser.Email,
		"type":      oldUser.Type,
		"map_ids":   oldUser.MapIds,
		"net_disk":  oldUser.NetDisk,
		"flow":      oldUser.Flow,
		"flow_time": oldUser.FlowTime,
	}

	oldJsonData, _ := json.Marshal(oldData)

	allId := strings.Join(idsStr, ",")

	ex := c.service.CheckUsername(username, int(id))
	if ex {
		c.Fail("该用户名已被使用")
		return
	}

	ex1 := c.service.CheckParent(parentId, 0, 0)
	if !ex1 {
		c.Fail("父级被禁用或已被修改为授权用户，暂时无法修改")
		return
	}

	ex2 := c.service.CheckName(name, int(id))
	if ex2 {
		c.Fail("该名称已被使用")
		return
	}

	ex3 := c.service.CheckTel(name, int(id))
	if ex3 {
		c.Fail("该手机号已被使用")
		return
	}

	if password != "" {
		re := common.PublicCheckPass(password)
		if !re {
			c.Fail("密码格式错误")
			return
		}

		password = common.MD5(password)
	}

	// where := map[string]interface{}{
	//	"id": id,
	// }

	userInfo, err = c.CheckToken()
	if err != nil {
		c.Fail(err.Error())
		return
	}

	if (oldUser.NetDisk != netDisk || oldUser.Flow != flow || oldUser.FlowTime != flowTime) && userInfo.Id != 1 {
		c.Fail("权限不足")
		return
	}

	// if userInfo.Level != common.SGLevel {
	//	where["parent_id"] = id
	// }

	hzUserId, _ := beego.AppConfig.Int64("HangZhouUserId")

	if hzUserId == id {
		hdMap, err := c.HdMapService.Get(mapIds[0])
		if err != nil {
			c.Fail("杭州大会展地图查询失败:" + err.Error())
			return
		}

		if hdMap.Type != 1 {
			c.Fail("地图格式非geoJson，无法解析")
			return
		}

		var geoJson dto.GeoJson
		err = json.Unmarshal([]byte(hdMap.Data), &geoJson)
		if err != nil {
			c.Fail("geoJson格式地图解析失败：" + err.Error())
			return
		}

		var returnMap dto.ReturnMap
		returnMap.AreaId = fmt.Sprintf("%d", hdMap.Id)
		returnMap.AreaName = hdMap.Name

		var stops []dto.Stops

		for _, v := range geoJson.Features {
			if v.Properties.Type == "route" {
				position := v.Geometry.Coordinates[0].([]interface{})
				position1 := position[0].([]interface{})
				returnMap.Boundary = append(returnMap.Boundary, dto.Position{
					Lon: position1[0].(float64),
					Lat: position1[1].(float64),
				})
			} else if v.Properties.Type == "station" {
				position := v.Geometry.Coordinates[0].([]interface{})
				stops = append(stops, dto.Stops{
					StopId:   v.Properties.PolygonID,
					StopName: v.Properties.PolygonID,
					Location: dto.Position{
						Lon: position[0].(float64),
						Lat: position[1].(float64),
					},
				})
			}
		}

		returnMap.Paths = append(returnMap.Paths, dto.Paths{
			PathId:     "1",
			PathName:   "路线1",
			PathPoints: returnMap.Boundary,
			Stops:      stops,
		})

		url := "https://api.thousandsim.com/open/api?method=qiancen.rop.vehicle.area.report"

		err = common.PostData(returnMap, url)
		if err != nil {
			c.Fail("地图同步失败：" + err.Error())
			return
		}
	}

	flag := 0
	if oldUser.Tel != tel {
		flag = 1
	}

	err = c.service.Update(id, username, password, _type, status, name, tip, parentId, hdMapId, roleId, realName, tel,
		email, allId, flag, netDisk, flow, flowTime)

	if err != nil {
		c.Fail("修改失败：" + err.Error())
		return
	}

	// 更新用户信息到 Redis
	userInfoKey := fmt.Sprintf("%s%d", common.RedisUserInfof, id)
	redis.HSet(userInfoKey, "name", name)
	redis.HSet(userInfoKey, "tip", tip)
	redis.HSet(userInfoKey, "status", status)
	redis.HSet(userInfoKey, "hd_map_id", hdMapId)
	redis.HSet(userInfoKey, "updated_time", time.Now().Format("2006-01-02 15:04:05"))

	// if isXJMod { // 如果修改了状态, 则重置市级管理员缓存
	//	err = c.service.ResetUserCache(user.CompanyId)
	//	if err != nil {
	//		c.Fail("设置市级管理员缓存失败," + err.Error())
	//		return
	//	}
	// }

	// 操作日志
	logType := "修改用户数据"

	newData := map[string]interface{}{
		"username":  username,
		"status":    status,
		"name":      name,
		"tip":       tip,
		"parent_id": parentId,
		"hd_map_id": hdMapId,
		"role_id":   roleId,
		"tel":       tel,
		"email":     email,
		"type":      _type,
		"map_ids":   mapIds,
	}

	jsonData, _ := json.Marshal(newData)

	if e := c.AddOperationLog(userInfo, logType, 3, string(oldJsonData), string(jsonData), ""); e != nil {
		msg := fmt.Sprintf(AddLogFailed, logType, e)
		c.Fail(msg)
		return
	}

	c.Success(nil)
	return
}

// UpdatePwd 修改本账号密码和昵称
func (c *UserController) UpdatePwd() {
	var (
		err        error
		updateData dto.UpdatePwd
		userInfo   dto.UserInfo
		now        = time.Now().Format("2006-01-02 15:04:05")
	)

	data := c.Ctx.Input.RequestBody
	err = common.Validate(data, &updateData)

	if err != nil {
		c.Fail("参数错误," + err.Error())
		return
	}

	password := updateData.Password
	name := updateData.Name
	tip := updateData.Tip

	userInfo, err = c.CheckToken()
	if err != nil {
		c.Fail(err.Error())
		return
	}

	id := userInfo.Id
	if password == "" {
		c.Fail("请输入新密码")
		return
	}
	tip = ""

	col := map[string]interface{}{
		"tip":          tip,
		"updated_time": now,
	}

	if password != "" {
		col["password"] = common.MD5(password)
	}

	err = c.service.UpdatePwd(id, col)
	if err != nil {
		c.Fail("修改失败：" + err.Error())
		return
	}

	userInfoKey := fmt.Sprintf("%s%d", common.RedisUserInfof, id) // 更新信息到 Redis
	// v.Redis().Do("HSET", userInfoKey, "name", dataForm.Name)
	redis.HSet(userInfoKey, "tip", tip)
	redis.HSet(userInfoKey, "updated_time", now)

	// 操作日志
	logType := "修改密码"
	logMsg := fmt.Sprintf("%s: name(%s), tip(%s)",
		logType,
		name,
		tip,
	)

	if e := c.AddOperationLog(userInfo, logType, 3, "", logMsg, ""); e != nil {
		msg := fmt.Sprintf(AddLogFailed, logType, e)
		c.Fail(msg)
		return
	}

	c.Success(nil)
	return
}

// Order 看板排序变更
func (c *UserController) Order() {
	var (
		err       error
		editOrder dto.EditOrder
	)

	data := c.Ctx.Input.RequestBody
	err = common.Validate(data, &editOrder)

	if err != nil {
		c.Fail("参数错误," + err.Error())
		return
	}

	id := editOrder.Id
	orderId := editOrder.OrderId

	err = c.service.UpdateOrder(id, orderId)
	if err != nil {
		c.Fail("修改失败：" + err.Error())
		return
	}
	c.Success(nil)
	return
}

// XJList 获取市级管理员列表
func (c *UserController) XJList() {
	var (
		data []pix.User
		err  error
	)

	// key := fmt.Sprintf("%s", common.RedisXJUserList)
	//
	// redisData := redis.Get(key)
	// if redisData == "" {
	data, err = c.service.XJList()
	if err != nil {
		c.Fail("获取失败：" + err.Error())
		return
	}
	err = c.service.ResetUserCache()
	if err != nil {
		c.Fail("设置缓存失败：" + err.Error())
		return
	}
	c.Success(data)
	return
	// }
	// err := json.Unmarshal([]byte(redisData), &data)
	// if err != nil {
	//	c.Fail("json解析失败：" + err.Error())
	//	return
	// }
	// c.Success(data)
	// return
}

// Detail 获取用户详情
func (c *UserController) Detail() {
	id, err := c.GetInt64("id", 0)
	if err != nil {
		c.Fail("获取参数错误")
		return
	}

	// userInfo, err := c.CheckToken()
	// if err != nil {
	//	c.Fail(err.Error())
	//	return
	// }

	user, err := c.service.Detail(id)
	if err != nil {
		c.Fail("获取用户信息失败:" + err.Error())
		return
	}

	// if userInfo.Level == common.XZLevel && id != userInfo.ParentID {
	//	c.Fail("暂无权限")
	//	return
	// }
	//
	// if userInfo.Level == common.XGLevel && (int64(user.ParentId) != userInfo.Id && id != userInfo.Id) {
	//	c.Fail("暂无权限")
	//	return
	// }

	hdMap, _ := c.HdMapService.Find(user.Id)
	// if err != nil {
	//	c.Fail("此用户未设置运营地图")
	//	return
	// }

	var userDetail dto.UserWithMap

	userDetail.Id = user.Id
	userDetail.Username = user.Username
	userDetail.Name = user.Name
	userDetail.Tip = user.Tip
	userDetail.Level = user.Level
	userDetail.Status = user.Status
	userDetail.ParentId = user.ParentId
	userDetail.HdMapId = user.HdMapId
	userDetail.CreatedTime = user.CreatedTime
	userDetail.UpdatedTime = user.UpdatedTime
	userDetail.HdMapData = hdMap

	// var resp []dto.AllMapRe
	// var defaultData dto.DefaultData
	// var wxDefaultData dto.WxDefaultData
	// err = json.Unmarshal([]byte(userDetail.HdMapData), &defaultData)
	// if err != nil {
	//
	// }
	//
	// for _, i := range defaultData.StationPointList {
	//	wxDefaultData.StationPointList = append(wxDefaultData.StationPointList, dto.WxStationPointList{
	//		Id:           i.Id,
	//		Tag:          i.Tag,
	//		Name:         i.Name,
	//		Type:         i.Type,
	//		Latitude:     i.Lat,
	//		Longitude:    i.Lng,
	//		Alt:          i.Alt,
	//		Head:         i.Head,
	//		StayDuration: i.StayDuration,
	//		Raw:          i.Raw,
	//		Other:        i.Other,
	//		NamePosition: i.NamePosition,
	//		IsUsed:       i.IsUsed,
	//	})
	// }
	//
	// for _, j := range defaultData.RoutePointList {
	//	wxDefaultData.RoutePointList = append(wxDefaultData.RoutePointList, dto.WxRoutePointList{
	//		Id:        j.Id,
	//		Alt:       j.Alt,
	//		Head:      j.Head,
	//		Raw:       j.Raw,
	//		Latitude:  j.Lat,
	//		Longitude: j.Lng,
	//	})
	// }
	//
	// resp = append(resp, dto.AllMapRe{
	//	Username: userDetail.Username,
	//	Name:     userDetail.Name,
	//	MapData:  wxDefaultData,
	// })
	//
	// fmt.Println(len(resp[0].MapData.RoutePointList))
	c.Success(userDetail)
	return
}

// Child 获取所有子节点
func (c *UserController) Child() {
	userInfo, _ := c.CheckToken()
	// if err != nil {
	//	c.Fail(err.Error())
	//	return
	// }

	name := c.GetString("name", "")

	data, err := c.service.Child(userInfo, name)

	if err != nil {
		c.Fail(err.Error())
		return
	}

	c.Success(map[string]interface{}{
		"items": data,
	})

	return
}

// GetMap 获取用户的地图
func (c *UserController) GetMap() {
	id, err := c.GetInt("id", 0)
	if err != nil {
		c.Fail("获取参数错误")
		return
	}

	maps, err := c.service.GetMap(id)
	if err != nil {
		c.Fail("获取地图失败：" + err.Error())
		return
	}

	c.Success(maps)
}

// ChangeLogo 修改用户头像
func (c *UserController) ChangeLogo() {
	userInfo, _ := c.CheckToken()

	file, header, err := c.GetFile("file")
	filename := header.Filename
	position := strings.LastIndex(filename, ".")
	param := c.GetString("type", "logo")

	err = file.Close()
	if err != nil {
		c.Fail(err.Error())
		return
	}

	endpoint, _ := beego.AppConfig.String("endpoint")
	accessKeyId, _ := beego.AppConfig.String("accessKeyId")
	accessKeySecret, _ := beego.AppConfig.String("accessKeySecret")
	bucketName, _ := beego.AppConfig.String("bucketName")

	ossFilename := fmt.Sprintf("%s%d", common.RandomString("alpha", 5), time.Now().Unix()) + filename[position:]
	err = c.UploadOss(endpoint, accessKeyId, accessKeySecret, bucketName, ossFilename, file)

	if err != nil {
		c.Fail("上传到oss失败:" + err.Error())
		return
	}

	ossUrl, err := beego.AppConfig.String("ossUrl")
	ossUrl = ossUrl + ossFilename
	err = c.service.ChangeLogo(userInfo.Id, ossUrl, param)
	if err != nil {
		c.Fail(err.Error())
		return
	}

	userInfoKey := fmt.Sprintf("%s%d", common.RedisUserInfof, userInfo.Id)
	redis.HSet(userInfoKey, param, ossUrl)

	c.Success(ossUrl)
	return
}

// UpdateConfig 更新视频配置
func (c *UserController) UpdateConfig() {
	var (
		err          error
		updateConfig dto.UpdateConfig
	)

	userInfo, _ := c.CheckToken()
	exist := c.service.CheckConfig(userInfo.Id)
	if exist {
		err = c.service.UpdateConfig(userInfo.Id, updateConfig.Config)
	} else {
		err = c.service.AddConfig(userInfo.Id, updateConfig.Config)
	}

	if err != nil {
		c.Fail(err.Error())
		return
	}

	c.Success(nil)
	return
}

// GetConfig 获取视频配置
func (c *UserController) GetConfig() {
	userInfo, _ := c.CheckToken()
	config, err := c.service.GetConfig(userInfo.Id)
	if err != nil {
		c.Fail(err.Error())
		return
	}

	c.Success(config)
	return
}

// CheckChild 判断是否有子用户
func (c *UserController) CheckChild() {
	var (
		err    error
		postId dto.PostId
	)

	data := c.Ctx.Input.RequestBody
	err = common.Validate(data, &postId)

	if err != nil {
		c.Fail("参数错误," + err.Error())
		return
	}

	exist := c.service.CheckChild(postId.Id)
	c.Success(exist)
	return
}

// GetSetting 获取配置
func (c *UserController) GetSetting() {
	userInfo, _ := c.CheckToken()
	setting, err := c.service.GetSetting(userInfo.Id)
	if err != nil {
		c.Fail("获取失败," + err.Error())
		return
	}
	c.Success(setting)
	return
}

// UpdateSetting 更新配置
func (c *UserController) UpdateSetting() {
	var (
		err           error
		updateSetting dto.UpdateSetting
	)

	data := c.Ctx.Input.RequestBody
	err = common.Validate(data, &updateSetting)

	if err != nil {
		c.Fail("参数错误," + err.Error())
		return
	}

	userInfo, _ := c.CheckToken()
	err = c.service.UpdateSetting(userInfo.Id, updateSetting.System, updateSetting.Screen)
	if err != nil {
		c.Fail("更新失败," + err.Error())
		return
	}
	c.Success(nil)
	return
}

// SendSms 发送验证码
func (c *UserController) SendSms() {
	phone := c.GetString("phone")
	signName := "pixmoving"
	templateCode := "SMS_251745253"
	code := common.RandomString("nozero", 6)
	myCode := "{\"code\":" + code + "}"
	AccessKeyId, _ := beego.AppConfig.String("AccessKeyId")
	AccessKeySecret, _ := beego.AppConfig.String("AccessKeySecret")
	client, _err := common.CreateClient(tea.String(AccessKeyId), tea.String(AccessKeySecret))
	if _err != nil {
		c.Fail("服务初始化失败：" + _err.Error())
		return
	}

	request := &dysmsapi20170525.SendSmsRequest{
		PhoneNumbers:  &phone,
		TemplateCode:  &templateCode,
		SignName:      &signName,
		TemplateParam: &myCode,
	}

	_, err := client.SendSms(request)
	if err != nil {
		c.Fail("发送验证码失败：" + err.Error())
		return
	}

	err = c.service.UpdateCode(phone, code)
	if err != nil {
		c.Fail("更新验证码失败：" + err.Error())
		return
	}
	c.Success(nil)
	return
}

// CheckCode 验证验证码
func (c *UserController) CheckCode() {
	phone := c.GetString("phone")
	code := c.GetString("code")

	exist := c.service.CheckCode(phone, code)
	if !exist {
		c.Fail("验证码错误")
		return
	}

	err := c.service.UpdateCheck(phone)
	if err != nil {
		c.Fail("更新验证状态失败：" + err.Error())
		return
	}

	userInfo, _ := c.CheckToken()
	userInfoKey := fmt.Sprintf("%s%d", common.RedisUserInfof, userInfo.Id)
	redis.HSet(userInfoKey, "check", 1)
	c.Success(nil)
	return
}

// verifyCaptchaCode 验证验证码
// func (c *UserController) verifyCaptchaCode(captchaId, captchaCode string) error {
// 从Redis获取验证码
// b64s := redis.Get(fmt.Sprintf(RedisCaptchaKey, captchaId))
// if b64s == "" {
//	return fmt.Errorf("验证码已过期")
// }

// 验证验证码
// if !store.Verify(captchaId, captchaCode, true) {
//	return fmt.Errorf("验证码错误")
// }
//
// return nil
// }

// VerifyCaptcha 验证图形验证码接口
func (c *UserController) VerifyCaptcha() {
	var verifyCaptcha dto.VerifyCaptcha
	data := c.Ctx.Input.RequestBody
	err := common.Validate(data, &verifyCaptcha)
	if err != nil {
		c.Fail("参数错误," + err.Error())
		return
	}

	// 验证图形验证码
	if !store.Verify(verifyCaptcha.CaptchaId, verifyCaptcha.CaptchaCode, true) {
		c.Fail("验证码错误")
		return
	}

	// 如果验证码结果是非正数，要求重新获取
	if result, err := strconv.Atoi(verifyCaptcha.CaptchaCode); err == nil && result <= 0 {
		c.Fail("验证码已失效，请重新获取")
		return
	}

	// 验证成功后，将验证码ID存入Redis，并设置过期时间
	key := fmt.Sprintf(RedisCaptchaKey, verifyCaptcha.CaptchaId)
	err = redis.SetEx2(key, int(CaptchaExpireTime.Seconds()), "1")
	if err != nil {
		logs.Error("存储验证码到Redis失败: %v", err)
		c.Fail("存储验证码失败：" + err.Error())
		return
	}

	c.Success(nil)
}

// 验证码配置
const (
	RedisCaptchaKey   = "captcha:%s"    // 验证码Redis key格式
	CaptchaExpireTime = 1 * time.Minute // 验证码过期时间1分钟
)

// 验证码驱动配置
var store = base64Captcha.DefaultMemStore

// GetCaptcha 获取验证码
func (c *UserController) GetCaptcha() {
	// 从配置文件读取验证码配置
	captchaType, err := beego.AppConfig.String("captcha_type")
	if err != nil {
		logs.Error("读取验证码类型配置失败: %v", err)
		c.Fail("读取验证码配置失败")
		return
	}
	logs.Info("验证码类型: %s", captchaType)

	captchaWidth, err := beego.AppConfig.Int("captcha_width")
	if err != nil {
		logs.Error("读取验证码宽度配置失败: %v", err)
		c.Fail("读取验证码配置失败")
		return
	}
	logs.Info("验证码宽度: %d", captchaWidth)

	captchaHeight, err := beego.AppConfig.Int("captcha_height")
	if err != nil {
		logs.Error("读取验证码高度配置失败: %v", err)
		c.Fail("读取验证码配置失败")
		return
	}
	logs.Info("验证码高度: %d", captchaHeight)

	captchaLength, err := beego.AppConfig.Int("captcha_length")
	if err != nil {
		logs.Error("读取验证码长度配置失败: %v", err)
		c.Fail("读取验证码配置失败")
		return
	}
	logs.Info("验证码长度: %d", captchaLength)

	captchaNoise, err := beego.AppConfig.Int("captcha_noise")
	if err != nil {
		logs.Error("读取验证码噪点配置失败: %v", err)
		c.Fail("读取验证码配置失败")
		return
	}
	logs.Info("验证码噪点数量: %d", captchaNoise)

	captchaShowLine, err := beego.AppConfig.Int("captcha_show_line")
	if err != nil {
		logs.Error("读取验证码干扰线配置失败: %v", err)
		c.Fail("读取验证码配置失败")
		return
	}
	logs.Info("验证码干扰线数量: %d", captchaShowLine)

	var driver base64Captcha.Driver
	if captchaType == "math" {
		logs.Info("使用算术验证码")
		// 算术验证码配置
		driverMath := &base64Captcha.DriverMath{
			Height:          captchaHeight,
			Width:           captchaWidth,
			NoiseCount:      captchaNoise,
			ShowLineOptions: captchaShowLine,
			BgColor: &color.RGBA{
				R: 240,
				G: 240,
				B: 246,
				A: 246,
			},
			Fonts: []string{"wqy-microhei.ttc"},
		}
		driver = driverMath.ConvertFonts()
		logs.Info("算术验证码驱动配置完成")
	} else {
		logs.Info("使用数字验证码")
		// 数字验证码配置
		maxSkew, err := beego.AppConfig.Float("captcha_max_skew")
		if err != nil {
			logs.Error("读取验证码倾斜度配置失败: %v", err)
			c.Fail("读取验证码配置失败")
			return
		}
		logs.Info("验证码倾斜度: %f", maxSkew)

		driverDigit := &base64Captcha.DriverDigit{
			Height:   captchaHeight,
			Width:    captchaWidth,
			Length:   captchaLength,
			MaxSkew:  maxSkew,
			DotCount: captchaNoise,
		}
		driver = driverDigit
		logs.Info("数字验证码驱动配置完成")
	}

	// 创建验证码
	logs.Info("开始生成验证码")
	c1 := base64Captcha.NewCaptcha(driver, store)
	var id, b64s, answer string
	for {
		var err error
		id, b64s, answer, err = c1.Generate()
		if err != nil {
			logs.Error("生成验证码失败: %v", err)
			c.Fail("生成验证码失败：" + err.Error())
			return
		}

		// 如果是算术验证码，检查结果是否为正数
		if captchaType == "math" {
			if result, err := strconv.Atoi(answer); err == nil && result > 0 {
				break
			}
			// logs.Info("重新生成验证码：结果为非正数")
			continue
		}
		break
	}
	logs.Info("验证码生成成功, ID: %s, 答案: %s", id, answer)

	c.Success(map[string]interface{}{
		"id":    id,
		"image": b64s,
	})
	logs.Info("验证码接口调用成功")
}

// LoginJWT JWT登录
func (c *UserController) LoginJWT() {
	var loginData dto.LoginJWT
	data := c.Ctx.Input.RequestBody
	err := common.Validate(data, &loginData)
	if err != nil {
		c.Fail("参数错误," + err.Error())
		return
	}

	// 验证图形验证码ID是否已验证
	result := redis.Get(fmt.Sprintf(RedisCaptchaKey, loginData.CaptchaId))
	if result != "1" {
		c.Fail("请先完成验证码验证")
		return
	}

	// 登录验证
	code, response, err, userInfo := c.authService.Login(loginData.Username, loginData.Password)
	if err != nil {
		c.Fail(err.Error())
		return
	}
	if code == 1 {
		c.Fail("用户名或密码错误")
		return
	}

	// 操作日志
	logType := "用户登录"
	logMsg := fmt.Sprintf("%s: %s",
		logType,
		loginData.Username,
	)

	if err = c.AddOperationLog(userInfo, logType, 3, "", logMsg, ""); err != nil {
		errStr := fmt.Sprintf(AddLogFailed, logType, err.Error())
		c.Fail(errStr)
		return
	}

	// 登录成功后删除验证码记录
	redis.Del(fmt.Sprintf(RedisCaptchaKey, loginData.CaptchaId))

	c.Success(response)
}

// LogoutJWT JWT登出
func (c *UserController) LogoutJWT() {
	token := c.Ctx.Request.Header.Get("X-Token")
	if token == "" {
		c.Success(nil)
		return
	}

	err := c.authService.Logout(token)
	if err != nil {
		c.Fail(err.Error())
		return
	}

	c.Success(nil)
	return
}

// RefreshToken 刷新token
func (c *UserController) RefreshToken() {
	var (
		req dto.RefreshTokenRequest
		err error
	)

	data := c.Ctx.Input.RequestBody
	err = common.Validate(data, &req)
	if err != nil {
		c.Fail("参数错误," + err.Error())
		return
	}

	newToken, err := c.authService.RefreshToken(req.RefreshToken)
	if err != nil {
		c.CustomError(402, err.Error())
		return
	}

	c.Success(newToken)
	return
}
