package controllers

import (
	"ccapi/common"
	"ccapi/common/protocol"
	"ccapi/models/dto"
	"ccapi/server/redis"
	"ccapi/service"
	"encoding/json"
	"fmt"
	"time"

	beego "github.com/beego/beego/v2/server/web"
)

// VehicleController 杭州车辆合作模块
type VehicleController struct {
	BaseController
	service       service.VehicleServiceInter
	deviceService service.DeviceServiceInter
	taskService   service.TaskServiceInter
}

// InitVehicleRouter 初始化路由
func InitVehicleRouter() beego.LinkNamespace {
	return beego.NSNamespace("/vehicle",
		beego.NSRouter("/task/start", &VehicleController{}, "post:Start"),                 // 车辆任务控制开始
		beego.NSRouter("/task/stop", &VehicleController{}, "post:Stop"),                   // 停止任务
		beego.NSRouter("/task/pause", &VehicleController{}, "post:Pause"),                 // 暂停任务
		beego.NSRouter("/task/resume", &VehicleController{}, "post:Resume"),               // 恢复任务
		beego.NSRouter("/task/emergencyStop", &VehicleController{}, "post:EmergencyStop"), // 紧急停车
		beego.NSRouter("/task/return", &VehicleController{}, "post:Return"),               // 一键回家
		beego.NSRouter("/task/charge", &VehicleController{}, "post:Charge"),               // 充电
		beego.NSRouter("/map/list", &VehicleController{}, "get:MapList"),                  // 获取地图列表
	)
}

// Prepare 注册服务
func (c *VehicleController) Prepare() {
	c.service = service.NewVehicleService()
	c.deviceService = service.NewDeviceService()
	c.taskService = service.NewTaskService()
}

// Start 车辆任务控制开始
func (c *VehicleController) Start() {
	var (
		err        error
		start      dto.Start
		child      = make([]dto.TaskChild, 0)
		actionList = make([]dto.TaskContent, 0)
	)

	data := c.Ctx.Input.RequestBody
	err = common.Validate(data, &start)

	if err != nil {
		c.HZFail("参数错误," + err.Error())
		return
	}

	if start.MapId == 0 {
		c.HZFail("参数错误, mapId不可为0。")
		return
	}

	id := start.VehicleId
	stopId := start.TargetStopId
	stringStopId := fmt.Sprintf("%d", stopId)

	device, err := c.deviceService.Find(id)
	if err != nil {
		c.HZFail("获取车辆信息失败：" + err.Error())
		return
	}

	if device.TaskId != 0 {
		c.HZFail("BUSY")
		return
	}

	taskName := "杭州车队站点任务" + stringStopId

	child = append(child, dto.TaskChild{
		Type:      0,
		Name:      stringStopId,
		Action:    "goPoint",
		Content:   "{\"action\":\"goPoint\",\"pointType\":2,\"stationId\":" + stringStopId + ",\"tailPointId\":1,\"headPointId\":1,\"lat\":23.5,\"lng\":123.6,\"heading\":123.6,\"missionOrder\":1,\"extraActionList\":[]}",
		Order:     1,
		Condition: "{\"type\":1}",
	})

	add, err := c.taskService.Add(taskName, "", 1, device.Imsi, 0, 0, 0, 0, "",
		0, 0, 0, child, start.MapId)
	if err != nil {
		c.HZFail("新增任务失败：" + err.Error())
		return
	}

	newChild, _ := c.taskService.Child(add)
	actionList = append(actionList, dto.TaskContent{
		Action:  newChild.Action,
		Content: newChild.Content,
		PST:     int(time.Now().Unix()),
		PET:     0,
	})

	err = c.taskService.UpdateDevice(device.Imsi, start.MapId, int64(newChild.Id))
	if err != nil {
		c.HZFail("地图id更新失败，" + err.Error())
		return
	}

	cmdData := map[string]interface{}{
		"imsi":       device.Imsi,
		"cmd":        protocol.CmdMap[9],
		"taskId":     newChild.Id,
		"routeId":    start.MapId,
		"name":       taskName,
		"desp":       "",
		"actionList": actionList,
	}

	err = c.deviceService.SendMessage(cmdData)

	if err != nil {
		c.HZFail("下发消息失败：" + err.Error())
		return
	}

	userInfo, _ := c.CheckToken()

	// 操作日志
	logType := protocol.LogMap[9]
	lastHbJsonStr, err := redis.HGet(common.RedisLastHeartbeat, device.Imsi)
	if err != nil {
		lastHbJsonStr = ""
	}
	var heartData dto.RedisHeart
	if lastHbJsonStr != "" {
		err = json.Unmarshal([]byte(lastHbJsonStr), &heartData)
		if err != nil {
			c.HZFail("心跳数据解析失败：" + err.Error())
			return
		}
	}

	logMsg := fmt.Sprintf("终端(%s)%s: Lng:%.6f, Lat:%.6f",
		device.Imsi,
		logType,
		heartData.Lng,
		heartData.Lat,
	)

	_, err = c.AddOperationLogWithId(userInfo, logType, 4, "", logMsg, device.Imsi)

	if err != nil {
		msg := fmt.Sprintf(AddLogFailed, logType, err)
		c.HZFail(msg)
		return
	}

	go func() {
		time.Sleep(time.Duration(3) * time.Second)
		cmdData2 := map[string]interface{}{
			"imsi": device.Imsi,
			"cmd":  protocol.CmdMap[16],
		}
		c.deviceService.SendMessage(cmdData2)
		// 操作日志
		logType = protocol.LogMap[1]
		logMsg = fmt.Sprintf("终端(%s)%s: Lng:%.6f, Lat:%.6f",
			device.Imsi,
			logType,
			heartData.Lng,
			heartData.Lat,
		)
		c.AddOperationLogWithId(userInfo, logType, 4, "", logMsg, device.Imsi)
	}()

	redisData, _ := json.Marshal(map[string]interface{}{
		"cmd": protocol.CmdMap[9],
	})
	// 设置到 Redis 缓存 (120s
	redisKey := fmt.Sprintf(common.RedisOperationPbMsg, device.Imsi, protocol.CmdMap[9])
	redis.SetEx(redisKey, 120, redisData)

	c.HZSuccess(newChild.Id)
	return
}

// Stop 停止任务
func (c *VehicleController) Stop() {
	var (
		err  error
		task dto.TaskPost
	)

	data := c.Ctx.Input.RequestBody
	err = common.Validate(data, &task)

	if err != nil {
		c.HZFail("参数错误," + err.Error())
		return
	}

	userInfo, _ := c.CheckToken()

	device, err := c.service.FindDevice(task.TaskId)
	if err != nil {
		c.HZFail("该任务暂无设备执行")
		return
	}

	_, err = c.service.UpdateDevice(task.TaskId)
	if err != nil {
		c.HZFail("任务停止失败，" + err.Error())
		return
	}

	child, err := c.taskService.FindChild(int64(task.TaskId))
	if err != nil {
		c.HZFail("获取子任务失败：" + err.Error())
		return
	}

	// 获取任务信息以获取任务名称
	taskInfo, err := c.taskService.Find(int64(child.ParentId))
	if err != nil {
		c.HZFail("获取任务失败：" + err.Error())
		return
	}

	err = c.taskService.AddLog(device.Imsi, int(child.ParentId), child.Id, 6, "用户停止了任务", userInfo.Id, taskInfo.Name)
	if err != nil {
		c.HZFail("日志新增失败，请联系管理员")
		return
	}

	cmdData := map[string]interface{}{
		"imsi": device.Imsi,
		"cmd":  protocol.CmdMap[2],
	}

	err = c.deviceService.SendMessage(cmdData)
	if err != nil {
		c.HZFail("下发消息失败：" + err.Error())
		return
	}

	c.HZSuccess(nil)
	return
}

// Pause 暂停任务
func (c *VehicleController) Pause() {
	var (
		err  error
		task dto.TaskPost
	)

	data := c.Ctx.Input.RequestBody
	err = common.Validate(data, &task)

	if err != nil {
		c.HZFail("参数错误," + err.Error())
		return
	}

	device, err := c.service.FindDevice(task.TaskId)
	if err != nil {
		c.HZFail("该任务暂无设备执行")
		return
	}

	cmdData := map[string]interface{}{
		"imsi": device.Imsi,
		"cmd":  protocol.CmdMap[19],
	}

	err = c.deviceService.SendMessage(cmdData)
	if err != nil {
		c.HZFail("下发消息失败：" + err.Error())
		return
	}

	c.HZSuccess(nil)
	return
}

// Resume 恢复任务
func (c *VehicleController) Resume() {
	var (
		err  error
		task dto.TaskPost
	)

	data := c.Ctx.Input.RequestBody
	err = common.Validate(data, &task)

	if err != nil {
		c.HZFail("参数错误," + err.Error())
		return
	}

	device, err := c.service.FindDevice(task.TaskId)
	if err != nil {
		c.HZFail("该任务暂无设备执行")
		return
	}

	cmdData := map[string]interface{}{
		"imsi": device.Imsi,
		"cmd":  protocol.CmdMap[1],
	}

	err = c.deviceService.SendMessage(cmdData)
	if err != nil {
		c.HZFail("下发消息失败：" + err.Error())
		return
	}

	c.HZSuccess(nil)
	return
}

// EmergencyStop 紧急停车
func (c *VehicleController) EmergencyStop() {
	var (
		err  error
		task dto.VehiclePost
	)

	data := c.Ctx.Input.RequestBody
	err = common.Validate(data, &task)

	if err != nil {
		c.HZFail("参数错误," + err.Error())
		return
	}

	device, err := c.deviceService.Find(task.VehicleId)
	if err != nil {
		c.HZFail("设备查询失败")
		return
	}

	cmdData := map[string]interface{}{
		"imsi": device.Imsi,
		"cmd":  protocol.CmdMap[3],
	}

	err = c.deviceService.SendMessage(cmdData)
	if err != nil {
		c.HZFail("下发消息失败：" + err.Error())
		return
	}

	c.HZSuccess(nil)
	return
}

// Return 一键回家
// func (c *VehicleController) Return() {
//	var (
//		err  error
//		task dto.VehiclePost
//	)
//
//	data := c.Ctx.Input.RequestBody
//	err = common.Validate(data, &task)
//
//	if err != nil {
//		c.HZFail("参数错误," + err.Error())
//		return
//	}
//
//	device, err := c.deviceService.Find(task.VehicleId)
//	if err != nil {
//		c.HZFail("设备查询失败")
//		return
//	}
//
//	cmdData := map[string]interface{}{
//		"imsi": device.Imsi,
//		"cmd":  protocol.CmdMap[21],
//	}
//
//	err = c.deviceService.SendMessage(cmdData)
//	if err != nil {
//		c.HZFail("下发消息失败：" + err.Error())
//		return
//	}
//
//	c.HZSuccess(nil)
//	return
// }

// MapList 地图列表
func (c *VehicleController) MapList() {
	maps, err := c.service.MapList()
	if err != nil {
		c.HZFail("获取地图失败：" + err.Error())
		return
	}
	c.HZSuccess(maps)
	return
}

// Charge 车辆充电
func (c *VehicleController) Charge() {
	var (
		err        error
		start      dto.VehiclePost
		child      = make([]dto.TaskChild, 0)
		actionList = make([]dto.TaskContent, 0)
	)

	data := c.Ctx.Input.RequestBody
	err = common.Validate(data, &start)

	if err != nil {
		c.HZFail("参数错误," + err.Error())
		return
	}

	id := start.VehicleId
	stopId := 10350
	stringStopId := fmt.Sprintf("%d", stopId)

	device, err := c.deviceService.Find(id)
	if err != nil {
		c.HZFail("获取车辆信息失败：" + err.Error())
		return
	}

	if device.TaskId != 0 {
		c.HZFail("BUSY")
		return
	}

	taskName := "杭州车队站点任务" + stringStopId

	child = append(child, dto.TaskChild{
		Type:    0,
		Name:    stringStopId,
		Action:  "goStation",
		Content: "{\"action\":\"goPoint\",\"pointType\":2,\"stationId\":" + stringStopId + ",\"tailPointId\":1,\"headPointId\":1,\"lat\":23.5,\"lng\":123.6,\"heading\":123.6,\"missionOrder\":1,\"extraActionList\":[]}",
		Order:   1,
	})

	add, err := c.taskService.Add(taskName, "", 1, device.Imsi, 0, 0, 0, 0, "",
		0, 0, 0, child, device.MapId)
	if err != nil {
		c.HZFail("新增任务失败：" + err.Error())
		return
	}

	newChild, _ := c.taskService.Child(add)
	actionList = append(actionList, dto.TaskContent{
		Action:  newChild.Action,
		Content: newChild.Content,
		PST:     int(time.Now().Unix()),
		PET:     0,
	})

	cmdData := map[string]interface{}{
		"imsi":       device.Imsi,
		"cmd":        protocol.CmdMap[9],
		"taskId":     newChild.Id,
		"routeId":    device.MapId,
		"name":       taskName,
		"desp":       "",
		"actionList": actionList,
	}

	err = c.deviceService.SendMessage(cmdData)

	if err != nil {
		c.HZFail("下发消息失败：" + err.Error())
		return
	}

	err = c.taskService.UpdateDevice(device.Imsi, device.MapId, add)
	if err != nil {
		c.HZFail("地图id更新失败，" + err.Error())
		return
	}

	userInfo, _ := c.CheckToken()

	// 操作日志
	logType := protocol.LogMap[9]
	lastHbJsonStr, err := redis.HGet(common.RedisLastHeartbeat, device.Imsi)
	if err != nil {
		lastHbJsonStr = ""
	}
	var heartData dto.RedisHeart
	if lastHbJsonStr != "" {
		err = json.Unmarshal([]byte(lastHbJsonStr), &heartData)
		if err != nil {
			c.HZFail("心跳数据解析失败：" + err.Error())
			return
		}
	}

	logMsg := fmt.Sprintf("终端(%s)%s: Lng:%.6f, Lat:%.6f",
		device.Imsi,
		logType,
		heartData.Lng,
		heartData.Lat,
	)

	_, err = c.AddOperationLogWithId(userInfo, logType, 4, "", logMsg, device.Imsi)

	if err != nil {
		msg := fmt.Sprintf(AddLogFailed, logType, err)
		c.HZFail(msg)
		return
	}

	go func() {
		time.Sleep(time.Duration(3) * time.Second)
		cmdData2 := map[string]interface{}{
			"imsi": device.Imsi,
			"cmd":  protocol.CmdMap[16],
		}
		c.deviceService.SendMessage(cmdData2)
		// 操作日志
		logType = protocol.LogMap[1]
		logMsg = fmt.Sprintf("终端(%s)%s: Lng:%.6f, Lat:%.6f",
			device.Imsi,
			logType,
			heartData.Lng,
			heartData.Lat,
		)
		c.AddOperationLogWithId(userInfo, logType, 4, "", logMsg, device.Imsi)
	}()

	redisData, _ := json.Marshal(map[string]interface{}{
		"cmd": protocol.CmdMap[9],
	})
	// 设置到 Redis 缓存 (120s
	redisKey := fmt.Sprintf(common.RedisOperationPbMsg, device.Imsi, protocol.CmdMap[9])
	redis.SetEx(redisKey, 120, redisData)

	c.HZSuccess(add)
	return
}

// Return 车辆回到停放区
func (c *VehicleController) Return() {
	var (
		err        error
		start      dto.VehiclePost
		child      = make([]dto.TaskChild, 0)
		actionList = make([]dto.TaskContent, 0)
	)

	data := c.Ctx.Input.RequestBody
	err = common.Validate(data, &start)

	if err != nil {
		c.HZFail("参数错误," + err.Error())
		return
	}

	id := start.VehicleId
	stopId := 10353
	stringStopId := fmt.Sprintf("%d", stopId)

	device, err := c.deviceService.Find(id)
	if err != nil {
		c.HZFail("获取车辆信息失败：" + err.Error())
		return
	}

	if device.TaskId != 0 {
		c.HZFail("BUSY")
		return
	}

	taskName := "杭州车队站点任务" + stringStopId

	child = append(child, dto.TaskChild{
		Type:    0,
		Name:    stringStopId,
		Action:  "goStation",
		Content: "{\"action\":\"goPoint\",\"pointType\":2,\"stationId\":" + stringStopId + ",\"tailPointId\":1,\"headPointId\":1,\"lat\":23.5,\"lng\":123.6,\"heading\":123.6,\"missionOrder\":1,\"extraActionList\":[]}",
		Order:   1,
	})

	add, err := c.taskService.Add(taskName, "", 1, device.Imsi, 0, 0, 0, 0, "",
		0, 0, 0, child, device.MapId)
	if err != nil {
		c.HZFail("新增任务失败：" + err.Error())
		return
	}

	newChild, _ := c.taskService.Child(add)
	actionList = append(actionList, dto.TaskContent{
		Action:  newChild.Action,
		Content: newChild.Content,
		PST:     int(time.Now().Unix()),
		PET:     0,
	})

	cmdData := map[string]interface{}{
		"imsi":       device.Imsi,
		"cmd":        protocol.CmdMap[9],
		"taskId":     newChild.Id,
		"routeId":    device.MapId,
		"name":       taskName,
		"desp":       "",
		"actionList": actionList,
	}

	err = c.deviceService.SendMessage(cmdData)

	if err != nil {
		c.HZFail("下发消息失败：" + err.Error())
		return
	}

	err = c.taskService.UpdateDevice(device.Imsi, device.MapId, add)
	if err != nil {
		c.HZFail("地图id更新失败，" + err.Error())
		return
	}

	userInfo, _ := c.CheckToken()

	// 操作日志
	logType := protocol.LogMap[9]
	lastHbJsonStr, err := redis.HGet(common.RedisLastHeartbeat, device.Imsi)
	if err != nil {
		lastHbJsonStr = ""
	}
	var heartData dto.RedisHeart
	if lastHbJsonStr != "" {
		err = json.Unmarshal([]byte(lastHbJsonStr), &heartData)
		if err != nil {
			c.HZFail("心跳数据解析失败：" + err.Error())
			return
		}
	}

	logMsg := fmt.Sprintf("终端(%s)%s: Lng:%.6f, Lat:%.6f",
		device.Imsi,
		logType,
		heartData.Lng,
		heartData.Lat,
	)

	_, err = c.AddOperationLogWithId(userInfo, logType, 4, "", logMsg, device.Imsi)

	if err != nil {
		msg := fmt.Sprintf(AddLogFailed, logType, err)
		c.HZFail(msg)
		return
	}

	go func() {
		time.Sleep(time.Duration(3) * time.Second)
		cmdData2 := map[string]interface{}{
			"imsi": device.Imsi,
			"cmd":  protocol.CmdMap[16],
		}
		c.deviceService.SendMessage(cmdData2)
		// 操作日志
		logType = protocol.LogMap[1]
		logMsg = fmt.Sprintf("终端(%s)%s: Lng:%.6f, Lat:%.6f",
			device.Imsi,
			logType,
			heartData.Lng,
			heartData.Lat,
		)
		c.AddOperationLogWithId(userInfo, logType, 4, "", logMsg, device.Imsi)
	}()

	redisData, _ := json.Marshal(map[string]interface{}{
		"cmd": protocol.CmdMap[9],
	})
	// 设置到 Redis 缓存 (120s
	redisKey := fmt.Sprintf(common.RedisOperationPbMsg, device.Imsi, protocol.CmdMap[9])
	redis.SetEx(redisKey, 120, redisData)

	c.HZSuccess(add)
	return
}
