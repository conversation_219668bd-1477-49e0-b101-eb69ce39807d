package controllers

import (
	"ccapi/common"
	"ccapi/models/dto"
	"ccapi/server/redis"
	"ccapi/service"
	"encoding/json"
	"fmt"
	beego "github.com/beego/beego/v2/server/web"
	"regexp"
	"time"
)

// WxController PIX智行微信小程序
type WxController struct {
	BaseController
	service service.WxServiceInter
}

// InitWxRouter 初始化路由
func InitWxRouter() beego.LinkNamespace {
	return beego.NSNamespace("/wx",
		beego.NSRouter("/user/mobilelogin", &WxController{}, "post:MobileLogin"),     // 手机号登录
		beego.NSRouter("/sms/send", &WxController{}, "post:Send"),                    // 发送验证码
		beego.NSRouter("/sms/check", &WxController{}, "post:Check"),                  // 验证验证码
		beego.NSRouter("/user/logout", &WxController{}, "post:Logout"),               // 退出登录
		beego.NSRouter("/user/thirdregister", &WxController{}, "post:ThirdRegister"), // 微信登录
		beego.NSRouter("/user/decpdewx", &WxController{}, "post:Decpdewx"),           // 更新手机号和openid
		beego.NSRouter("/user/addremark", &WxController{}, "post:AddRemark"),         // 新增评论
		beego.NSRouter("/user/getremark", &WxController{}, "post:GetRemark"),         // 获取评论
		beego.NSRouter("/user/getorderlist", &WxController{}, "post:GetOrderList"),   // 获取订单列表
	)
}

// Prepare 注册服务
func (c *WxController) Prepare() {
	c.service = service.NewWxService()
}

// MobileLogin 手机验证码登录
func (c *WxController) MobileLogin() {
	mobile := c.GetString("mobile")
	registerType := c.GetString("register_type")

	phoneRegex := `^1[34578]d{9}$`
	reg := regexp.MustCompile(phoneRegex)
	result := reg.MatchString(mobile)
	if !result {
		c.Fail("手机号格式错误")
		return
	}

	user, _ := c.service.GetByMobile(mobile)

	ip := c.GetIp()

	if user.Id != 0 {
		if user.Status != "normal" {
			c.Fail("用户状态异常")
			return
		}

		err := c.service.Direct(user, ip)
		if err != nil {
			c.Fail("登录失败：" + err.Error())
			return
		} else {
			c.Success(user)
			return
		}
	} else {
		newUser, err := c.service.Register(mobile, mobile, "", 0, registerType, ip)
		if err != nil {
			c.Fail("登录失败：" + err.Error())
			return
		} else {
			err = c.service.Flush(mobile, "mobilelogin")
			if err != nil {
				c.Fail("清空验证码失败：" + err.Error())
				return
			}
			c.Success(newUser)
			return
		}
	}
}

// Send 发送验证码
func (c *WxController) Send() {
	mobile := c.GetString("mobile")
	event := c.GetString("event", "login")

	phoneRegex := `^1[34578]d{9}$`
	reg := regexp.MustCompile(phoneRegex)
	result := reg.MatchString(mobile)
	if !result {
		c.Fail("手机号格式错误")
		return
	}

	err, lastSend := c.service.LastSend(mobile, event)
	if err != nil {
		c.Fail("数据库错误," + err.Error())
		return
	}

	if time.Now().Unix()-lastSend < 60 {
		c.Fail("发送频繁")
		return
	}

	ip := c.GetIp()
	err, count := c.service.LastSendMinute(ip)
	if err != nil {
		c.Fail("查询失败：" + err.Error())
		return
	}

	if count >= 5 {
		c.Fail("发送频繁")
		return
	}

	user, _ := c.service.GetByMobile(mobile)
	if event == "register" && user.Id != 0 {
		c.Fail("已被注册")
		return
	} else if event == "changemobile" && user.Id != 0 {
		c.Fail("已被占用")
		return
	} else if (event == "changepwd" || event == "resetpwd") && user.Id == 0 {
		c.Fail("未注册")
		return
	}

	code := common.RandomString("nozero", 4)

	err = c.service.AddSms(mobile, code, event, ip)
	if err != nil {
		c.Fail("新增记录失败：" + err.Error())
		return
	}

	appKey, _ := beego.AppConfig.String("HUAWEIKey")
	appSecret, _ := beego.AppConfig.String("HUAWEISecret")

	apiAddress, _ := beego.AppConfig.String("HUAWEIApiAddress") // APP接入地址(在控制台"应用管理"页面获取)+接口访问URI
	sender, _ := beego.AppConfig.String("HUAWEISender")         // 国内短信签名通道号
	templateId, _ := beego.AppConfig.String("HUAWEITemplateId") // 模板ID
	// 条件必填,国内短信关注,当templateId指定的模板类型为通用模板时生效且必填,必须是已审核通过的,与模板类型一致的签名名称
	signature, _ := beego.AppConfig.String("HUAWEISignalName") // 签名名称

	body := common.BuildRequestBody(sender, mobile, templateId, code, "", signature)
	headers := make(map[string]string)
	headers["Content-Type"] = "application/x-www-form-urlencoded"
	headers["Authorization"] = "WSSE realm=\"SDP\",profile=\"UsernameToken\",type=\"Appkey\""
	headers["X-WSSE"] = common.BuildWsseHeader(appKey, appSecret)
	_, err = common.HUAWEIPost(apiAddress, []byte(body), headers)
	if err != nil {
		c.Fail("post失败：" + err.Error())
		return
	}

	c.Success("发送成功")
	return
}

// Check 验证验证码
func (c *WxController) Check() {
	mobile := c.GetString("mobile")
	event := c.GetString("event", "login")
	captcha := c.GetString("captcha")

	phoneRegex := `^1[34578]d{9}$`
	reg := regexp.MustCompile(phoneRegex)
	result := reg.MatchString(mobile)
	if !result {
		c.Fail("手机号格式错误")
		return
	}

	err, lastSend := c.service.LastSend(mobile, event)
	if err != nil {
		c.Fail("数据库错误," + err.Error())
		return
	}

	if time.Now().Unix()-lastSend > 600 {
		c.Fail("验证码已过期")
		return
	}

	user, _ := c.service.GetByMobile(mobile)
	if event == "register" && user.Id != 0 {
		c.Fail("已被注册")
		return
	} else if event == "changemobile" && user.Id != 0 {
		c.Fail("已被占用")
		return
	} else if (event == "changepwd" || event == "resetpwd") && user.Id == 0 {
		c.Fail("未注册")
		return
	}

	err, ret := c.service.CheckSms(mobile, captcha, event)
	if err != nil {
		c.Fail("验证失败：" + err.Error())
		return
	}

	if ret {
		c.Success("验证成功")
		return
	}
	c.Fail("验证码错误")
	return
}

// Logout 退出登录
func (c *WxController) Logout() {
	token := c.GetString("token")
	redis.Del(fmt.Sprintf(common.WxRedisSessionMultiTokenUid, token))

	c.Success("退出成功")
	return
}

// ThirdRegister 微信登录
func (c *WxController) ThirdRegister() {
	username := c.GetString("nickName")
	avatar := c.GetString("avatar")
	gender, err := c.GetInt("gender")
	registerType := c.GetString("register_type")
	iv := c.GetString("iv")
	code := c.GetString("code")
	encryptedData := c.GetString("encryptedData")
	if err != nil {
		c.Fail("参数错误")
		return
	}

	appid, _ := beego.AppConfig.String("appid")
	secret, _ := beego.AppConfig.String("secret")

	url := "https://api.weixin.qq.com/sns/jscode2session?appid=" + appid + "&secret=" + secret + "&js_code=" + code + "&grant_type=authorization_code"
	res := common.HttpGet(url)
	var wxLoginReturn dto.WxLoginReturn
	err = json.Unmarshal(res, &wxLoginReturn)
	if err != nil {
		c.Fail("json解析失败：" + err.Error())
		return
	}

	_, err = common.DecryptWXOpenData(appid, wxLoginReturn.SessionKey, encryptedData, iv)
	if err != nil {
		c.Fail("解密失败：" + err.Error())
		return
	}

	if username == "" {
		c.Fail("参数错误")
		return
	}

	ret, err := c.service.Register(username, "", avatar, gender, registerType, c.GetIp())
	if err != nil {
		c.Fail("登录失败：" + err.Error())
		return
	}

	if ret.Id != 0 {
		err = c.service.UpdateOpenid(ret.Id, wxLoginReturn.Openid)
		if err != nil {
			c.Fail("更新openid失败：" + err.Error())
			return
		}
		c.Success(nil)
		return
	} else {
		c.Fail("用户信息异常")
		return
	}
}

// Decpdewx 更新手机号和openid
func (c *WxController) Decpdewx() {
	userId, err := c.GetInt64("user_id")
	iv := c.GetString("iv")
	code := c.GetString("code")
	encryptedData := c.GetString("encryptedData")
	if err != nil {
		c.Fail("参数错误")
		return
	}

	appid, _ := beego.AppConfig.String("appid")
	secret, _ := beego.AppConfig.String("secret")

	url := "https://api.weixin.qq.com/sns/jscode2session?appid=" + appid + "&secret=" + secret + "&js_code=" + code + "&grant_type=authorization_code"
	res := common.HttpGet(url)
	var wxLoginReturn dto.WxLoginReturn
	err = json.Unmarshal(res, &wxLoginReturn)
	if err != nil {
		c.Fail("json解析失败：" + err.Error())
		return
	}

	data, err := common.DecryptWXOpenData(appid, wxLoginReturn.SessionKey, encryptedData, iv)
	if err != nil {
		c.Fail("解密失败：" + err.Error())
		return
	} else {
		err = c.service.UpdateOpenidPhone(userId, wxLoginReturn.Openid, data["phoneNumber"].(string))
		if err != nil {
			c.Fail("用户数据更新失败：" + err.Error())
			return
		}

		data["openid"] = wxLoginReturn.Openid
		c.Success(data)
		return
	}
}

// AddRemark 新增评论
func (c *WxController) AddRemark() {
	orderId, err := c.GetInt64("order_id")
	satisfy, err := c.GetInt("satisfy")
	content := c.GetString("content")

	if err != nil {
		c.Fail("参数错误")
		return
	}

	remark, _ := c.service.GetRemarkByOrderId(orderId)
	if remark.Id == 0 {
		err = c.service.AddRemark(orderId, content, satisfy)
		if err != nil {
			c.Fail("新增评论失败：" + err.Error())
			return
		}
	} else {
		err = c.service.UpdateRemark(remark.Id, content, satisfy)
		if err != nil {
			c.Fail("修改评论失败：" + err.Error())
			return
		}
	}

	c.Success(nil)
	return
}

// GetRemark 获取评论
func (c *WxController) GetRemark() {
	orderId, err := c.GetInt64("order_id")

	if err != nil {
		c.Fail("参数错误")
		return
	}

	remark, _ := c.service.GetRemarkByOrderId(orderId)
	order, err := c.service.GetOrder(remark.OrderId)
	if err != nil {
		c.Fail("获取订单失败：" + err.Error())
		return
	}

	ticket, err := c.service.GetTicket(order.TicketId)
	if err != nil {
		c.Fail("获取车票失败：" + err.Error())
		return
	}

	c.Success(map[string]interface{}{
		"remark": remark,
		"order":  order,
		"ticket": ticket,
	})
	return
}

// GetOrderList 获取订单列表
func (c *WxController) GetOrderList() {
	userId, err := c.GetInt64("user_id")
	if err != nil {
		c.Fail("参数错误")
		return
	}

	orderList, err := c.service.OrderList(userId)
	if err != nil {
		c.Fail("获取订单失败：" + err.Error())
		return
	}

	c.Success(orderList)
	return
}
