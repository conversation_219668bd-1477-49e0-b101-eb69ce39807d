# 🎯 函数参数输入功能实现文档

## 📋 功能概述

为动态参数系统的函数选择添加了参数输入功能，用户现在可以直接在UI界面中输入函数参数，而不需要手动编写完整的函数调用语法。

## ✨ 核心特性

### 1. **智能函数参数识别**
- 自动识别需要参数的函数（如 `monthsAgo`、`daysAgo`、`hoursAgo` 等）
- 根据函数定义显示相应的参数输入框
- 支持必填和可选参数的区分

### 2. **用户友好的参数输入界面**
- 在函数选择下拉框旁边自动显示参数输入框
- 只有当选择的函数需要参数时才显示输入框
- 提供参数说明和类型提示

### 3. **实时参数验证**
- 数字类型参数的格式验证
- 必填参数的空值检查
- 参数范围验证（如果定义了min/max）
- 实时显示验证错误信息

### 4. **智能函数调用生成**
- 自动组合生成完整的函数调用语法
- 支持多参数函数的参数组合
- 实时预览最终生成的函数调用

## 🔧 技术实现

### 支持的函数类型

#### 时间相关函数
```javascript
// 需要参数的时间函数
daysAgo(n)      // N天前的开始时间戳
monthsAgo(n)    // N个月前的开始时间戳  
hoursAgo(n)     // N小时前的时间戳
minutesAgo(n)   // N分钟前的时间戳
addDays(timestamp, days)  // 在指定时间基础上增加天数
startOfDay(timestamp)     // 获取指定时间戳当天的开始时间
endOfDay(timestamp)       // 获取指定时间戳当天的结束时间

// 无参数的时间函数
now()           // 当前时间戳
today()         // 今天开始时间戳
todayEnd()      // 今天结束时间戳
yesterday()     // 昨天开始时间戳
```

#### 工具函数
```javascript
random(min, max)  // 生成指定范围的随机数
hasRole(role)     // 检查用户是否具有指定角色
```

### 核心方法

#### 1. **函数调用解析**
```javascript
// 解析函数名和参数
const parseFunctionCall = (value: string) => {
  const match = value.match(/^(\w+)\((.*)\)$/)
  if (!match) return { functionName: value, params: [] }
  
  const functionName = match[1]
  const paramsStr = match[2].trim()
  
  if (!paramsStr) return { functionName, params: [] }
  
  const params = paramsStr.split(',').map(p => {
    const trimmed = p.trim()
    // 处理字符串和数字类型
    if ((trimmed.startsWith('"') && trimmed.endsWith('"')) || 
        (trimmed.startsWith("'") && trimmed.endsWith("'"))) {
      return trimmed.slice(1, -1)
    }
    const num = Number(trimmed)
    return isNaN(num) ? trimmed : num
  })
  
  return { functionName, params }
}
```

#### 2. **函数调用构建**
```javascript
// 构建函数调用字符串
const buildFunctionCall = (functionName: string, params: any[]) => {
  if (!functionName) return ''
  if (params.length === 0) return `${functionName}()`
  
  const paramStrs = params.map(p => {
    if (typeof p === 'string') return `"${p}"`
    return String(p)
  })
  
  return `${functionName}(${paramStrs.join(', ')})`
}
```

#### 3. **参数验证**
```javascript
// 验证参数值
const validateParamValue = (value: any, paramConfig: any) => {
  if (paramConfig.required && (value === '' || value === null || value === undefined)) {
    return { valid: false, message: '此参数为必填项' }
  }
  
  if (paramConfig.type === 'number') {
    const num = Number(value)
    if (isNaN(num)) {
      return { valid: false, message: '请输入有效的数字' }
    }
    
    if (paramConfig.min !== undefined && num < paramConfig.min) {
      return { valid: false, message: `数值不能小于 ${paramConfig.min}` }
    }
    if (paramConfig.max !== undefined && num > paramConfig.max) {
      return { valid: false, message: `数值不能大于 ${paramConfig.max}` }
    }
  }
  
  return { valid: true, message: '' }
}
```

## 🎨 UI界面设计

### 主参数配置界面
```vue
<!-- 函数选择器 -->
<div v-else-if="param.type === 'function'" class="space-y-2">
  <!-- 函数选择下拉框 -->
  <select
    :value="parseFunctionCall(param.value).functionName"
    class="select select-bordered select-sm flex-1"
    @change="onFunctionChange($event, param)"
  >
    <option value="">选择函数...</option>
    <!-- 函数选项 -->
  </select>

  <!-- 函数参数输入 -->
  <div 
    v-if="parseFunctionCall(param.value).functionName && functionNeedsParams(parseFunctionCall(param.value).functionName)"
    class="space-y-2 bg-base-100 p-3 rounded border border-base-300"
  >
    <div class="text-sm font-medium text-base-content/80">函数参数</div>
    <div 
      v-for="(paramConfig, paramIndex) in getFunctionParams(parseFunctionCall(param.value).functionName)"
      :key="paramIndex"
      class="grid grid-cols-3 gap-2 items-center"
    >
      <label class="text-xs text-base-content/70">
        {{ paramConfig.name }}
        <span v-if="paramConfig.required" class="text-error">*</span>
      </label>
      <input
        :value="parseFunctionCall(param.value).params[paramIndex] || ''"
        type="text"
        :class="[
          'input input-bordered input-xs',
          getParamValidation(param.value, paramIndex).valid ? '' : 'input-error'
        ]"
        :placeholder="paramConfig.description"
        @input="onParamValueChange($event, param, paramIndex)"
      />
      <div class="text-xs text-base-content/50">
        {{ paramConfig.type }}
      </div>
    </div>
  </div>

  <!-- 函数说明和预览 -->
  <div class="text-xs text-base-content/60 bg-base-200 p-2 rounded space-y-1">
    <div><strong>当前调用：</strong> 
      <code class="bg-base-300 px-1 rounded">{{ param.value }}</code>
    </div>
    <div v-if="!isFunctionCallValid(param.value)" class="text-warning">
      ⚠️ 函数参数配置不完整或有误
    </div>
  </div>
</div>
```

## 📊 使用示例

### 1. **配置3个月前的时间戳**
1. 选择函数类型：`function`
2. 选择函数：`monthsAgo() - 获取N个月前的开始时间戳`
3. 输入参数：`3`
4. 系统自动生成：`monthsAgo(3)`

### 2. **配置随机数范围**
1. 选择函数类型：`function`
2. 选择函数：`random() - 生成随机数`
3. 输入参数：
   - min: `1`
   - max: `100`
4. 系统自动生成：`random(1, 100)`

### 3. **配置时间范围数组**
```javascript
{
  key: 'timeRange',
  type: 'array',
  arrayElements: [
    {
      type: 'function',
      value: 'monthsAgo(3)',  // 3个月前
      description: '开始时间'
    },
    {
      type: 'function', 
      value: 'todayEnd()',    // 今天结束
      description: '结束时间'
    }
  ]
}
```

## 🧪 测试验证

### 测试页面
- **路径**: `/function-params-test`
- **功能**: 验证函数参数输入功能的正确性

### 测试用例
1. **单参数函数测试**: `monthsAgo(3)`、`daysAgo(7)`
2. **多参数函数测试**: `random(1, 100)`、`addDays(timestamp, 30)`
3. **参数验证测试**: 无效参数的错误处理
4. **数组元素函数测试**: 数组中的函数参数配置

## 🎯 用户体验提升

### 1. **直观的参数配置**
- 用户无需记住函数语法
- 参数输入框自动显示和隐藏
- 清晰的参数说明和类型提示

### 2. **实时反馈**
- 参数验证错误实时显示
- 函数调用预览实时更新
- 配置状态清晰可见

### 3. **错误预防**
- 必填参数的明确标识
- 参数类型的自动转换
- 无效配置的警告提示

## 🚀 后续优化方向

1. **参数类型扩展**: 支持更多参数类型（日期、布尔值等）
2. **参数联动**: 支持参数之间的依赖关系
3. **函数文档**: 集成更详细的函数说明文档
4. **参数模板**: 提供常用参数组合的快速模板

这个功能大大提升了动态参数系统的易用性，让用户可以更直观、更安全地配置复杂的函数参数！🎉
