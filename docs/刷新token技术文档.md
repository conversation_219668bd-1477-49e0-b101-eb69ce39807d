# API文档

## 服务器信息
- 开发环境: http://localhost:8086
- 生产环境: [需要补充实际地址]

## 登录流程
1. 获取验证码
   - 调用 `/user/get_captcha` 获取验证码图片和验证码ID
   - 将验证码图片显示给用户
   - 保存验证码ID用于后续验证

2. 用户输入验证码
   - 先调用 `/user/verify_captcha` 验证验证码是否正确
   - 验证码有效期为1分钟
   - 验证码只能使用一次

3. 登录
   - 调用 `/user/v2/login` 进行登录
   - 需要提供用户名、密码和验证码id
   - 登录成功后获得token、refreshToken和expiresIn（Unix时间戳）

4. Token使用和刷新
   - 使用token访问需要认证的接口
   - token过期后使用refreshToken获取新的token对
   - 建议在token过期前主动刷新token

## API接口说明

### 1. 获取验证码
```
GET /user/get_captcha

请求参数: 无

响应数据:
{
    "code": 0,
    "msg": "success",
    "data": {
        "id": "验证码ID",
        "image": "base64编码的验证码图片"
    }
}
```

### 2. 验证验证码
```
POST /user/verify_captcha
Content-Type: application/json

请求参数:
{
    "captchaId": "验证码ID",
    "captchaCode": "用户输入的验证码"
}

响应数据:
{
    "code": 0,
    "msg": "success",
    "data": null
}
```

### 3. 登录
```
POST /user/v2/login
Content-Type: application/json

请求参数:
{
    "username": "用户名",
    "password": "密码",
    "captchaId": "验证码ID"
}

响应数据:
{
    "code": 0,
    "msg": "success",
    "data": {
        "token": "访问令牌",
        "refreshToken": "刷新令牌",
        "expiresIn": 1665412345  // Unix时间戳，表示token的过期时间
    }
}
```

### 4. 刷新Token
```
POST /user/v2/token/refresh
Content-Type: application/json

请求参数:
{
    "refreshToken": "刷新令牌"
}

响应数据:
{
    "code": 0,
    "msg": "success",
    "data": {
        "token": "新的访问令牌",
        "refreshToken": "新的刷新令牌",
        "expiresIn": 1665412345  // Unix时间戳，表示token的过期时间
    }
}
```

### 5. 登出
```
POST /user/v2/logout
X-Token: {token}

请求参数: 无

响应数据:
{
    "code": 0,
    "msg": "success",
    "data": null
}
```

## 注意事项

### Token使用
- 所有需要认证的接口都需要在请求头中携带token:
  ```
  X-Token: {token}
  ```

### 验证码说明
- 验证码有效期为1分钟
- 验证码只能使用一次
- 支持两种验证码类型:
  - digit: 数字验证码
  - math: 算术验证码
- 验证码配置可在服务端配置文件中调整
- 实现细节:
  - 验证码使用内存存储和Redis结合的方式实现
  - 验证成功后，将验证码ID存入Redis，并设置1分钟过期时间
  - 使用算术验证码时，确保结果为正数
  - 登录成功后会删除已使用的验证码记录

### Token说明
- 有效期
  - token有效期为10分钟
  - refreshToken有效期为7天
- 登出处理
  - 登出后token和refreshToken立即失效
  - 登出后必须重新登录,即使token或refreshToken未过期也不能继续使用
- 过期处理
  - token过期后可以使用refreshToken获取新的token对
  - refreshToken过期后必须重新登录
  - 前端处理token过期的方式:
    1. 通过登录接口返回的expiresIn判断
       - expiresIn是Unix时间戳，表示token的过期时间点
       - 建议在token过期前5分钟自动刷新
       - 前端可以通过比较当前时间与expiresIn来判断是否需要刷新
    2. 通过接口响应判断
       - 当接口返回code=401时表示token已过期或无效
       - 此时可以使用refreshToken获取新的token对
       - 如果刷新token接口返回code=402,表示refreshToken已过期或无效,需要重新登录
- 错误码说明
  - 401: token已过期或无效
    ```json
    {
        "code": 401,
        "msg": "登录已失效, 请重新登录",
        "data": null
    }
    ```
  - 402: refreshToken已过期或无效
    ```json
    {
        "code": 402,
        "msg": "refresh token已过期或无效",
        "data": null
    }
    ```

### 错误处理
- 接口返回格式统一为:
  ```json
  {
      "code": 0,  // 0表示成功,非0表示失败
      "msg": "错误信息",
      "data": null
  }
  ```
- 常见错误码:
  - 0: 成功
  - 1: 失败,具体原因见msg字段
  - 401: token无效或过期
  - 402: refreshToken无效或过期

### Token存储策略

- token和refreshToken均存储于Redis，便于分布式环境下的统一校验和失效控制。
- 存储key格式如下：
  - 访问令牌（token）：`ccapi:user:token:{uid}:{sessionId}`
  - 刷新令牌（refreshToken）：`ccapi:user:refresh:{uid}:{sessionId}`
    - `{uid}`：用户ID，唯一标识一个用户。
    - `{sessionId}`：会话ID，每次登录生成唯一sessionId，支持多端并发登录。
- 存储内容：
  - key对应的value为实际的token或refreshToken字符串。
- 生命周期：
  - token的Redis过期时间与token本身有效期一致（如10分钟）。
  - refreshToken的Redis过期时间与refreshToken本身有效期一致（如7天）。
- 校验流程：
  - 每次接口请求时，服务端会从Redis读取对应key，校验token/refreshToken是否存在且与请求一致。
  - 登出、刷新token等操作会删除或覆盖对应的Redis key，实现即时失效。
- 兼容性：
  - 历史版本token兼容key：`SESSION::MULTI::TOKEN_{token}`，用于老token校验。

### 开发环境下的鉴权跳过机制

- 在开发环境（runmode=dev）下，系统默认不会全局跳过鉴权。
- 只有当请求头中携带 `X-Dev-Bypass-Auth: let-me-in` 时，才会跳过token和权限校验，直接放行，并在CheckToken方法中模拟超级管理员（id=1）。
- 未携带该请求头时，依然会严格校验token和权限，与生产环境一致。
- 该机制仅供开发调试使用，严禁在生产环境或公网环境下启用。
- 建议仅开发团队知晓该请求头及其用法，防止被滥用。