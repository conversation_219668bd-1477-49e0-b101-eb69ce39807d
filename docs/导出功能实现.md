# ccapi 项目导出功能技术讲解（CSV/Excel/PDF）

---

## 1. 概述

本项目支持订单数据的多格式导出，包括：
- **CSV**（纯文本，兼容性强）
- **Excel (xlsx)**（支持列宽、格式美观）
- **PDF**（适合打印、归档）

本文档将详细讲解三种导出方式的实现原理、关键代码、页面与列宽控制、分割线绘制、注意事项等，适合新手快速上手。

---

## 2. 关键代码文件

- `controllers/export.go`：导出接口控制器，处理前端导出请求，参数校验、鉴权、数据查询、分发导出。
- `service/export_service.go`：导出表格数据结构、字段元信息（如列宽）、数据格式化等。
- `exporters/excel.go`：Excel 导出实现，基于 excelize，支持列宽设置。
- `exporters/pdf.go`：PDF 导出实现，基于 gopdf，支持页面与列宽自适应、分割线绘制。
- `exporters/csv.go`：CSV 导出实现，基于 Go 标准库 encoding/csv。
- `exporters/factory.go`：导出器工厂，根据 fileType 返回对应导出实现。

---

## 3. 导出流程总览

1. 前端发起导出请求，指定字段、格式等参数。
2. 控制器（`ExportController`）校验参数、鉴权，组装查询条件。
3. service 层查询所有订单数据，构建导出表格结构（表头、内容、字段key）。
4. 根据 fileType 选择对应导出器（Excel/PDF/CSV）。
5. 调用导出器，将数据写入响应流，返回导出文件。

**注意**: 导出功能支持新的查询参数 `begin` 和 `end`，可以精确指定起点站和终点站进行导出。同时保持对旧参数 `beginOrEnd` 的兼容性。

---

## 4. 各格式导出实现细节

### 4.1 CSV 导出
- 代码文件：`exporters/csv.go`
- 实现方式：
  - 使用 Go 标准库 `encoding/csv`，直接写表头和内容。
  - **无列宽、格式控制**，所有内容为纯文本，兼容性强。
- 代码片段：
```go
func ExportCSV(w io.Writer, table service.ExportTable) error {
    writer := csv.NewWriter(w)
    defer writer.Flush()
    // 写表头
    if err := writer.Write(table.Headers); err != nil {
        return err
    }
    // 写内容
    for _, row := range table.Rows {
        if err := writer.Write(row); err != nil {
            return err
        }
    }
    return nil
}
```
- **注意：CSV 格式本身不支持列宽，打开方式（如Excel、记事本）会自动适配。**

---

### 4.2 Excel 导出
- 代码文件：`exporters/excel.go`
- 实现方式：
  - 基于 `excelize` 库，支持表头、内容、列宽设置。
  - 列宽读取自 `service.OrderExportMeta` 配置。
- 关键配置：
```go
// service/export_service.go
var OrderExportMeta = map[string]FieldMeta{
    "orderNo":   {Width: 25},
    "routeName": {Width: 25},
    ...
}
```
- 代码片段：
```go
for col, key := range table.FieldKeys {
    colName, _ := excelize.ColumnNumberToName(col + 1)
    var width float64 = 16 // 默认宽度
    if meta, ok := service.OrderExportMeta[key]; ok {
        width = meta.Width
    }
    file.SetColWidth(sheet, colName, colName, width)
}
```
- **每列宽度严格按配置设置，导出后在 Excel 中打开效果美观。**

---

### 4.3 PDF 导出
- 代码文件：`exporters/pdf.go`
- 实现方式：
  - 基于 `gopdf`，支持页面宽度自适应、列宽、自动换行、分割线。
  - 列宽读取自 `OrderPdfExportMeta` 配置。
  - 页面宽度 = 所有列宽之和 + 左右边距（最小A4宽度）。
- 关键配置：
```go
var OrderPdfExportMeta = map[string]int{
    "orderNo":           180,
    "routeName":         160,
    ...
}
```
- 页面宽度控制：
```go
colWidths := ... // 按字段查找OrderPdfExportMeta
sumWidth := ...  // 累加所有列宽
marginLeft, marginRight := 100.0, 100.0
minPageWidth := 595.0 // A4宽度
pageWidth := sumWidth + marginLeft + marginRight
if pageWidth < minPageWidth {
    pageWidth = minPageWidth
}
pdf.Start(gopdf.Config{PageSize: gopdf.Rect{W: pageWidth, H: pageHeight}})
```
- 分割线绘制：
```go
// 横线
for _, y := range ys {
    pdf.Line(xs[0], y-4, xs[len(xs)-1], y-4)
}
// 竖线
for _, x := range xs {
    pdf.Line(x, ys[0]-4, x, ys[len(ys)-1]-4)
}
```
- **自动换行、动态行高、分割线坐标均与内容对齐，保证美观。**

---

## 5. 页面大小与列宽控制

- **Excel**：页面大小由 Excel 软件自动适配，列宽严格按 `OrderExportMeta` 设置。
- **PDF**：页面宽度=所有列宽+边距，最小A4宽度，列宽按 `OrderPdfExportMeta` 设置。
- **CSV**：无页面和列宽概念，内容为纯文本。

---

## 6. 分割线绘制与注意事项（PDF）

- **分割线绘制原理**：
  - 横线：每一行底部Y坐标，整行画一条横线。
  - 竖线：每一列X坐标，从表头到最后一行底部画竖线。
- **注意事项**：
  - `ys`（每行底部Y坐标）和 `xs`（每列X坐标）必须与内容绘制严格同步，不能遗漏或多余。
  - 自动换行时，行高动态变化，`ys` 需动态累加。
  - 分割线Y坐标可适当加/减偏移（如-4pt）防止线条与文字重叠。
  - 若分割线错位，优先检查 `ys`、`xs` 生成逻辑与内容绘制是否一致。

---

## 7. 导出查询参数说明

### 7.1 站点查询参数（新功能）

导出功能现在支持更精确的站点查询：

#### 独立起点站查询
```json
{
  "fields": [{"key": "orderNo", "label": "订单号"}],
  "fileType": "xlsx",
  "begin": "北京站",
  "end": "",
  "beginOrEnd": ""
}
```

#### 独立终点站查询
```json
{
  "fields": [{"key": "orderNo", "label": "订单号"}],
  "fileType": "xlsx",
  "begin": "",
  "end": "上海站",
  "beginOrEnd": ""
}
```

#### 起点+终点组合查询
```json
{
  "fields": [{"key": "orderNo", "label": "订单号"}],
  "fileType": "xlsx",
  "begin": "北京站",
  "end": "上海站",
  "beginOrEnd": ""
}
```

#### 兼容性查询（保持原有逻辑）
```json
{
  "fields": [{"key": "orderNo", "label": "订单号"}],
  "fileType": "xlsx",
  "begin": "",
  "end": "",
  "beginOrEnd": "北京"
}
```

### 7.2 查询优先级
1. **优先使用新参数**: 如果 `begin` 或 `end` 有值，则使用精确查询
2. **兼容性支持**: 如果新参数为空但 `beginOrEnd` 有值，则使用原逻辑
3. **组合查询**: 支持同时指定 `begin` 和 `end` 进行精确导出

---

## 8. 新手上手指引

- **导出相关代码主要集中在 exporters/、service/export_service.go、controllers/export.go。**
- **流程：**
  1. 前端发起导出请求，指定字段、格式。
  2. 控制器校验、鉴权、查数据。
  3. service 层组装表格结构。
  4. exporters/ 下按 fileType 选择导出实现。
  5. Excel/PDF 支持列宽，PDF 支持页面宽度自适应和分割线。
- **如需调整列宽、页面大小、分割线等，优先修改 exporters/pdf.go、exporters/excel.go 及 service/export_service.go 的元信息配置。**

---

## 9. 常见问题与调试建议

### 9.1 导出格式问题
- **CSV 无法设置列宽**，如需美观请用 Excel 导出。
- **PDF 分割线错位**，多为 ys/xs 生成与内容绘制不一致。
- **导出字段顺序/内容异常**，优先检查 table.Headers、table.FieldKeys 组装逻辑。

### 9.2 查询参数问题
- **新参数不生效**：确认前端传递的参数名称正确（`begin`、`end`）
- **兼容性问题**：检查是否同时传递了新旧参数，新参数优先级更高
- **查询结果不准确**：验证数据库中的 `o.begin` 和 `o.end` 字段数据

### 9.3 扩展建议
- **如需支持更多格式或自定义导出，建议参考 exporters/factory.go 的工厂模式扩展。**
- **如需添加更多查询条件，可在 dto.OrderListQuery 中扩展字段。**

---

如有疑问，建议先阅读本文件，再结合源码调试。 