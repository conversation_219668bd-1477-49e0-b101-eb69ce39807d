package exporters

import (
	"ccapi/service"
	"io"
	"encoding/csv"
)

// ExportCSV 导出为 CSV 文件
func ExportCSV(w io.Writer, table service.ExportTable) error {
	writer := csv.NewWriter(w)
	defer writer.Flush()
	// 写表头
	if err := writer.Write(table.Headers); err != nil {
		return err
	}
	// 写内容
	for _, row := range table.Rows {
		if err := writer.Write(row); err != nil {
			return err
		}
	}
	return nil
} 