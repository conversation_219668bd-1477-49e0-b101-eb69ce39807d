package exporters

import (
	"ccapi/service"
	"io"
	"github.com/xuri/excelize/v2"
)

// ExportExcel 导出为 Excel 文件
func ExportExcel(w io.Writer, table service.ExportTable) error {
	file := excelize.NewFile()
	sheet := "Sheet1"
	file.SetSheetName("Sheet1", sheet)
	// 写表头并设置列宽
	for col, header := range table.Headers {
		cell, _ := excelize.CoordinatesToCellName(col+1, 1)
		file.SetCellValue(sheet, cell, header)
	}
	for col, key := range table.FieldKeys {
		colName, _ := excelize.ColumnNumberToName(col + 1)
		var width float64 = 16 // 默认宽度
		// 优先查找历史任务字段宽度，然后查找订单字段宽度
		if meta, ok := service.TaskExecutionExportMeta[key]; ok {
			width = meta.Width
		} else if meta, ok := service.OrderExportMeta[key]; ok {
			width = meta.Width
		}
		file.SetColWidth(sheet, colName, colName, width)
	}
	// 写内容
	for rowIdx, row := range table.Rows {
		for colIdx, val := range row {
			cell, _ := excelize.CoordinatesToCellName(colIdx+1, rowIdx+2)
			file.SetCellValue(sheet, cell, val)
		}
	}
	return file.Write(w)
} 