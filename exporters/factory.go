package exporters

import (
	"ccapi/service"
	"io"
)

// Exporter 导出器接口
// table: 导出表格数据，w: 输出流，fontPath: 字体路径（PDF用）
type Exporter interface {
	Export(w io.Writer, table service.ExportTable, fontPath string) error
}

// ExcelExporter 实现

type ExcelExporter struct{}

func (e *ExcelExporter) Export(w io.Writer, table service.ExportTable, fontPath string) error {
	return ExportExcel(w, table)
}

// PDFExporter 实现

type PDFExporter struct{}

func (p *PDFExporter) Export(w io.Writer, table service.ExportTable, fontPath string) error {
	return ExportPDF(w, table, fontPath)
}

// CSVExporter 实现

type CSVExporter struct{}

func (c *CSVExporter) Export(w io.Writer, table service.ExportTable, fontPath string) error {
	return ExportCSV(w, table)
}

// GetExporter 工厂方法
func GetExporter(fileType string) Exporter {
	switch fileType {
	case "pdf":
		return &PDFExporter{}
	case "csv":
		return &CSVExporter{}
	case "xlsx", "excel":
		return &ExcelExporter{}
	default:
		return &ExcelExporter{}
	}
}

// ExportExcel 导出为 Excel 文件
// 移除 ExportExcel 的实现，直接调用 exporters/excel.go 中的 ExportExcel 