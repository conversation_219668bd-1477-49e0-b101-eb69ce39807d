package exporters

import (
	"ccapi/service"
	"fmt"
	"io"
	"os"

	"github.com/signintech/gopdf"
)

// PDF专用字段宽度配置
var OrderPdfExportMeta = map[string]int{
	"orderNo":           180,
	"routeName":         160,
	"begin":             120,
	"end":               120,
	"mobile":            120,
	"num":               60,
	"carName":           130,
	"status":            100,
	"originalFee":       60,
	"couponId":          110,
	"discountedFee":     80,
	"totalFee":          80,
	"reserveTimeSlot":   180,
	"createdTime":       160,
	"timeEnd":           160,
	"refundSuccessTime": 160,
}

// 历史任务PDF专用字段宽度配置
var TaskExecutionPdfExportMeta = map[string]int{
	"id":                   80,
	"taskId":               80,
	"childId":              80,
	"executionNo":          80,
	"taskName":             200,
	"taskType":             120,
	"executionStatus":      120,
	"status":               120,
	"imsi":                 140,
	"vehiclePlate":         120,
	"vehicleType":          120,
	"plannedDistanceM":     160,
	"plannedDurationMin":   140,
	"actualDurationMin":    160,
	"actualMileageM":       160,
	"actualSpeedKmh":       120,
	"creator":              120,
	"startTime":            160, // 映射到createdTime
	"endTime":              160,
	"departureTime":        160,
	"arrivalTime":          160,
	"startLocation":        200,
	"endLocation":          200,
	"startCm":              120,
	"endCm":                120,
	"routeId":              80,
	"mapId":                80,
	"vehicleUsage":         120,
	"startMileageM":        160,
	"endMileageM":          160,
	"startStationId":       120,
	"endStationId":         120,
	"createdTime":          160,
	"updatedTime":          160,
	"runningTask":          160,
	"taskDescription":      240,
	"waypoints":            240,
	"executionHash":        200,
	"originalTaskContent":  240,
	"heartbeatDataSummary": 240,
}

func ExportPDF(w io.Writer, table service.ExportTable, fontPath string) error {
	pdf := &gopdf.GoPdf{}

	// 1. 动态计算表格总宽度
	colWidths := make([]float64, len(table.Headers))
	sumWidth := 0.0
	for i := range table.Headers {
		if table.FieldKeys != nil && i < len(table.FieldKeys) {
			k := table.FieldKeys[i]
			// 优先查找历史任务字段宽度，然后查找订单字段宽度
			if w, ok := TaskExecutionPdfExportMeta[k]; ok {
				colWidths[i] = float64(w)
			} else if w, ok := OrderPdfExportMeta[k]; ok {
				colWidths[i] = float64(w)
			} else {
				colWidths[i] = 80
			}
		} else {
			colWidths[i] = 80
		}
		sumWidth += colWidths[i]
	}

	// 2. 页面宽度 = 表格宽度 + 左右边距（各100pt），最小A4宽度（595pt）
	marginLeft := 100.0
	marginRight := 100.0
	minPageWidth := 595.0 // A4宽度
	pageWidth := sumWidth + marginLeft + marginRight
	if pageWidth < minPageWidth {
		pageWidth = minPageWidth
	}

	// 3. 页面高度保持不变（A4高度842pt，或原A3高度1684pt）
	pageHeight := 1684.0 // 如需A4高度可改为842.0

	pdf.Start(gopdf.Config{PageSize: gopdf.Rect{W: pageWidth, H: pageHeight}})
	pdf.AddPage()
	_ = pdf.AddTTFFont("noto", "fonts/NotoSansSC-Regular.ttf")
	_ = pdf.AddTTFFont("noto-bold", "fonts/NotoSansSC-Bold.ttf")
	pdf.SetFont("noto", "", 12)
	marginTop := 80.0
	marginBottom := 40.0
	cellH := 24.0
	pdf.SetY(marginTop)
	rowY := pdf.GetY()
	fields := table.Headers
	// 统计总权重
	sum := 0.0
	for _, field := range fields {
		if w, ok := OrderPdfExportMeta[field]; ok {
			sum += float64(w)
		} else {
			sum += 10 // 默认宽度
		}
	}
	// 动态计算每列最大内容长度
	maxLens := make([]int, len(table.Headers))
	for i, h := range table.Headers {
		maxLens[i] = len([]rune(h))
	}
	for _, row := range table.Rows {
		for i, col := range row {
			l := len([]rune(col))
			if l > maxLens[i] {
				maxLens[i] = l
			}
		}
	}
	// 固定每列宽度
	// 调试日志
	println("[DEBUG] table.FieldKeys:", fmt.Sprint(table.FieldKeys))
	println("[DEBUG] OrderPdfExportMeta keys:")
	for k := range OrderPdfExportMeta {
		println("  ", k)
	}
	pdf.SetFont("noto", "", 14)
	rowIdx := 0
	for {
		if rowIdx >= len(table.Rows) {
			break
		}
		// 每页开始
		pdf.SetFont("noto-bold", "", 16)
		pdf.SetX(marginLeft)
		yStart := pdf.GetY()
		// 计算每列x坐标
		xs := make([]float64, len(colWidths)+1)
		xs[0] = marginLeft
		for i := 0; i < len(colWidths); i++ {
			xs[i+1] = xs[i] + colWidths[i]
		}
		// 记录每行y坐标
		// 表头自动换行处理
		headLineCounts := make([]int, len(table.Headers))
		headLines := make([][]string, len(table.Headers))
		pdf.SetFont("noto-bold", "", 16)
		for i, header := range table.Headers {
			headLines[i] = wrapText(pdf, header, colWidths[i]-16)
			headLineCounts[i] = len(headLines[i])
		}
		headMaxLines := 1
		for _, n := range headLineCounts {
			if n > headMaxLines {
				headMaxLines = n
			}
		}
		headCellH := cellH * float64(headMaxLines)
		for lineIdx := 0; lineIdx < headMaxLines; lineIdx++ {
			for i := range table.Headers {
				pdf.SetX(xs[i] + 8)
				var txt string
				if lineIdx < len(headLines[i]) {
					txt = headLines[i][lineIdx]
				} else {
					txt = ""
				}
				rect := &gopdf.Rect{W: colWidths[i] - 16, H: cellH}
				pdf.CellWithOption(rect, txt, gopdf.CellOption{Align: gopdf.Left})
			}
			pdf.SetY(pdf.GetY() + cellH)
		}
		yEnd := yStart + headCellH
		ys := []float64{yStart}
		ys = append(ys, yEnd)
		pdf.SetY(yEnd + 8)
		rowY = pdf.GetY()
		pdf.SetFont("noto", "", 14)
		for ; rowIdx < len(table.Rows); rowIdx++ {
			// 数据行自动换行处理
			rowLineCounts := make([]int, len(table.Rows[rowIdx]))
			rowLines := make([][]string, len(table.Rows[rowIdx]))
			for i, col := range table.Rows[rowIdx] {
				rowLines[i] = wrapText(pdf, col, colWidths[i]-16)
				rowLineCounts[i] = len(rowLines[i])
			}
			rowMaxLines := 1
			for _, n := range rowLineCounts {
				if n > rowMaxLines {
					rowMaxLines = n
				}
			}
			rowCellH := cellH * float64(rowMaxLines)
			if rowY+rowCellH > pageHeight-marginBottom {
				break
			}
			yStart := pdf.GetY()
			for lineIdx := 0; lineIdx < rowMaxLines; lineIdx++ {
				for i := range table.Rows[rowIdx] {
					pdf.SetX(xs[i] + 8)
					var txt string
					if lineIdx < len(rowLines[i]) {
						txt = rowLines[i][lineIdx]
					} else {
						txt = ""
					}
					rect := &gopdf.Rect{W: colWidths[i] - 16, H: cellH}
					pdf.CellWithOption(rect, txt, gopdf.CellOption{Align: gopdf.Left})
				}
				pdf.SetY(pdf.GetY() + cellH)
			}
			yEnd := yStart + rowCellH
			ys = append(ys, yEnd)
			pdf.SetY(yEnd)
			rowY = pdf.GetY()
		}
		// 统一画分割线（每页独立，严格用内容x/y坐标）
		// 画横线
		for _, y := range ys {
			pdf.Line(xs[0], y-4, xs[len(xs)-1], y-4)
		}
		// 画竖线
		for _, x := range xs {
			pdf.Line(x, ys[0]-4, x, ys[len(ys)-1]-4)
		}
		if rowIdx < len(table.Rows) {
			pdf.AddPage()
			pdf.SetY(marginTop)
		}
	}

	if f, ok := w.(*os.File); ok {
		return pdf.WritePdf(f.Name())
	}
	tmp := "tmp_export.pdf"
	err := pdf.WritePdf(tmp)
	if err != nil {
		return err
	}
	defer os.Remove(tmp)
	f, err := os.Open(tmp)
	if err != nil {
		return err
	}
	defer f.Close()
	_, err = io.Copy(w, f)
	return err
}

// wrapText 按单元格宽度自动分行
func wrapText(pdf *gopdf.GoPdf, text string, maxWidth float64) []string {
	if text == "" {
		return []string{""}
	}
	var lines []string

	runes := []rune(text)
	start := 0
	for start < len(runes) {
		end := start
		for end < len(runes) {
			w, _ := pdf.MeasureTextWidth(string(runes[start : end+1]))
			if w > maxWidth {
				break
			}
			end++
		}
		if end == start {
			// 单个字符就超宽，强制断行
			end++
		}
		lines = append(lines, string(runes[start:end]))
		start = end
	}
	return lines
}
