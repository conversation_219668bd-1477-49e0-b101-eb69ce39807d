# GoView 外部系统集成指南

## 概述

GoView 支持通过标准URL参数的方式与外部系统集成，实现统一认证和无感token刷新。

## 核心特性

- ✅ **统一认证体系**：所有系统使用相同的认证服务
- ✅ **标准URL参数**：使用 `external_*` 格式的标准参数
- ✅ **无感token刷新**：自动刷新即将过期的token
- ✅ **自定义登录页面**：支持指定系统专属的登录页面
- ✅ **预览页面公开访问**：图表预览无需登录

## 快速开始

### 方式一：直接URL跳转（推荐）

```javascript
// 构建GoView URL
const goviewUrl = `http://your-goview-domain/#/project/items?` + new URLSearchParams({
  external_token: 'your-jwt-token',
  external_refresh_token: 'your-refresh-token',
  external_expires_in: '1234567890',
  external_user: 'username',
  external_nickname: 'display-name',
  external_user_id: 'user-id',
  external_system: 'your-system-name',
  external_login_url: 'http://your-system.com/login'
}).toString()

// 打开GoView
window.open(goviewUrl, '_blank')
```

### 方式二：使用集成工具类

```javascript
import { GoViewIntegration } from './GoViewIntegration.js'

const goview = new GoViewIntegration({
  baseUrl: 'http://your-goview-domain',
  systemName: 'your-system',
  loginUrl: 'http://your-system.com/login',
  getUserInfo: () => ({
    token: getCurrentToken(),
    refreshToken: getRefreshToken(),
    expiresIn: getTokenExpiry(),
    username: getCurrentUsername(),
    nickname: getCurrentNickname(),
    userId: getCurrentUserId()
  })
})

// 打开编辑器
goview.openEditor({ projectId: 123 })

// 打开预览
goview.openPreview(123)
```

## 标准URL参数

| 参数名 | 类型 | 必需 | 说明 |
|--------|------|------|------|
| `external_token` | string | ✅ | JWT访问令牌 |
| `external_refresh_token` | string | ✅ | JWT刷新令牌 |
| `external_user` | string | ✅ | 用户名 |
| `external_expires_in` | string | ❌ | token过期时间戳（Unix时间戳） |
| `external_nickname` | string | ❌ | 用户显示名称 |
| `external_user_id` | string | ❌ | 用户ID |
| `external_system` | string | ❌ | 来源系统标识（用于日志） |
| `external_login_url` | string | ❌ | 登录页面URL（认证失效时跳转） |

## 路由说明

### 编辑器路由
- `/project/items` - 项目列表页面
- `/chart/home/<USER>

### 预览路由（无需认证）
- `/chart/preview/:id` - 预览指定项目

## 认证流程

```mermaid
sequenceDiagram
    participant User as 用户
    participant System as 外部系统
    participant GoView as GoView
    participant Auth as 认证服务

    User->>System: 点击"数据可视化"
    System->>GoView: 跳转带认证参数
    GoView->>GoView: 存储认证信息
    GoView->>GoView: 启动token刷新机制
    
    Note over GoView: 用户正常使用
    
    GoView->>Auth: 自动刷新token
    Auth-->>GoView: 返回新token
    
    Note over GoView: token失效时
    
    GoView->>System: 跳转到登录页面
    System->>User: 显示登录界面
```

## 环境配置

GoView需要配置以下环境变量：

```bash
# .env.development
VITE_TOKEN_REFRESH_URL=http://localhost:3000/api/user/v2/token/refresh
VITE_DEFAULT_LOGIN_URL=http://localhost:3000/lowcode/login

# .env.production  
VITE_TOKEN_REFRESH_URL=https://your-domain.com/api/user/v2/token/refresh
VITE_DEFAULT_LOGIN_URL=https://your-domain.com/login
```

## 集成示例

### 示例1：React应用集成

```jsx
import React from 'react'

function DataVisualizationButton() {
  const openGoView = () => {
    const userInfo = getCurrentUser() // 获取当前用户信息
    
    const goviewUrl = `http://goview.com/#/project/items?` + new URLSearchParams({
      external_token: userInfo.accessToken,
      external_refresh_token: userInfo.refreshToken,
      external_expires_in: userInfo.expiresAt.toString(),
      external_user: userInfo.username,
      external_nickname: userInfo.displayName,
      external_user_id: userInfo.id.toString(),
      external_system: 'my-react-app',
      external_login_url: 'http://my-app.com/login'
    }).toString()
    
    window.open(goviewUrl, '_blank')
  }
  
  return (
    <button onClick={openGoView}>
      打开数据可视化
    </button>
  )
}
```

### 示例2：Vue应用集成

```vue
<template>
  <button @click="openGoView">数据可视化</button>
</template>

<script>
export default {
  methods: {
    openGoView() {
      const userInfo = this.$store.getters.currentUser
      
      const params = new URLSearchParams({
        external_token: userInfo.token,
        external_refresh_token: userInfo.refreshToken,
        external_expires_in: userInfo.expiresIn.toString(),
        external_user: userInfo.username,
        external_system: 'my-vue-app',
        external_login_url: 'http://my-vue-app.com/login'
      })
      
      const url = `http://goview.com/#/project/items?${params}`
      window.open(url, '_blank')
    }
  }
}
</script>
```

## 安全性考虑

1. **URL长度限制**：确保参数不超过浏览器URL长度限制（通常2048字符）
2. **敏感信息**：token会出现在URL中，建议使用短期token
3. **HTTPS**：生产环境务必使用HTTPS
4. **来源验证**：可通过 `external_system` 参数验证请求来源

## 故障排除

### 常见问题

1. **跳转后显示登录页面**
   - 检查必需参数是否完整
   - 验证token是否有效
   - 确认认证服务是否正常

2. **token刷新失败**
   - 检查 `VITE_TOKEN_REFRESH_URL` 配置
   - 验证refresh token是否有效
   - 确认认证服务接口是否正常

3. **预览页面无法访问**
   - 预览页面需要登录状态，但数据API在白名单中
   - 检查项目ID是否正确

### 调试日志

GoView会输出详细的调试日志，关键日志包括：

```
🔧 认证配置加载: {TOKEN_REFRESH_URL: ..., DEFAULT_LOGIN_URL: ...}
🔍 GoView路由守卫检查外部认证: {query: ..., token: ..., ...}
✅ 检测到来自 xxx 的完整认证信息，设置登录状态...
🔄 Token刷新机制已启动
```

## 技术支持

如有问题，请检查：
1. 浏览器控制台日志
2. 网络请求状态
3. 认证服务响应
4. 环境配置是否正确
