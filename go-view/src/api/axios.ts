import axios, { AxiosResponse, AxiosRequestConfig, Axios, AxiosError, InternalAxiosRequestConfig } from 'axios'
import { RequestHttpHeaderEnum, ResultEnum, ModuleTypeEnum } from '@/enums/httpEnum'
import { PageEnum, ErrorPageNameMap } from '@/enums/pageEnum'
import { StorageEnum } from '@/enums/storageEnum'
import { axiosPre } from '@/settings/httpSetting'
import { SystemStoreEnum, SystemStoreUserInfoEnum } from '@/store/modules/systemStore/systemStore.d'
import { redirectErrorPage, getLocalStorage, routerTurnByName, isPreview } from '@/utils'
import { fetchAllowList } from './axios.config'
import includes from 'lodash/includes'
import { tokenRefreshManager } from '@/utils/tokenRefresh'
import { redirectToLogin } from '@/utils/authUtils'

export interface MyResponseType<T> {
  code: ResultEnum
  data: T
  message: string
}

export interface MyRequestInstance extends Axios {
  <T = any>(config: AxiosRequestConfig): Promise<MyResponseType<T>>
}

const axiosInstance = axios.create({
  baseURL: `${import.meta.env.PROD ? import.meta.env.VITE_PRO_PATH : ''}${axiosPre}`,
  timeout: ResultEnum.TIMEOUT
}) as unknown as MyRequestInstance

axiosInstance.interceptors.request.use(
  async (config: InternalAxiosRequestConfig) => {
    console.log('🔍 [DEBUG] Axios interceptor - Original config:', {
      url: config.url,
      baseURL: config.baseURL,
      method: config.method
    })

    // 如果 URL 是完整的 URL（包含 http:// 或 https://），则清空 baseURL
    if (config.url && (config.url.startsWith('http://') || config.url.startsWith('https://'))) {
      console.log('🔍 [DEBUG] Detected full URL, clearing baseURL')
      config.baseURL = ''
      // 强制清空，确保不会拼接任何前缀
      delete config.baseURL
    }

    // 白名单校验
    if (includes(fetchAllowList, config.url)) {
      console.log('🔍 [DEBUG] URL in whitelist, returning early')
      return config
    }

    // 获取 token
    const info = getLocalStorage(StorageEnum.GO_SYSTEM_STORE)
    // 重新登录
    if (!info) {
      console.log('🔍 [DEBUG] No user info, redirecting to login')
      // 跳转到登录页面
      redirectToLogin()
      return config
    }
    const userInfo = info[SystemStoreEnum.USER_INFO]
    const tokenName = userInfo[SystemStoreUserInfoEnum.TOKEN_NAME] || 'token'
    let tokenValue = userInfo[SystemStoreUserInfoEnum.USER_TOKEN] || ''

    // 检查token是否即将过期并刷新
    try {
      const newToken = await tokenRefreshManager.checkAndRefreshToken()
      if (newToken) {
        tokenValue = newToken
        console.log('🔄 Token已刷新，使用新token')
      }
    } catch (error) {
      console.error('Token检查失败:', error)
    }

    // 判断是否为外部API并设置相应的认证头
    if (config.url && (config.url.startsWith('http://') || config.url.startsWith('https://'))) {
      // 对于完整URL，判断是否为外部API
      try {
        const currentHost = window.location.host
        const urlObj = new URL(config.url)
        const isExternalAPI = urlObj.host !== currentHost

        if (isExternalAPI) {
          console.log('🔍 [DEBUG] Setting X-Token for external API')
          config.headers['X-Token'] = tokenValue
        } else {
          console.log('🔍 [DEBUG] Setting default token for internal API')
          config.headers[tokenName] = tokenValue
        }
      } catch (error) {
        console.log('🔍 [DEBUG] Error parsing URL, using default token')
        config.headers[tokenName] = tokenValue
      }
    } else {
      console.log('🔍 [DEBUG] Setting default token for relative URL')
      config.headers[tokenName] = tokenValue
    }

    console.log('🔍 [DEBUG] Axios interceptor - Final config:', {
      url: config.url,
      baseURL: config.baseURL,
      method: config.method,
      headers: config.headers
    })

    return config
  },
  (err: AxiosError) => {
    console.log('🔍 [DEBUG] Axios interceptor error:', err)
    Promise.reject(err)
  }
)

// 响应拦截器
axiosInstance.interceptors.response.use(
  (res: AxiosResponse) => {
    // 预览页面错误不进行处理
    if (isPreview()) {
      return Promise.resolve(res.data)
    }
    const { code } = res.data as { code: number }

    if (code === undefined || code === null) return Promise.resolve(res.data)

    // 成功
    if (code === ResultEnum.SUCCESS) {
      return Promise.resolve(res.data)
    }

    // 登录过期 - 支持多种错误码
    if (code === ResultEnum.TOKEN_OVERDUE || code === 401) {
      window['$message'].error(window['$t']('http.token_overdue_message'))
      // 跳转到登录页面
      redirectToLogin()
      return Promise.resolve(res.data)
    }

    // 固定错误码重定向
    if (ErrorPageNameMap.get(code)) {
      redirectErrorPage(code)
      return Promise.resolve(res.data)
    }

    // 提示错误
    window['$message'].error(window['$t']((res.data as any).msg))
    return Promise.resolve(res.data)
  },
  async (err: AxiosError) => {
    const status = err.response?.status
    switch (status) {
      case 401:
        console.log('🔄 收到401响应，尝试刷新token...')
        try {
          // 尝试刷新token
          const newToken = await tokenRefreshManager.refreshToken()

          // 重试原始请求
          if (err.config && err.config.headers) {
            const tokenName = err.config.headers['X-Token'] ? 'X-Token' : 'token'
            err.config.headers[tokenName] = newToken
            console.log('🔄 使用新token重试请求')
            return axiosInstance.request(err.config)
          }
        } catch (refreshError) {
          console.error('401响应后token刷新失败:', refreshError)
          // 刷新失败，跳转到登录页面
          redirectToLogin()
        }
        return Promise.reject(err)

      default:
        return Promise.reject(err)
    }
  }
)

export default axiosInstance
