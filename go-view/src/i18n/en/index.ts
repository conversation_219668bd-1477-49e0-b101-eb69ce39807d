import project from './project'

const global = {
  doc_addr: 'Document',
  code_addr: 'Code',
  form_account: 'Please enter your account or email',
  form_password: 'Please enter your password',
  // header
  doc: 'Document',
  help: 'Help',
  contact: 'About Software',
  logout: 'Logout',
  logout_success: 'Logout success！',
  logout_failure: 'Logout Failed！',
  // system setting
  sys_set: 'System Setting',
  lang_set: 'Language Setting',
  // right key
  r_edit: 'Edit',
  r_preview: 'Preview',
  r_copy: 'Clone',
  r_rename: 'Rename',
  r_publish: 'Publish',
  r_unpublish: 'Unpublish',
  r_download: 'Download',
  r_delete: 'Delete',
  r_more: 'More',
}

const http = {
  error_message: 'The interface is abnormal, please check the interface!',
  token_overdue_message: 'Login expired, please log in again!',
  '请求数据格式错误': 'Request data format error',
  '用户名或密码不能为空': 'Username or password cannot be empty',
  '登录成功': 'Login successful',
  '登出成功': 'Logout successful',
  '获取成功': 'Get successful',
  '项目名称不能为空': 'Project name cannot be empty',
  '创建成功': 'Create successful',
  '项目ID不能为空': 'Project ID cannot be empty',
  '项目不存在': 'Project does not exist',
  '保存成功': 'Save successful',
  '修改成功': 'Modify successful',
  '删除成功': 'Delete successful',
  '发布状态修改成功': 'Publish status modified successfully',
  '获取上传文件失败': 'Failed to get upload file',
  '保存文件失败': 'Failed to save file',
  '上传成功': 'Upload successful'
}

export default {
  global,
  http,
  project
}
