import { echartOptionProfixHandle, PublicConfigClass } from '@/packages/public'
import { MapBubbleConfig } from './index'
import { chartInitConfig } from '@/settings/designSetting'
import { CreateComponentType } from '@/packages/index.d'
import cloneDeep from 'lodash/cloneDeep'
import dataJson from './data.json'

export const includes = []

export const option = {
  dataset: dataJson,
  mapRegion: {
    adcode: 'china',
    showHainanIsLands: true,
    enter: false,
    backSize: 20,
    backColor: '#ffffff'
  },
  tooltip: {
    show: true,
    trigger: 'item',
    backgroundColor: 'rgba(0,0,0,0.8)',
    borderColor: 'rgba(147, 235, 248, 0.8)',
    borderWidth: 1,
    textStyle: {
      color: '#FFFFFF',
      fontSize: 14
    }
  },
  // visualMap 配置已移除，气泡地图不需要视觉映射配置
  geo: {
    show: true,
    type: 'map',
    roam: true,
    map: 'china-bubble',  // 使用唯一的地图名称避免冲突
    selectedMode: false,
    zoom: 1,
    itemStyle: {
      borderColor: '#4DD0E1',  // 青色边界
      borderWidth: 1,
      areaColor: {
        type: 'linear',
        x: 0,
        y: 0,
        x2: 0,
        y2: 1,
        colorStops: [
          {
            offset: 0,
            color: '#1A1A2E'
          },
          {
            offset: 1,
            color: '#16213E'
          }
        ]
      },
      shadowColor: 'rgba(77, 208, 225, 0.3)',
      shadowOffsetX: 0,
      shadowOffsetY: 0,
      shadowBlur: 5
    },
    emphasis: {
      itemStyle: {
        areaColor: '#16213E',    // 悬停时稍微亮一点的深色
        shadowColor: 'rgba(77, 208, 225, 0.5)',
        borderWidth: 2,
        borderColor: '#4DD0E1'
      }
    }
  },
  bubbleConfig: {
    symbolSize: [20, 40],
    showEffect: true,
    effectScale: 6,
    effectColor: '#DE3D3DFF',  // 使用与主颜色一致的动画颜色
    bubbleColor: '#DE3D3DFF',
    borderColor: '#FFFFFF',
    borderWidth: 2,
    shadowBlur: 10,
    shadowColor: '#DE3D3DFF'   // 使用与主颜色一致的阴影颜色
  },
  labelConfig: {
    show: true,
    position: 'top',
    fontSize: 12,
    color: '#FFFFFF',
    fontWeight: 'bold',
    textBorderColor: '#000000',
    textBorderWidth: 1,
    textShadowColor: '#000000',
    textShadowBlur: 3,
    offset: [0, -10]
  },
  // 新增聚合策略配置
  aggregationStrategy: 'max', // 'max' | 'sum' | 'avg'
  series: [
    {
      name: '区域',
      type: 'map',
      map: 'china-bubble',  // 使用唯一的地图名称避免冲突
      data: [],
      selectedMode: false,
      zoom: 1,
      geoIndex: 1,
      tooltip: {
        show: true,
        backgroundColor: '#00000060',
        borderColor: 'rgba(147, 235, 248, 0.8)',
        textStyle: {
          color: '#FFFFFF',
          fontSize: 12
        }
      },
      label: {
        show: false,
        color: '#FFFFFF',
        fontSize: 12
      },
      emphasis: {
        disabled: false,
        label: {
          color: '#FFFFFF',
          fontSize: 12
        },
        itemStyle: {
          areaColor: '#16213E',    // 悬停时稍微亮一点的深色
          shadowColor: 'rgba(77, 208, 225, 0.5)',
          borderWidth: 2,
          borderColor: '#4DD0E1'
        }
      },
      itemStyle: {
        borderColor: '#4DD0E1',  // 青色边界
        borderWidth: 1,
        areaColor: {
          type: 'linear',
          x: 0,
          y: 0,
          x2: 0,
          y2: 1,
          colorStops: [
            {
              offset: 0,
              color: '#1A1A2E'
            },
            {
              offset: 1,
              color: '#16213E'
            }
          ]
        },
        shadowColor: 'rgba(77, 208, 225, 0.3)',
        shadowOffsetX: 0,
        shadowOffsetY: 0,
        shadowBlur: 5
      }
    },
    {
      name: '气泡标注',
      type: 'effectScatter',
      coordinateSystem: 'geo',
      symbolSize: function(val: any, params: any) {
        const config = params.option.bubbleConfig || { symbolSize: [20, 40] }
        const minSize = config.symbolSize[0]
        const maxSize = config.symbolSize[1]
        return Math.max(minSize, Math.min(maxSize, val[2] / 50))
      },
      showEffectOn: 'render',
      rippleEffect: {
        scale: 6,
        color: '#DE3D3DFF',  // 使用与主颜色一致的动画颜色
        brushType: 'fill'
      },
      tooltip: {
        show: true,
        formatter: function(params: any) {
          return params.data.label || `${params.data.name}: ${params.data.value[2]}`
        }
      },
      label: {
        show: true,
        formatter: function(params: any) {
          return params.data.label || params.data.name
        },
        fontSize: 12,
        color: '#FFFFFF',
        fontWeight: 'bold',
        position: 'top',
        offset: [0, -10],
        textBorderColor: '#000000',
        textBorderWidth: 1,
        textShadowColor: '#000000',
        textShadowBlur: 3
      },
      // itemStyle 将由动态配置设置，这里不预设固定值
      data: [],
      encode: {
        value: 2
      }
    }
  ]
}

export const MapBubbleDefaultConfig = { ...option }

export default class Config extends PublicConfigClass implements CreateComponentType {
  public key: string = MapBubbleConfig.key
  public attr = { ...chartInitConfig, w: 750, h: 800, zIndex: -1 }
  public chartConfig = cloneDeep(MapBubbleConfig)
  public option = echartOptionProfixHandle(option, includes)
}
