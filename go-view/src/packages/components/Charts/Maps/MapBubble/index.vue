<template>
  <div>
    <div class="back-icon" v-if="(enter && levelHistory.length !== 0) || (enter && !isPreview())" @click="backLevel">
      <n-icon :color="backColor" :size="backSize * 1.1">
        <ArrowBackIcon />
      </n-icon>
      <span
        :style="{
          'font-weight': 200,
          color: backColor,
          'font-size': `${backSize}px`
        }"
      >
        返回上级
      </span>
    </div>
    <v-chart
      ref="vChartRef"
      :init-options="initOptions"
      :theme="themeColor"
      :option="option.value"
      :manual-update="isPreview()"
      autoresize
      @click="chartPEvents"
    >
    </v-chart>
  </div>
</template>

<script setup lang="ts">
import { PropType, reactive, watch, ref, nextTick, toRefs, onBeforeUnmount } from 'vue'
import config, { includes } from './config'
import VChart from 'vue-echarts'
import { icon } from '@/plugins'
import { useCanvasInitOptions } from '@/hooks/useCanvasInitOptions.hook'
import { use, registerMap } from 'echarts/core'
import { EffectScatterChart, MapChart } from 'echarts/charts'
import { CanvasRenderer } from 'echarts/renderers'
import { useChartDataFetch } from '@/hooks'
import { mergeTheme, setOption } from '@/packages/public/chart'
import { useChartEditStore } from '@/store/modules/chartEditStore/chartEditStore'
import { isPreview } from '@/utils'
import mapJsonWithoutHainanIsLands from '../MapBase/mapWithoutHainanIsLands.json'
import mapChinaJson from '../MapBase/mapGeojson/china.json'
import { DatasetComponent, GridComponent, TooltipComponent, GeoComponent } from 'echarts/components'

const props = defineProps({
  themeSetting: {
    type: Object,
    required: true
  },
  themeColor: {
    type: Object,
    required: true
  },
  chartConfig: {
    type: Object as PropType<config>,
    required: true
  }
})

const { ArrowBackIcon } = icon.ionicons5
let levelHistory: any = ref([])

const { backColor, backSize, enter } = toRefs(props.chartConfig.option.mapRegion)
const initOptions = useCanvasInitOptions(props.chartConfig.option, props.themeSetting)

use([
  MapChart,
  DatasetComponent,
  CanvasRenderer,
  GridComponent,
  TooltipComponent,
  GeoComponent,
  EffectScatterChart
])

// 确保 MapBubble 组件不包含 visualMap 配置
if (props.chartConfig.option.visualMap) {
  console.log('🔍 [MapBubble] 检测到 visualMap 配置，正在移除...')
  delete props.chartConfig.option.visualMap
  console.log('🔍 [MapBubble] visualMap 配置已移除')
}

const option = reactive({
  value: mergeTheme(props.chartConfig.option, props.themeSetting, includes)
})
const vChartRef = ref<typeof VChart>()

// 城市到省份的映射关系表
const cityToProvinceMap: Record<string, string> = {
  // 直辖市
  '北京': '北京市',
  '上海': '上海市',
  '天津': '天津市',
  '重庆': '重庆市',

  // 广东省
  '广州': '广东省',
  '深圳': '广东省',
  '东莞': '广东省',
  '佛山': '广东省',
  '中山': '广东省',
  '珠海': '广东省',
  '惠州': '广东省',
  '江门': '广东省',
  '肇庆': '广东省',
  '汕头': '广东省',
  '湛江': '广东省',
  '茂名': '广东省',
  '韶关': '广东省',
  '梅州': '广东省',
  '河源': '广东省',
  '清远': '广东省',
  '阳江': '广东省',
  '潮州': '广东省',
  '揭阳': '广东省',
  '云浮': '广东省',
  '汕尾': '广东省',

  // 浙江省
  '杭州': '浙江省',
  '宁波': '浙江省',
  '温州': '浙江省',
  '嘉兴': '浙江省',
  '湖州': '浙江省',
  '绍兴': '浙江省',
  '金华': '浙江省',
  '衢州': '浙江省',
  '舟山': '浙江省',
  '台州': '浙江省',
  '丽水': '浙江省',

  // 江苏省
  '南京': '江苏省',
  '苏州': '江苏省',
  '无锡': '江苏省',
  '常州': '江苏省',
  '镇江': '江苏省',
  '南通': '江苏省',
  '泰州': '江苏省',
  '扬州': '江苏省',
  '盐城': '江苏省',
  '连云港': '江苏省',
  '徐州': '江苏省',
  '淮安': '江苏省',
  '宿迁': '江苏省',

  // 山东省
  '济南': '山东省',
  '青岛': '山东省',
  '淄博': '山东省',
  '枣庄': '山东省',
  '东营': '山东省',
  '烟台': '山东省',
  '潍坊': '山东省',
  '济宁': '山东省',
  '泰安': '山东省',
  '威海': '山东省',
  '日照': '山东省',
  '临沂': '山东省',
  '德州': '山东省',
  '聊城': '山东省',
  '滨州': '山东省',
  '菏泽': '山东省',

  // 四川省
  '成都': '四川省',
  '自贡': '四川省',
  '攀枝花': '四川省',
  '泸州': '四川省',
  '德阳': '四川省',
  '绵阳': '四川省',
  '广元': '四川省',
  '遂宁': '四川省',
  '内江': '四川省',
  '乐山': '四川省',
  '南充': '四川省',
  '眉山': '四川省',
  '宜宾': '四川省',
  '广安': '四川省',
  '达州': '四川省',
  '雅安': '四川省',
  '巴中': '四川省',
  '资阳': '四川省',

  // 河南省
  '郑州': '河南省',
  '开封': '河南省',
  '洛阳': '河南省',
  '平顶山': '河南省',
  '安阳': '河南省',
  '鹤壁': '河南省',
  '新乡': '河南省',
  '焦作': '河南省',
  '濮阳': '河南省',
  '许昌': '河南省',
  '漯河': '河南省',
  '三门峡': '河南省',
  '南阳': '河南省',
  '商丘': '河南省',
  '信阳': '河南省',
  '周口': '河南省',
  '驻马店': '河南省',

  // 湖北省
  '武汉': '湖北省',
  '黄石': '湖北省',
  '十堰': '湖北省',
  '宜昌': '湖北省',
  '襄阳': '湖北省',
  '鄂州': '湖北省',
  '荆门': '湖北省',
  '孝感': '湖北省',
  '荆州': '湖北省',
  '黄冈': '湖北省',
  '咸宁': '湖北省',
  '随州': '湖北省',

  // 湖南省
  '长沙': '湖南省',
  '株洲': '湖南省',
  '湘潭': '湖南省',
  '衡阳': '湖南省',
  '邵阳': '湖南省',
  '岳阳': '湖南省',
  '常德': '湖南省',
  '张家界': '湖南省',
  '益阳': '湖南省',
  '郴州': '湖南省',
  '永州': '湖南省',
  '怀化': '湖南省',
  '娄底': '湖南省',

  // 其他省份主要城市
  '西安': '陕西省',
  '合肥': '安徽省',
  '福州': '福建省',
  '厦门': '福建省',
  '南昌': '江西省',
  '石家庄': '河北省',
  '太原': '山西省',
  '沈阳': '辽宁省',
  '大连': '辽宁省',
  '长春': '吉林省',
  '哈尔滨': '黑龙江省',
  '昆明': '云南省',
  '贵阳': '贵州省',
  '南宁': '广西壮族自治区',
  '海口': '海南省',
  '三亚': '海南省',
  '拉萨': '西藏自治区',
  '兰州': '甘肃省',
  '西宁': '青海省',
  '银川': '宁夏回族自治区',
  '乌鲁木齐': '新疆维吾尔自治区',
  '呼和浩特': '内蒙古自治区'
}

// 将城市数据转换为省份数据的函数
const convertBubblesToRegions = (bubbles: any[], strategy: 'max' | 'avg' | 'sum' = 'max') => {
  if (!Array.isArray(bubbles) || bubbles.length === 0) {
    console.warn('🔍 [MapBubble] convertBubblesToRegions: 无效的气泡数据')
    return []
  }

  const provinceMap = new Map<string, { values: number[], count: number }>()

  console.log('🔍 [MapBubble] 开始转换气泡数据，策略:', strategy)

  bubbles.forEach((bubble, index) => {
    if (!bubble || !bubble.name || !bubble.value || !Array.isArray(bubble.value) || bubble.value.length < 3) {
      console.warn(`🔍 [MapBubble] 跳过无效数据项 ${index}:`, bubble)
      return
    }

    const cityName = bubble.name
    const provinceName = cityToProvinceMap[cityName] || `${cityName}市`
    const value = bubble.value[2] // 使用第三个数值

    if (typeof value !== 'number' || isNaN(value)) {
      console.warn(`🔍 [MapBubble] 跳过无效数值 ${cityName}:`, value)
      return
    }

    if (!provinceMap.has(provinceName)) {
      provinceMap.set(provinceName, { values: [], count: 0 })
    }

    const provinceData = provinceMap.get(provinceName)!
    provinceData.values.push(value)
    provinceData.count++

    console.log(`🔍 [MapBubble] 映射: ${cityName} -> ${provinceName}, 数值: ${value}`)
  })

  // 根据策略计算省份数值
  const result = Array.from(provinceMap.entries()).map(([name, data]) => {
    let finalValue: number

    switch (strategy) {
      case 'sum':
        finalValue = data.values.reduce((sum, val) => sum + val, 0)
        break
      case 'avg':
        finalValue = Math.round(data.values.reduce((sum, val) => sum + val, 0) / data.count)
        break
      case 'max':
      default:
        finalValue = Math.max(...data.values)
        break
    }

    console.log(`🔍 [MapBubble] 省份聚合: ${name}, 策略: ${strategy}, 原始值: [${data.values.join(', ')}], 最终值: ${finalValue}`)

    return {
      name,
      value: finalValue
    }
  })

  console.log('🔍 [MapBubble] 转换完成，生成区域数据:', result)
  return result
}

//动态获取json注册地图 - 使用唯一的地图名称避免与其他组件冲突
const getGeojson = (regionId: string) => {
  return new Promise<boolean>(resolve => {
    import(`../MapBase/mapGeojson/${regionId}.json`).then(data => {
      const uniqueMapName = `${regionId}-bubble`
      registerMap(uniqueMapName, { geoJSON: data.default as any, specialAreas: {} })
      console.log('🔍 [MapBubble] 注册地图:', uniqueMapName)
      resolve(true)
    })
  })
}

//异步时先注册空的 保证初始化不报错 - 使用唯一的地图名称
const uniqueMapName = `${props.chartConfig.option.mapRegion.adcode}-bubble`
registerMap(uniqueMapName, { geoJSON: {} as any, specialAreas: {} })
console.log('🔍 [MapBubble] 预注册地图:', uniqueMapName)

// 进行更换初始化地图 如果为china 单独处理
const registerMapInitAsync = async () => {
  try {
    console.log('🔍 [MapBubble] 开始初始化地图')
    console.log('🔍 [MapBubble] 初始配置:', props.chartConfig.option)

    await nextTick()
    const adCode = `${props.chartConfig.option.mapRegion.adcode}`
    console.log('🔍 [MapBubble] 地图区域代码:', adCode)

    if (adCode !== 'china') {
      await getGeojson(adCode)
    } else {
      await hainanLandsHandle(props.chartConfig.option.mapRegion.showHainanIsLands)
    }
    console.log('🔍 [MapBubble] 地图数据加载完成')

    // 处理初始数据和配置
    const initialData = props.chartConfig.option.dataset
    console.log('🔍 [MapBubble] 初始数据:', initialData)
    await dataSetHandle(initialData)
    console.log('🔍 [MapBubble] 地图初始化完成')
  } catch (error) {
    console.warn('🔍 [MapBubble] 初始化错误:', error)
    // 即使出错也要尝试渲染基本配置
    vEchartsSetOption()
  }
}
registerMapInitAsync()

// 手动触发渲染
const vEchartsSetOption = () => {
  try {
    // 保存用户配置的 areaColor，防止被主题覆盖
    const userAreaColor = props.chartConfig.option?.geo?.itemStyle?.areaColor

    // 使用 mergeTheme 处理配置
    const finalOption = mergeTheme(props.chartConfig.option, props.themeSetting, includes)

    // 强制确保用户的 areaColor 不被覆盖
    if (userAreaColor) {
      // 修复 geo.itemStyle.areaColor
      if (finalOption.geo?.itemStyle) {
        finalOption.geo.itemStyle.areaColor = userAreaColor
      }

      // 🔧 关键修复：修复 series[0].itemStyle.areaColor（这个才是真正控制地图颜色的）
      if (finalOption.series && finalOption.series[0] && finalOption.series[0].type === 'map') {
        if (!finalOption.series[0].itemStyle) {
          finalOption.series[0].itemStyle = {}
        }
        finalOption.series[0].itemStyle.areaColor = userAreaColor
      }
    }

    option.value = finalOption
    setOption(vChartRef.value, finalOption)
  } catch (error) {
    console.error('vEchartsSetOption error:', error)
  }
}

// 更新数据处理
const dataSetHandle = async (dataset: any) => {
  try {
    console.log('🔍 [MapBubble] dataSetHandle 开始执行')
    console.log('🔍 [MapBubble] dataset:', dataset)
    console.log('🔍 [MapBubble] chartConfig.option:', props.chartConfig?.option)

    if (!props.chartConfig?.option?.series) {
      console.warn('🔍 [MapBubble] series配置不存在')
      return
    }

    console.log('🔍 [MapBubble] series数量:', props.chartConfig.option.series.length)

    props.chartConfig.option.series.forEach((item: any, index: number) => {
      console.log(`🔍 [MapBubble] 处理series[${index}]:`, item.type, item.name)

      if (item.type === 'effectScatter') {
        console.log('🔍 [MapBubble] 找到effectScatter类型的series')

        // 应用气泡配置
        const bubbleConfig = props.chartConfig.option.bubbleConfig

        // 设置数据 - 为每个数据项设置itemStyle确保颜色生效
        if (dataset && Array.isArray(dataset) && dataset.length > 0) {
          const mainColor = bubbleConfig?.bubbleColor || '#DE3D3DFF'
          item.data = dataset.map((dataItem: any) => ({
            name: dataItem.name || '',
            value: dataItem.value || [0, 0, 0],
            label: dataItem.label || dataItem.name || '',
            itemStyle: {
              color: mainColor,  // 在数据项级别设置颜色
              borderColor: bubbleConfig?.borderColor || '#FFFFFF',
              borderWidth: bubbleConfig?.borderWidth || 2,
              shadowColor: mainColor,
              shadowBlur: bubbleConfig?.shadowBlur || 10
            }
          }))
          console.log('🔍 [MapBubble] 设置数据完成，数据量:', item.data.length)
          console.log('🔍 [MapBubble] 数据项颜色:', mainColor)
        }
        console.log('🔍 [MapBubble] bubbleConfig:', bubbleConfig)

        if (bubbleConfig) {
          console.log('🔍 [MapBubble] 应用气泡配置...')

          item.symbolSize = function(val: any) {
            if (!val || !Array.isArray(val) || val.length < 3) return bubbleConfig?.symbolSize?.[0] || 20
            const minSize = bubbleConfig?.symbolSize?.[0] || 20
            const maxSize = bubbleConfig?.symbolSize?.[1] || 40
            return Math.max(minSize, Math.min(maxSize, val[2] / 50))
          }

          // 记录原始itemStyle
          console.log('🔍 [MapBubble] 原始itemStyle:', JSON.stringify(item.itemStyle))

          // 应用样式配置 - 确保所有颜色都使用主颜色
          const mainColor = bubbleConfig?.bubbleColor || '#DE3D3DFF'
          console.log('🔍 [MapBubble] 主颜色:', mainColor)

          const newItemStyle = {
            color: mainColor,
            borderColor: bubbleConfig?.borderColor || '#FFFFFF',
            borderWidth: bubbleConfig?.borderWidth || 2,
            shadowColor: mainColor, // 强制使用主颜色作为阴影
            shadowBlur: bubbleConfig?.shadowBlur || 10
          }

          console.log('🔍 [MapBubble] 新的itemStyle:', JSON.stringify(newItemStyle))
          item.itemStyle = newItemStyle
          console.log('🔍 [MapBubble] 设置后的itemStyle:', JSON.stringify(item.itemStyle))

          // 强制设置颜色属性，确保生效
          item.color = mainColor

          // 尝试多种颜色设置方式确保生效
          if (!item.emphasis) item.emphasis = {}
          if (!item.emphasis.itemStyle) item.emphasis.itemStyle = {}
          item.emphasis.itemStyle.color = mainColor

          console.log('🔍 [MapBubble] 强制设置series.color:', mainColor)
          console.log('🔍 [MapBubble] 强制设置emphasis.itemStyle.color:', mainColor)

          // 应用动画效果 - 动画颜色也使用主颜色
          if (bubbleConfig?.showEffect) {
            item.rippleEffect = {
              scale: bubbleConfig?.effectScale || 6,
              color: mainColor, // 强制使用主颜色作为动画效果颜色
              brushType: 'fill'
            }
            console.log('🔍 [MapBubble] 设置动画效果:', JSON.stringify(item.rippleEffect))
          }
        } else {
          console.warn('🔍 [MapBubble] bubbleConfig不存在，使用默认配置')
        }

        // 应用标签配置
        const labelConfig = props.chartConfig.option.labelConfig
        console.log('🔍 [MapBubble] labelConfig:', labelConfig)

        if (labelConfig) {
          item.label = {
            show: labelConfig.show !== false,
            formatter: function(params: any) {
              return params?.data?.label || params?.data?.name || ''
            },
            fontSize: labelConfig.fontSize || 12,
            color: labelConfig.color || '#FFFFFF',
            fontWeight: labelConfig.fontWeight || 'bold',
            position: labelConfig.position || 'top',
            offset: labelConfig.offset || [0, -10],
            textBorderColor: labelConfig.textBorderColor || '#000000',
            textBorderWidth: labelConfig.textBorderWidth || 1,
            textShadowColor: labelConfig.textShadowColor || '#000000',
            textShadowBlur: labelConfig.textShadowBlur || 3
          }
          console.log('🔍 [MapBubble] 设置标签配置完成')
        }

        console.log('🔍 [MapBubble] 最终的series配置:', JSON.stringify(item, null, 2))
      }
    })

    console.log('🔍 [MapBubble] 准备调用vEchartsSetOption')
    // 重新渲染
    vEchartsSetOption()
    console.log('🔍 [MapBubble] vEchartsSetOption调用完成')
  } catch (error) {
    console.error('🔍 [MapBubble] dataSetHandle error:', error)
  }
}

// 处理海南群岛 - 使用唯一的地图名称
const hainanLandsHandle = async (newData: boolean) => {
  const uniqueMapName = 'china-bubble'
  if (newData) {
    await getGeojson('china')
  } else {
    registerMap(uniqueMapName, { geoJSON: mapJsonWithoutHainanIsLands as any, specialAreas: {} })
    console.log('🔍 [MapBubble] 注册海南群岛地图:', uniqueMapName)
  }
}

// 点击区域
const chartPEvents = (e: any) => {
  if (e.seriesType !== 'map') return
  if (!props.chartConfig.option.mapRegion.enter) {
    return
  }
  mapChinaJson.features.forEach(item => {
    var pattern = new RegExp(e.name)
    if (pattern.test(item.properties.name)) {
      let code = String(item.properties.adcode)
      levelHistory.value.push(code)
      checkOrMap(code)
    }
  })
}

// 返回上一级
const backLevel = () => {
  levelHistory.value = []
  if (levelHistory.value.length > 1) {
    levelHistory.value.pop()
    const code = levelHistory[levelHistory.value.length - 1]
    checkOrMap(code)
  } else {
    checkOrMap('china')
  }
}

// 切换地图 - 使用唯一的地图名称
const checkOrMap = async (newData: string) => {
  const uniqueMapName = `${newData}-bubble`

  if (newData === 'china') {
    if (props.chartConfig.option.mapRegion.showHainanIsLands) {
      hainanLandsHandle(true)
      vEchartsSetOption()
    } else {
      hainanLandsHandle(false)
      vEchartsSetOption()
    }
  } else {
    await getGeojson(newData)
  }

  // 使用唯一的地图名称
  props.chartConfig.option.geo.map = uniqueMapName
  props.chartConfig.option.series.forEach((item: any) => {
    if (item.type === 'map') {
      item.map = uniqueMapName
    }
    if (item.type === 'effectScatter') item.coordinateSystem = 'geo'
  })
  console.log('🔍 [MapBubble] 切换到地图:', uniqueMapName)
  vEchartsSetOption()
}

watch(
  () => props.chartConfig.option.mapRegion.adcode,
  (newData: string) => {
    checkOrMap(newData)
  }
)

//监听是否显示南海群岛
if (!isPreview()) {
  watch(
    () => props.chartConfig.option.mapRegion.showHainanIsLands,
    async newData => {
      try {
        console.log('🔍 [MapBubble] 南海诸岛开关变化:', newData)
        await hainanLandsHandle(newData)
        vEchartsSetOption()
        console.log('🔍 [MapBubble] 南海诸岛开关处理完成')
      } catch (error) {
        console.error('🔍 [MapBubble] 南海诸岛开关处理错误:', error)
      }
    },
    {
      deep: false
    }
  )
}

watch(
  () => props.chartConfig.option.mapRegion.showHainanIsLands,
  (newData: boolean) => {
    if (props.chartConfig.option.mapRegion.adcode === 'china') {
      hainanLandsHandle(newData)
      vEchartsSetOption()
    }
  }
)

// 预览
useChartDataFetch(props.chartConfig, useChartEditStore, (newData: any) => {
  dataSetHandle(newData)
})

// 监听数据变化
watch(
  () => props.chartConfig?.option?.dataset,
  (newData: any) => {
    try {
      dataSetHandle(newData)
    } catch (error) {
      console.error('MapBubble dataset watch error:', error)
    }
  },
  { deep: true, immediate: true }
)

// 监听气泡配置变化
watch(
  () => props.chartConfig?.option?.bubbleConfig,
  (newConfig, oldConfig) => {
    try {
      console.log('🔍 [MapBubble] bubbleConfig变化')
      console.log('🔍 [MapBubble] 旧配置:', oldConfig)
      console.log('🔍 [MapBubble] 新配置:', newConfig)
      const currentData = props.chartConfig?.option?.dataset
      dataSetHandle(currentData)
    } catch (error) {
      console.error('🔍 [MapBubble] bubbleConfig watch error:', error)
    }
  },
  { deep: true }
)

// 监听标签配置变化
watch(
  () => props.chartConfig?.option?.labelConfig,
  () => {
    try {
      const currentData = props.chartConfig?.option?.dataset
      dataSetHandle(currentData)
    } catch (error) {
      console.error('MapBubble labelConfig watch error:', error)
    }
  },
  { deep: true }
)

// 监听整个option配置变化，但排除dataset避免无限循环
watch(
  () => ({
    bubbleConfig: props.chartConfig?.option?.bubbleConfig,
    labelConfig: props.chartConfig?.option?.labelConfig,
    mapRegion: props.chartConfig?.option?.mapRegion,
    geo: props.chartConfig?.option?.geo
  }),
  () => {
    try {
      // 延迟一点执行，确保配置已经更新
      nextTick(() => {
        const currentData = props.chartConfig?.option?.dataset
        dataSetHandle(currentData)
      })
    } catch (error) {
      console.error('MapBubble config watch error:', error)
    }
  },
  { deep: true }
)

// 组件销毁时清理
onBeforeUnmount(() => {
  try {
    if (vChartRef.value) {
      // 清理ECharts实例
      const chartInstance = vChartRef.value
      if (chartInstance && typeof chartInstance.dispose === 'function') {
        chartInstance.dispose()
      }
    }
  } catch (error) {
    console.warn('MapBubble cleanup error:', error)
  }
})
</script>

<style lang="scss" scoped>
.back-icon {
  position: absolute;
  top: 10px;
  left: 10px;
  z-index: 999;
  display: flex;
  align-items: center;
  cursor: pointer;
  user-select: none;
  
  span {
    margin-left: 5px;
  }
}
</style>
