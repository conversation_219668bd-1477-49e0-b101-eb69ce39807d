import { echartOptionProfixHandle, PublicConfigClass } from '@/packages/public'
import { RankingListConfig } from './index'
import { chartInitConfig } from '@/settings/designSetting'
import { CreateComponentType } from '@/packages/index.d'
import cloneDeep from 'lodash/cloneDeep'
import dataJson from './data.json'

export const includes = []

export const option = {
  dataset: dataJson,
  // 排行榜样式配置
  rankingStyle: {
    // 容器样式
    containerStyle: {
      backgroundColor: 'rgba(0, 0, 0, 0.1)',
      borderRadius: 8,
      padding: 16,
      border: '1px solid rgba(255, 255, 255, 0.1)'
    },
    // 标题样式
    titleStyle: {
      show: true,
      text: '排行榜',
      fontSize: 18,
      fontWeight: 'bold',
      color: '#ffffff',
      textAlign: 'center',
      marginBottom: 20
    },
    // 表头样式
    headerStyle: {
      show: true,
      backgroundColor: 'rgba(255, 255, 255, 0.1)',
      color: '#ffffff',
      fontSize: 14,
      fontWeight: 'bold',
      height: 40,
      borderRadius: 4
    },
    // 行样式
    rowStyle: {
      height: 60,
      backgroundColor: 'rgba(255, 255, 255, 0.05)',
      hoverBackgroundColor: 'rgba(255, 255, 255, 0.1)',
      borderRadius: 4,
      marginBottom: 8,
      padding: 12
    },
    // 排名样式
    rankStyle: {
      width: 60,
      fontSize: 16,
      fontWeight: 'bold',
      color: '#ffffff',
      // 前三名特殊颜色
      topColors: {
        1: '#FFD700', // 金色
        2: '#C0C0C0', // 银色
        3: '#CD7F32'  // 铜色
      }
    },
    // 头像样式
    avatarStyle: {
      show: true,
      width: 40,
      height: 40,
      borderRadius: '50%',
      border: '2px solid rgba(255, 255, 255, 0.2)'
    },
    // 名称样式
    nameStyle: {
      fontSize: 16,
      fontWeight: 'bold',
      color: '#ffffff',
      marginLeft: 12
    },
    // 描述样式
    descStyle: {
      fontSize: 12,
      color: 'rgba(255, 255, 255, 0.7)',
      marginLeft: 12,
      marginTop: 4
    },
    // 数值样式
    valueStyle: {
      fontSize: 16,
      fontWeight: 'bold',
      color: '#00D4FF',
      textAlign: 'right'
    }
  },
  // 动画配置
  animation: {
    enable: true,
    duration: 1000,
    delay: 100,
    easing: 'cubicOut'
  },
  // 数据配置
  dataConfig: {
    maxItems: 10, // 最大显示条数
    sortField: '产品数量', // 排序字段
    sortOrder: 'desc' // 排序方式: asc, desc
  }
}

export default class Config extends PublicConfigClass implements CreateComponentType {
  public key: string = RankingListConfig.key
  public attr = { ...chartInitConfig, w: 500, h: 600, zIndex: -1 }
  public chartConfig = cloneDeep(RankingListConfig)
  public option = echartOptionProfixHandle(option, includes)
}
