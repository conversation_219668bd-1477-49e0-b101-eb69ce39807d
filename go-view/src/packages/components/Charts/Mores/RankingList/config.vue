<template>
  <div>
    <!-- 全局设置 -->
    <global-setting :optionData="optionData"></global-setting>
    
    <!-- 标题配置 -->
    <CollapseItem name="标题设置" :expanded="true">
      <SettingItemBox name="标题样式">
        <SettingItem name="显示标题">
          <n-switch v-model:value="titleStyle.show" size="small" />
        </SettingItem>
        <SettingItem name="标题文字">
          <n-input v-model:value="titleStyle.text" size="small" placeholder="请输入标题" />
        </SettingItem>
        <SettingItem name="字体大小">
          <n-input-number v-model:value="titleStyle.fontSize" size="small" :min="12" :max="32" />
        </SettingItem>
        <SettingItem name="字体颜色">
          <n-color-picker v-model:value="titleStyle.color" size="small" />
        </SettingItem>
        <SettingItem name="字体粗细">
          <n-select
            v-model:value="titleStyle.fontWeight"
            size="small"
            :options="fontWeightOptions"
          />
        </SettingItem>
      </SettingItemBox>
    </CollapseItem>

    <!-- 容器配置 -->
    <CollapseItem name="容器设置" :expanded="true">
      <SettingItemBox name="容器样式">
        <SettingItem name="背景颜色">
          <n-color-picker v-model:value="containerStyle.backgroundColor" size="small" />
        </SettingItem>
        <SettingItem name="圆角大小">
          <n-input-number v-model:value="containerStyle.borderRadius" size="small" :min="0" :max="20" />
        </SettingItem>
        <SettingItem name="内边距">
          <n-input-number v-model:value="containerStyle.padding" size="small" :min="0" :max="50" />
        </SettingItem>
      </SettingItemBox>
    </CollapseItem>

    <!-- 表头配置 -->
    <CollapseItem name="表头设置" :expanded="true">
      <SettingItemBox name="表头样式">
        <SettingItem name="显示表头">
          <n-switch v-model:value="headerStyle.show" size="small" />
        </SettingItem>
        <SettingItem name="背景颜色">
          <n-color-picker v-model:value="headerStyle.backgroundColor" size="small" />
        </SettingItem>
        <SettingItem name="文字颜色">
          <n-color-picker v-model:value="headerStyle.color" size="small" />
        </SettingItem>
        <SettingItem name="字体大小">
          <n-input-number v-model:value="headerStyle.fontSize" size="small" :min="10" :max="20" />
        </SettingItem>
        <SettingItem name="高度">
          <n-input-number v-model:value="headerStyle.height" size="small" :min="30" :max="60" />
        </SettingItem>
      </SettingItemBox>
    </CollapseItem>

    <!-- 行样式配置 -->
    <CollapseItem name="行样式设置" :expanded="true">
      <SettingItemBox name="行样式">
        <SettingItem name="行高">
          <n-input-number v-model:value="rowStyle.height" size="small" :min="40" :max="100" />
        </SettingItem>
        <SettingItem name="背景颜色">
          <n-color-picker v-model:value="rowStyle.backgroundColor" size="small" />
        </SettingItem>
        <SettingItem name="悬停颜色">
          <n-color-picker v-model:value="rowStyle.hoverBackgroundColor" size="small" />
        </SettingItem>
        <SettingItem name="圆角大小">
          <n-input-number v-model:value="rowStyle.borderRadius" size="small" :min="0" :max="15" />
        </SettingItem>
        <SettingItem name="行间距">
          <n-input-number v-model:value="rowStyle.marginBottom" size="small" :min="0" :max="20" />
        </SettingItem>
      </SettingItemBox>
    </CollapseItem>

    <!-- 排名样式配置 -->
    <CollapseItem name="排名设置" :expanded="true">
      <SettingItemBox name="排名样式">
        <SettingItem name="排名宽度">
          <n-input-number v-model:value="rankStyle.width" size="small" :min="40" :max="100" />
        </SettingItem>
        <SettingItem name="字体大小">
          <n-input-number v-model:value="rankStyle.fontSize" size="small" :min="12" :max="24" />
        </SettingItem>
        <SettingItem name="默认颜色">
          <n-color-picker v-model:value="rankStyle.color" size="small" />
        </SettingItem>
        <SettingItem name="第一名颜色">
          <n-color-picker v-model:value="rankStyle.topColors[1]" size="small" />
        </SettingItem>
        <SettingItem name="第二名颜色">
          <n-color-picker v-model:value="rankStyle.topColors[2]" size="small" />
        </SettingItem>
        <SettingItem name="第三名颜色">
          <n-color-picker v-model:value="rankStyle.topColors[3]" size="small" />
        </SettingItem>
      </SettingItemBox>
    </CollapseItem>

    <!-- 头像配置 -->
    <CollapseItem name="头像设置" :expanded="true">
      <SettingItemBox name="头像样式">
        <SettingItem name="显示头像">
          <n-switch v-model:value="avatarStyle.show" size="small" />
        </SettingItem>
        <SettingItem name="头像宽度">
          <n-input-number v-model:value="avatarStyle.width" size="small" :min="20" :max="60" />
        </SettingItem>
        <SettingItem name="头像高度">
          <n-input-number v-model:value="avatarStyle.height" size="small" :min="20" :max="60" />
        </SettingItem>
      </SettingItemBox>
    </CollapseItem>

    <!-- 文字样式配置 -->
    <CollapseItem name="文字设置" :expanded="true">
      <SettingItemBox name="名称样式">
        <SettingItem name="名称字体大小">
          <n-input-number v-model:value="nameStyle.fontSize" size="small" :min="10" :max="20" />
        </SettingItem>
        <SettingItem name="名称颜色">
          <n-color-picker v-model:value="nameStyle.color" size="small" />
        </SettingItem>
      </SettingItemBox>
      
      <SettingItemBox name="描述样式">
        <SettingItem name="描述字体大小">
          <n-input-number v-model:value="descStyle.fontSize" size="small" :min="8" :max="16" />
        </SettingItem>
        <SettingItem name="描述颜色">
          <n-color-picker v-model:value="descStyle.color" size="small" />
        </SettingItem>
      </SettingItemBox>
      
      <SettingItemBox name="数值样式">
        <SettingItem name="数值字体大小">
          <n-input-number v-model:value="valueStyle.fontSize" size="small" :min="10" :max="24" />
        </SettingItem>
        <SettingItem name="数值颜色">
          <n-color-picker v-model:value="valueStyle.color" size="small" />
        </SettingItem>
      </SettingItemBox>
    </CollapseItem>

    <!-- 数据配置 -->
    <CollapseItem name="数据设置" :expanded="true">
      <SettingItemBox name="数据配置">
        <SettingItem name="最大显示条数">
          <n-input-number v-model:value="dataConfig.maxItems" size="small" :min="3" :max="20" />
        </SettingItem>
      </SettingItemBox>
    </CollapseItem>
  </div>
</template>

<script setup lang="ts">
import { PropType, computed } from 'vue'
import { GlobalSetting, CollapseItem, SettingItemBox, SettingItem } from '@/components/Pages/ChartItemSetting'
import { GlobalThemeJsonType } from '@/settings/chartThemes/index'

const props = defineProps({
  optionData: {
    type: Object as PropType<GlobalThemeJsonType>,
    required: true
  }
})

// 配置项引用
const rankingStyle = computed(() => props.optionData.rankingStyle)
const containerStyle = computed(() => rankingStyle.value.containerStyle)
const titleStyle = computed(() => rankingStyle.value.titleStyle)
const headerStyle = computed(() => rankingStyle.value.headerStyle)
const rowStyle = computed(() => rankingStyle.value.rowStyle)
const rankStyle = computed(() => rankingStyle.value.rankStyle)
const avatarStyle = computed(() => rankingStyle.value.avatarStyle)
const nameStyle = computed(() => rankingStyle.value.nameStyle)
const descStyle = computed(() => rankingStyle.value.descStyle)
const valueStyle = computed(() => rankingStyle.value.valueStyle)
const dataConfig = computed(() => props.optionData.dataConfig)

// 字体粗细选项
const fontWeightOptions = [
  { label: '正常', value: 'normal' },
  { label: '粗体', value: 'bold' },
  { label: '更粗', value: 'bolder' },
  { label: '更细', value: 'lighter' }
]
</script>
