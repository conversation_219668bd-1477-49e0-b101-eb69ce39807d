<template>
  <div class="ranking-list-container" :style="containerStyles">
    <!-- 标题 -->
    <div v-if="titleConfig.show" class="ranking-title" :style="titleStyles">
      {{ titleConfig.text }}
    </div>
    
    <!-- 表头 -->
    <div v-if="headerConfig.show" class="ranking-header" :style="headerStyles">
      <div class="header-rank">排名</div>
      <div class="header-name">应用名称</div>
      <div class="header-desc">大数据</div>
      <div class="header-value">产品数量</div>
    </div>
    
    <!-- 排行榜列表 -->
    <div class="ranking-list">
      <div 
        v-for="(item, index) in displayData" 
        :key="index"
        class="ranking-item"
        :style="getRowStyles(index)"
        @mouseenter="onRowHover(index, true)"
        @mouseleave="onRowHover(index, false)"
      >
        <!-- 排名 -->
        <div class="rank-number" :style="getRankStyles(item.排名)">
          <span v-if="item.排名 <= 3" class="rank-medal">{{ getRankMedal(item.排名) }}</span>
          <span v-else>{{ item.排名 }}</span>
        </div>
        
        <!-- 应用信息 -->
        <div class="app-info">
          <img 
            v-if="avatarConfig.show && item.avatar" 
            :src="item.avatar" 
            :style="avatarStyles"
            class="app-avatar"
            @error="handleImageError"
          />
          <div class="app-details">
            <div class="app-name" :style="nameStyles">{{ item.应用名称 }}</div>
            <div class="app-desc" :style="descStyles">{{ item.大数据 }}</div>
          </div>
        </div>
        
        <!-- 数值 -->
        <div class="app-value" :style="valueStyles">
          {{ formatValue(item.产品数量) }}
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, PropType, watch, onMounted } from 'vue'
import { CreateComponentType } from '@/packages/index.d'
import { useChartDataFetch } from '@/hooks'

const props = defineProps({
  themeSetting: {
    type: Object,
    required: true
  },
  themeColor: {
    type: Object,
    required: true
  },
  chartConfig: {
    type: Object as PropType<CreateComponentType>,
    required: true
  }
})

const hoveredIndex = ref(-1)

// 配置项
const rankingStyle = computed(() => props.chartConfig.option.rankingStyle)
const containerConfig = computed(() => rankingStyle.value.containerStyle)
const titleConfig = computed(() => rankingStyle.value.titleStyle)
const headerConfig = computed(() => rankingStyle.value.headerStyle)
const rowConfig = computed(() => rankingStyle.value.rowStyle)
const rankConfig = computed(() => rankingStyle.value.rankStyle)
const avatarConfig = computed(() => rankingStyle.value.avatarStyle)
const nameConfig = computed(() => rankingStyle.value.nameStyle)
const descConfig = computed(() => rankingStyle.value.descStyle)
const valueConfig = computed(() => rankingStyle.value.valueStyle)
const dataConfig = computed(() => props.chartConfig.option.dataConfig)

// 数据处理
const { vChartRef } = useChartDataFetch(props.chartConfig, false)

const displayData = computed(() => {
  const data = props.chartConfig.option.dataset?.source || []
  const maxItems = dataConfig.value.maxItems || 10
  return data.slice(0, maxItems)
})

// 样式计算
const containerStyles = computed(() => ({
  backgroundColor: containerConfig.value.backgroundColor,
  borderRadius: `${containerConfig.value.borderRadius}px`,
  padding: `${containerConfig.value.padding}px`,
  border: containerConfig.value.border,
  height: '100%',
  overflow: 'hidden'
}))

const titleStyles = computed(() => ({
  fontSize: `${titleConfig.value.fontSize}px`,
  fontWeight: titleConfig.value.fontWeight,
  color: titleConfig.value.color,
  textAlign: titleConfig.value.textAlign,
  marginBottom: `${titleConfig.value.marginBottom}px`
}))

const headerStyles = computed(() => ({
  backgroundColor: headerConfig.value.backgroundColor,
  color: headerConfig.value.color,
  fontSize: `${headerConfig.value.fontSize}px`,
  fontWeight: headerConfig.value.fontWeight,
  height: `${headerConfig.value.height}px`,
  borderRadius: `${headerConfig.value.borderRadius}px`,
  display: 'flex',
  alignItems: 'center',
  padding: '0 12px',
  marginBottom: '12px'
}))

const getRowStyles = (index: number) => ({
  height: `${rowConfig.value.height}px`,
  backgroundColor: hoveredIndex.value === index 
    ? rowConfig.value.hoverBackgroundColor 
    : rowConfig.value.backgroundColor,
  borderRadius: `${rowConfig.value.borderRadius}px`,
  marginBottom: `${rowConfig.value.marginBottom}px`,
  padding: `${rowConfig.value.padding}px`,
  display: 'flex',
  alignItems: 'center',
  transition: 'all 0.3s ease',
  cursor: 'pointer'
})

const getRankStyles = (rank: number) => ({
  width: `${rankConfig.value.width}px`,
  fontSize: `${rankConfig.value.fontSize}px`,
  fontWeight: rankConfig.value.fontWeight,
  color: rankConfig.value.topColors[rank] || rankConfig.value.color,
  textAlign: 'center',
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center'
})

const avatarStyles = computed(() => ({
  width: `${avatarConfig.value.width}px`,
  height: `${avatarConfig.value.height}px`,
  borderRadius: avatarConfig.value.borderRadius,
  border: avatarConfig.value.border,
  objectFit: 'cover'
}))

const nameStyles = computed(() => ({
  fontSize: `${nameConfig.value.fontSize}px`,
  fontWeight: nameConfig.value.fontWeight,
  color: nameConfig.value.color,
  marginLeft: `${nameConfig.value.marginLeft}px`
}))

const descStyles = computed(() => ({
  fontSize: `${descConfig.value.fontSize}px`,
  color: descConfig.value.color,
  marginLeft: `${descConfig.value.marginLeft}px`,
  marginTop: `${descConfig.value.marginTop}px`
}))

const valueStyles = computed(() => ({
  fontSize: `${valueConfig.value.fontSize}px`,
  fontWeight: valueConfig.value.fontWeight,
  color: valueConfig.value.color,
  textAlign: valueConfig.value.textAlign,
  marginLeft: 'auto'
}))

// 方法
const onRowHover = (index: number, isHover: boolean) => {
  hoveredIndex.value = isHover ? index : -1
}

const getRankMedal = (rank: number) => {
  const medals = { 1: '🥇', 2: '🥈', 3: '🥉' }
  return medals[rank] || rank
}

const formatValue = (value: number) => {
  if (value >= 1000) {
    return (value / 1000).toFixed(1) + 'k'
  }
  return value.toString()
}

const handleImageError = (event: Event) => {
  const img = event.target as HTMLImageElement
  img.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIHZpZXdCb3g9IjAgMCA0MCA0MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iMjAiIGN5PSIyMCIgcj0iMjAiIGZpbGw9IiM0Qjc2ODgiLz4KPHN2ZyB3aWR0aD0iMjAiIGhlaWdodD0iMjAiIHZpZXdCb3g9IjAgMCAyMCAyMCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB4PSIxMCIgeT0iMTAiPgo8cGF0aCBkPSJNMTAgMTBDMTIuNzYxNCAxMCAxNSA3Ljc2MTQyIDE1IDVDMTUgMi4yMzg1OCAxMi43NjE0IDAgMTAgMEM3LjIzODU4IDAgNSAyLjIzODU4IDUgNUM1IDcuNzYxNDIgNy4yMzg1OCAxMCAxMCAxMFoiIGZpbGw9IndoaXRlIi8+CjxwYXRoIGQ9Ik0xMCAxMkM2LjY4NjI5IDEyIDQgMTQuNjg2MyA0IDE4VjIwSDEwSDE2VjE4QzE2IDE0LjY4NjMgMTMuMzEzNyAxMiAxMCAxMloiIGZpbGw9IndoaXRlIi8+Cjwvc3ZnPgo8L3N2Zz4K'
}
</script>

<style scoped>
.ranking-list-container {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.ranking-header {
  display: flex;
  align-items: center;
}

.header-rank {
  width: 60px;
  text-align: center;
}

.header-name {
  flex: 1;
  margin-left: 12px;
}

.header-desc {
  width: 120px;
  text-align: center;
}

.header-value {
  width: 80px;
  text-align: right;
}

.ranking-item {
  display: flex;
  align-items: center;
}

.app-info {
  display: flex;
  align-items: center;
  flex: 1;
  margin-left: 12px;
}

.app-avatar {
  flex-shrink: 0;
}

.app-details {
  margin-left: 12px;
}

.rank-medal {
  font-size: 20px;
}
</style>
