import { PublicConfigClass } from '@/packages/public'
import { CreateComponentType } from '@/packages/index.d'
import { TableCardGridConfig } from './index'
import cloneDeep from 'lodash/cloneDeep'
import dataJson from './data.json'

export const option = {
  // 数据
  dataset: dataJson,
  // 每行卡片数量
  cardsPerRow: 2,
  // 卡片间距
  cardGap: 16,
  // 卡片高度
  cardHeight: 120,
  // 是否显示滚动条
  showScrollbar: true,
  // 卡片样式
  cardStyle: {
    // 背景色
    backgroundColor: '#1a2332',
    // 边框色
    borderColor: '#2a3441',
    // 边框宽度
    borderWidth: 1,
    // 圆角
    borderRadius: 8,
    // 阴影
    boxShadow: '0 2px 8px rgba(0,0,0,0.3)'
  },
  // 标题样式
  titleStyle: {
    fontSize: 14,
    color: '#ffffff',
    fontWeight: 'bold'
  },
  // 状态样式
  statusStyle: {
    fontSize: 12,
    otherInfoFontSize: 12,
    color: '#000000',
    onlineColor: '#52c41a',
    offlineColor: '#ff4d4f',
    normalColor: '#1890ff'
  },
  // 标签样式
  tagStyle: {
    fontSize: 12,
    backgroundColor: '#52c41a',
    color: '#ffffff',
    borderRadius: 4,
    padding: '2px 8px',
    width: 80
  },
  // 图标样式
  iconStyle: {
    size: 16,
    color: '#1890ff'
  }
}

export default class Config extends PublicConfigClass implements CreateComponentType {
  public key = TableCardGridConfig.key
  public chartConfig = cloneDeep(TableCardGridConfig)
  public option = cloneDeep(option)
}
