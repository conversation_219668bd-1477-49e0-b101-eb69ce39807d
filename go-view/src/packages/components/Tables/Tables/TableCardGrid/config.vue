<template>
  <CollapseItem name="卡片网格" :expanded="true">
    <SettingItemBox name="布局设置">
      <SettingItem name="每行卡片数">
        <n-input-number
          v-model:value="optionData.cardsPerRow"
          :min="1"
          :max="6"
          size="small"
          placeholder="每行显示卡片数量"
        />
      </SettingItem>
      <SettingItem name="卡片间距">
        <n-input-number
          v-model:value="optionData.cardGap"
          :min="0"
          :max="50"
          size="small"
          placeholder="卡片间距"
        />
      </SettingItem>
      <SettingItem name="卡片高度">
        <n-input-number
          v-model:value="optionData.cardHeight"
          :min="80"
          :max="300"
          size="small"
          placeholder="卡片高度"
        />
      </SettingItem>
      <SettingItem name="显示滚动条">
        <n-switch v-model:value="optionData.showScrollbar" size="small" />
      </SettingItem>
    </SettingItemBox>

    <SettingItemBox name="卡片样式">
      <SettingItem name="背景颜色">
        <n-color-picker v-model:value="optionData.cardStyle.backgroundColor" size="small" />
      </SettingItem>
      <SettingItem name="边框颜色">
        <n-color-picker v-model:value="optionData.cardStyle.borderColor" size="small" />
      </SettingItem>
      <SettingItem name="边框宽度">
        <n-input-number
          v-model:value="optionData.cardStyle.borderWidth"
          :min="0"
          :max="10"
          size="small"
          placeholder="边框宽度"
        />
      </SettingItem>
      <SettingItem name="圆角大小">
        <n-input-number
          v-model:value="optionData.cardStyle.borderRadius"
          :min="0"
          :max="20"
          size="small"
          placeholder="圆角大小"
        />
      </SettingItem>
    </SettingItemBox>

    <SettingItemBox name="文字样式">
      <SettingItem name="标题字体大小">
        <n-input-number
          v-model:value="optionData.titleStyle.fontSize"
          :min="10"
          :max="24"
          size="small"
          placeholder="标题字体大小"
        />
      </SettingItem>
      <SettingItem name="标题颜色">
        <n-color-picker v-model:value="optionData.titleStyle.color" size="small" />
      </SettingItem>
      <SettingItem name="状态字体大小">
        <n-input-number
          v-model:value="optionData.statusStyle.fontSize"
          :min="8"
          :max="18"
          size="small"
          placeholder="状态字体大小"
        />
      </SettingItem>
      <SettingItem name="其他信息字体大小">
        <n-input-number
          v-model:value="optionData.statusStyle.otherInfoFontSize"
          :min="8"
          :max="18"
          size="small"
          placeholder="12"
        />
      </SettingItem>
      <SettingItem name="在线状态颜色">
        <n-color-picker v-model:value="optionData.statusStyle.onlineColor" size="small" />
      </SettingItem>
      <SettingItem name="离线状态颜色">
        <n-color-picker v-model:value="optionData.statusStyle.offlineColor" size="small" />
      </SettingItem>
      <SettingItem name="其他信息颜色">
        <n-color-picker v-model:value="optionData.statusStyle.color" size="small" />
      </SettingItem>
    </SettingItemBox>

    <SettingItemBox name="标签样式">
      <SettingItem name="标签字体大小">
        <n-input-number
          v-model:value="optionData.tagStyle.fontSize"
          :min="8"
          :max="16"
          size="small"
          placeholder="标签字体大小"
        />
      </SettingItem>
      <SettingItem name="标签背景色">
        <n-color-picker v-model:value="optionData.tagStyle.backgroundColor" size="small" />
      </SettingItem>
      <SettingItem name="标签文字颜色">
        <n-color-picker v-model:value="optionData.tagStyle.color" size="small" />
      </SettingItem>
      <SettingItem name="标签宽度">
        <n-input-number
          v-model:value="optionData.tagStyle.width"
          :min="60"
          :max="150"
          size="small"
          placeholder="标签宽度"
        />
      </SettingItem>
      <SettingItem name="标签圆角">
        <n-input-number
          v-model:value="optionData.tagStyle.borderRadius"
          :min="0"
          :max="10"
          size="small"
          placeholder="标签圆角"
        />
      </SettingItem>
    </SettingItemBox>
  </CollapseItem>
</template>

<script setup lang="ts">
import { PropType, onMounted } from 'vue'
import { CollapseItem, SettingItemBox, SettingItem } from '@/components/Pages/ChartItemSetting'
import { option } from './config'

const props = defineProps({
  optionData: {
    type: Object as PropType<typeof option>,
    required: true
  }
})

// 初始化默认值
onMounted(() => {
  if (props.optionData.statusStyle.otherInfoFontSize === undefined) {
    props.optionData.statusStyle.otherInfoFontSize = 12
  }
  if (props.optionData.tagStyle.width === undefined) {
    props.optionData.tagStyle.width = 80
  }
})
</script>
