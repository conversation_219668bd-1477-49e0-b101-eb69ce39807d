<template>
  <div 
    class="go-table-card-grid" 
    :style="containerStyle"
  >
    <div 
      class="card-grid-container"
      :style="gridStyle"
    >
      <div
        v-for="(item, index) in dataset"
        :key="item.id || index"
        class="card-item"
        :style="cardItemStyle"
      >
        <!-- 状态指示器 -->
        <div class="status-indicator" :class="getStatusClass(item.status)"></div>
        
        <!-- 卡片内容 -->
        <div class="card-content">
          <!-- 标题行 -->
          <div class="card-header">
            <div class="card-title" :style="getTitleStyle">{{ item.title }}</div>
          </div>

          <!-- 状态信息行 -->
          <div class="card-status">
            <div class="status-item">
              <span class="status-text" :style="getStatusTextStyle(item.status)">{{ item.status }}</span>
            </div>
            <div class="status-item">
              <span class="type-text" :style="getOtherInfoStyle">{{ item.type }}</span>
            </div>
            <div class="status-item">
              <span class="speed-text" :style="getOtherInfoStyle">{{ item.speed }}</span>
            </div>
            <div class="status-item">
              <span class="percentage-text" :style="getOtherInfoStyle">{{ item.percentage }}</span>
            </div>
          </div>
          
          <!-- 标签 -->
          <div class="card-tag">
            <span 
              class="tag" 
              :class="getTagClass(item.tagType)"
              :style="getTagStyle(item.tagType)"
            >
              {{ item.tag }}
            </span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { PropType, computed, toRefs } from 'vue'
import { CreateComponentType } from '@/packages/index.d'
import { useChartDataFetch } from '@/hooks'
import { useChartEditStore } from '@/store/modules/chartEditStore/chartEditStore'

const props = defineProps({
  chartConfig: {
    type: Object as PropType<CreateComponentType>,
    required: true
  }
})

const { w, h } = toRefs(props.chartConfig.attr)
const { 
  dataset, 
  cardsPerRow, 
  cardGap, 
  cardHeight, 
  showScrollbar,
  cardStyle,
  titleStyle,
  statusStyle,
  tagStyle,
  iconStyle
} = toRefs(props.chartConfig.option)

// 容器样式
const containerStyle = computed(() => ({
  width: `${w.value}px`,
  height: `${h.value}px`,
  overflow: showScrollbar.value ? 'auto' : 'hidden',
  padding: `${cardGap.value}px`,
  boxSizing: 'border-box'
}))

// 网格样式
const gridStyle = computed(() => ({
  display: 'grid',
  gridTemplateColumns: `repeat(${cardsPerRow.value}, 1fr)`,
  gap: `${cardGap.value}px`,
  width: '100%',
  height: 'fit-content'
}))

// 卡片样式
const cardItemStyle = computed(() => ({
  position: 'relative',
  height: `${cardHeight.value}px`,
  backgroundColor: cardStyle.value.backgroundColor,
  border: `${cardStyle.value.borderWidth}px solid ${cardStyle.value.borderColor}`,
  borderRadius: `${cardStyle.value.borderRadius}px`,
  boxShadow: cardStyle.value.boxShadow,
  padding: '12px',
  display: 'flex',
  flexDirection: 'column',
  justifyContent: 'space-between'
}))

// 获取状态指示器类名
const getStatusClass = (status: string) => {
  return status === '在线' ? 'online' : 'offline'
}

// 获取标题样式
const getTitleStyle = computed(() => ({
  fontSize: `${titleStyle.value.fontSize}px`,
  color: titleStyle.value.color,
  fontWeight: titleStyle.value.fontWeight
}))

// 获取状态文本样式
const getStatusTextStyle = (status: string) => ({
  fontSize: `${statusStyle.value.fontSize}px`,
  color: status === '在线' ? statusStyle.value.onlineColor : statusStyle.value.offlineColor
})

// 获取其他信息样式
const getOtherInfoStyle = computed(() => ({
  fontSize: `${statusStyle.value.otherInfoFontSize || 12}px`,
  color: statusStyle.value.color
}))

// 获取标签类名
const getTagClass = (tagType: string) => {
  return `tag-${tagType}`
}

// 获取标签样式
const getTagStyle = (tagType: string) => {
  let backgroundColor = tagStyle.value.backgroundColor
  if (tagType === 'info') backgroundColor = '#1890ff'
  if (tagType === 'default') backgroundColor = '#666666'

  return {
    fontSize: `${tagStyle.value.fontSize}px`,
    backgroundColor,
    color: tagStyle.value.color,
    borderRadius: `${tagStyle.value.borderRadius}px`,
    padding: tagStyle.value.padding,
    width: `${tagStyle.value.width || 80}px`,
    textAlign: 'center',
    lineHeight: '1.2',
    minHeight: '20px',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center'
  }
}

// 数据获取
useChartDataFetch(props.chartConfig, useChartEditStore, (newData: any) => {
  props.chartConfig.option.dataset = newData
})
</script>

<style lang="scss" scoped>
.go-table-card-grid {
  position: relative;
  box-sizing: border-box;

  .card-grid-container {
    width: 100%;
    box-sizing: border-box;
  }
  
  .card-item {
    cursor: pointer;
    transition: all 0.3s ease;
    
    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(0,0,0,0.4) !important;
    }
  }
  
  .status-indicator {
    position: absolute;
    top: 8px;
    left: 8px;
    width: 8px;
    height: 8px;
    border-radius: 50%;
    
    &.online {
      background-color: #52c41a;
    }
    
    &.offline {
      background-color: #ff4d4f;
    }
  }
  
  .card-content {
    margin-left: 16px;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
  }
  
  .card-header {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 12px;
  }

  .card-title {
    font-weight: bold;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    text-align: center;
  }

  .card-status {
    display: flex;
    width: 100%;
    margin-bottom: 8px;
    font-size: 12px;
  }

  .status-item {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
  }
  
  .card-tag {
    display: flex;
    justify-content: center;
  }
  
  .tag {
    font-size: 12px;
    border-radius: 4px;
    padding: 2px 8px;
  }
}

// 滚动条样式
.go-table-card-grid::-webkit-scrollbar {
  width: 6px;
}

.go-table-card-grid::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 3px;
}

.go-table-card-grid::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.3);
  border-radius: 3px;
  
  &:hover {
    background: rgba(255, 255, 255, 0.5);
  }
}
</style>
