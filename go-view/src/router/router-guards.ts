import { Router } from 'vue-router';
import { PageEnum, PreviewEnum } from '@/enums/pageEnum'
import { loginCheck } from '@/utils'
import { useSystemStore } from '@/store/modules/systemStore/systemStore'
import { SystemStoreEnum, SystemStoreUserInfoEnum } from '@/store/modules/systemStore/systemStore.d'
import { initTokenRefreshMechanism } from '@/utils/tokenRefresh'
import { parseExternalAuthParams, validateAuthParams, redirectToLogin } from '@/utils/authUtils'

// 路由白名单（只保留预览功能，登录统一使用 CCAPI）
const routerAllowList = [
  // 预览
  PreviewEnum.CHART_PREVIEW_NAME
]

// 检查外部系统认证信息并自动登录
const checkExternalAuth = (query: any) => {
  // 解析外部认证参数
  const authParams = parseExternalAuthParams(query)

  console.log('🔍 GoView路由守卫检查外部认证:', {
    query,
    token: authParams.token ? authParams.token.substring(0, 10) + '...' : 'null',
    refreshToken: authParams.refreshToken ? authParams.refreshToken.substring(0, 10) + '...' : 'null',
    expiresIn: authParams.expiresIn,
    user: authParams.user,
    sourceSystem: authParams.sourceSystem,
    loginUrl: authParams.loginUrl,
    hasCompleteAuth: !!(authParams.token && authParams.refreshToken && authParams.user)
  })

  // 验证必需参数
  const validation = validateAuthParams(query)
  if (!validation.valid) {
    console.log('❌ 认证参数验证失败:', validation.error)
    return false
  }

  if (authParams.token && authParams.refreshToken && authParams.user) {
    console.log(`✅ 检测到来自 ${authParams.sourceSystem} 的完整认证信息，设置登录状态...`)

    // 有token就认为有效，直接设置登录状态
    // 如果token无效，后续API调用会返回401，axios拦截器会处理
    const systemStore = useSystemStore()

    // 设置完整的用户认证信息
    systemStore.setItem(SystemStoreEnum.USER_INFO, {
      [SystemStoreUserInfoEnum.USER_TOKEN]: authParams.token,
      [SystemStoreUserInfoEnum.REFRESH_TOKEN]: authParams.refreshToken,
      [SystemStoreUserInfoEnum.EXPIRES_IN]: authParams.expiresIn || '0',
      [SystemStoreUserInfoEnum.TOKEN_NAME]: 'X-Token',
      [SystemStoreUserInfoEnum.USER_ID]: authParams.userId || '1',
      [SystemStoreUserInfoEnum.USER_NAME]: authParams.user,
      [SystemStoreUserInfoEnum.NICK_NAME]: authParams.nickname || authParams.user,
      [SystemStoreUserInfoEnum.SOURCE_SYSTEM]: authParams.sourceSystem,
      [SystemStoreUserInfoEnum.LOGIN_URL]: authParams.loginUrl || 'http://localhost:3000/lowcode/login'
    })

    console.log('✅ 已设置GoView完整认证信息:', {
      token: authParams.token.substring(0, 10) + '...',
      refreshToken: authParams.refreshToken.substring(0, 10) + '...',
      expiresIn: authParams.expiresIn,
      sourceSystem: authParams.sourceSystem,
      loginUrl: authParams.loginUrl,
      userName: authParams.user
    })

    console.log(`${authParams.sourceSystem} 自动登录成功，立即启动无感刷新机制`)

    // 立即启动token刷新机制
    setTimeout(() => {
      initTokenRefreshMechanism()
    }, 1000) // 延迟1秒启动，确保应用完全初始化

    return true
  }

  return false
}

export function createRouterGuards(router: Router) {
  // 前置
  router.beforeEach(async (to, from, next) => {
    // http://localhost:3000/#/chart/preview/792622755697790976?t=123
    // 把外部动态参数放入window.route.params，后续API动态接口可以用window.route?.params?.t来拼接参数
    // @ts-ignore
    if (!window.route) window.route = {params: {}}
    // @ts-ignore
    Object.assign(window.route.params, to.query)

    const Loading = window['$loading'];
    Loading && Loading.start();
    const isErrorPage = router.getRoutes().findIndex((item) => item.name === to.name);
    if (isErrorPage === -1) {
      next({ name: PageEnum.ERROR_PAGE_NAME_404 })
    }

    // 检查是否需要登录
    // @ts-ignore
    if (!routerAllowList.includes(to.name)) {
      // 先检查外部系统认证信息
      const externalLoginSuccess = checkExternalAuth(to.query)

      // 如果外部登录成功或者已经登录，则继续
      if (externalLoginSuccess || loginCheck()) {
        next()
        return
      }

      // 否则跳转到登录页面
      console.log('❌ 未检测到有效认证信息，跳转到登录页面')
      redirectToLogin()
      return
    }

    // 白名单路由直接通过
    next()
  })

  router.afterEach((to, _, failure) => {
    const Loading = window['$loading'];
    document.title = (to?.meta?.title as string) || document.title;
    Loading && Loading.finish();
  })

  // 错误
  router.onError((error) => {
    console.log(error, '路由错误');
  });
}