/**
 * 认证相关配置
 */

// Token刷新API端点
export const TOKEN_REFRESH_URL = import.meta.env.VITE_TOKEN_REFRESH_URL || 'http://localhost:3000/api/user/v2/token/refresh'

// 默认登录页面URL
export const DEFAULT_LOGIN_URL = import.meta.env.VITE_DEFAULT_LOGIN_URL || 'http://localhost:3000/lowcode/login'

// 应用标题
export const APP_TITLE = import.meta.env.VITE_APP_TITLE || 'GoView数据可视化'

console.log('🔧 认证配置加载:', {
  TOKEN_REFRESH_URL,
  DEFAULT_LOGIN_URL,
  APP_TITLE,
  NODE_ENV: import.meta.env.MODE
})
