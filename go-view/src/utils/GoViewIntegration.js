/**
 * GoView 通用集成工具类
 * 
 * 使用示例：
 * ```javascript
 * import { GoViewIntegration } from './GoViewIntegration.js'
 * 
 * const goview = new GoViewIntegration({
 *   baseUrl: 'http://localhost:3001',
 *   systemName: 'my-system',
 *   loginUrl: 'http://my-system.com/login'
 * })
 * 
 * // 打开编辑器
 * goview.openEditor({ projectId: 123 })
 * 
 * // 打开预览
 * goview.openPreview(123)
 * ```
 */

export class GoViewIntegration {
  constructor(config = {}) {
    this.baseUrl = config.baseUrl || 'http://localhost:3001'
    this.systemName = config.systemName || 'external-system'
    this.loginUrl = config.loginUrl || 'http://localhost:3000/lowcode/login'
    this.getUserInfo = config.getUserInfo || this.getDefaultUserInfo.bind(this)
    
    console.log('🔧 GoView集成工具初始化:', {
      baseUrl: this.baseUrl,
      systemName: this.systemName,
      loginUrl: this.loginUrl
    })
  }

  /**
   * 打开GoView编辑器
   * @param {Object} options 选项
   * @param {number} options.projectId 项目ID（可选）
   * @param {boolean} options.newWindow 是否在新窗口打开（默认true）
   * @param {string} options.route 自定义路由（可选）
   */
  openEditor(options = {}) {
    const route = options.route || (options.projectId ? `/chart/home/<USER>'/project/items')
    const url = this.buildAuthUrl(route)
    
    console.log('🚀 打开GoView编辑器:', {
      projectId: options.projectId,
      route,
      newWindow: options.newWindow !== false,
      url: url.substring(0, 100) + '...'
    })
    
    if (options.newWindow !== false) {
      window.open(url, '_blank')
    } else {
      window.location.href = url
    }
  }

  /**
   * 打开GoView预览页面
   * @param {number} projectId 项目ID
   * @param {boolean} newWindow 是否在新窗口打开（默认true）
   */
  openPreview(projectId, newWindow = true) {
    // 预览页面不需要认证参数
    const url = `${this.baseUrl}/#/chart/preview/${projectId}`
    
    console.log('👁️ 打开GoView预览:', {
      projectId,
      newWindow,
      url
    })
    
    if (newWindow) {
      window.open(url, '_blank')
    } else {
      window.location.href = url
    }
  }

  /**
   * 构建带认证参数的URL
   * @param {string} route 路由路径
   * @returns {string} 完整的URL
   */
  buildAuthUrl(route) {
    const userInfo = this.getUserInfo()
    
    if (!userInfo || !userInfo.token || !userInfo.refreshToken || !userInfo.username) {
      throw new Error('无法获取有效的用户认证信息，请确保用户已登录')
    }
    
    const authParams = new URLSearchParams({
      external_token: userInfo.token,
      external_refresh_token: userInfo.refreshToken,
      external_expires_in: userInfo.expiresIn?.toString() || '0',
      external_user: userInfo.username,
      external_nickname: userInfo.nickname || userInfo.username,
      external_user_id: userInfo.userId?.toString() || '1',
      external_system: this.systemName,
      external_login_url: this.loginUrl
    })
    
    return `${this.baseUrl}/#${route}?${authParams.toString()}`
  }

  /**
   * 默认的用户信息获取方法
   * 子类可以重写此方法或在构造函数中传入自定义方法
   */
  getDefaultUserInfo() {
    // 尝试从常见的存储位置获取用户信息
    try {
      return {
        token: localStorage.getItem('access_token') || localStorage.getItem('token'),
        refreshToken: localStorage.getItem('refresh_token') || localStorage.getItem('refreshToken'),
        expiresIn: parseInt(localStorage.getItem('expires_in') || localStorage.getItem('expiresIn') || '0'),
        username: localStorage.getItem('username') || localStorage.getItem('user'),
        nickname: localStorage.getItem('nickname') || localStorage.getItem('displayName'),
        userId: localStorage.getItem('user_id') || localStorage.getItem('userId')
      }
    } catch (error) {
      console.error('获取用户信息失败:', error)
      return null
    }
  }

  /**
   * 验证用户认证状态
   * @returns {boolean} 是否已认证
   */
  isAuthenticated() {
    const userInfo = this.getUserInfo()
    return !!(userInfo && userInfo.token && userInfo.refreshToken && userInfo.username)
  }

  /**
   * 获取当前用户信息
   * @returns {Object|null} 用户信息
   */
  getCurrentUser() {
    return this.getUserInfo()
  }
}

/**
 * 创建CCAPI集成实例的便捷方法
 */
export function createCCAPIIntegration() {
  return new GoViewIntegration({
    baseUrl: 'http://localhost:3001',
    systemName: 'ccapi',
    loginUrl: 'http://localhost:3000/lowcode/login',
    getUserInfo: () => {
      try {
        const user = JSON.parse(localStorage.getItem('user') || '{}')
        return {
          token: localStorage.getItem('token'),
          refreshToken: localStorage.getItem('refreshToken'),
          expiresIn: parseInt(localStorage.getItem('expiresIn') || '0'),
          username: user.username,
          nickname: user.nickname,
          userId: user.id
        }
      } catch (error) {
        console.error('获取CCAPI用户信息失败:', error)
        return null
      }
    }
  })
}

/**
 * 全局便捷方法
 */
window.GoViewIntegration = GoViewIntegration
window.createCCAPIIntegration = createCCAPIIntegration

console.log('📦 GoView集成工具类已加载')
