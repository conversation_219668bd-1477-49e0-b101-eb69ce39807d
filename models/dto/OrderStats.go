package dto

import (
	"time"
)

// GoViewDataset GoView 通用数据格式
type GoViewDataset struct {
	Dimensions []string      `json:"dimensions"` // 维度定义
	Source     []interface{} `json:"source"`     // 数据源
}

// GoViewTableDataset GoView 表格数据格式
type GoViewTableDataset struct {
	Dimensions []GoViewTableColumn `json:"dimensions"` // 列定义
	Source     []interface{}       `json:"source"`     // 数据源
}

// GoViewTableColumn 表格列定义
type GoViewTableColumn struct {
	Title string `json:"title"` // 列标题
	Key   string `json:"key"`   // 列键名
}

// OrderStatsOverview 订单概览统计 - 适配 GoView 大屏组件
type OrderStatsOverview struct {
	// 数字卡片组件数据（直接是数值，可直接用于 GoView 数字组件）
	TotalOrders     int64 `json:"totalOrders"`     // 总订单数
	TotalAmount     int64 `json:"totalAmount"`     // 总金额（分）
	TotalPassengers int64 `json:"totalPassengers"` // 总乘客数
	TodayOrders     int64 `json:"todayOrders"`     // 今日订单数
	TodayAmount     int64 `json:"todayAmount"`     // 今日金额
	TodayPassengers int64 `json:"todayPassengers"` // 今日乘客数

	// 饼图组件数据（状态分布，可直接用于 GoView 饼图组件）
	StatusChart GoViewDataset `json:"statusChart"` // 状态分布饼图数据

	// 表格组件数据（状态详情，可直接用于 GoView 表格组件）
	StatusTable GoViewTableDataset `json:"statusTable"` // 状态详情表格数据
}

// 注意：以下数据结构设计为直接适配 GoView 大屏组件
// - 数字卡片组件：直接使用 int64/float64 数值
// - 图表组件（饼图、柱状图、折线图）：使用 GoViewDataset 格式
// - 表格组件：使用 GoViewTableDataset 格式

// OrderTrendQuery 订单趋势查询参数
type OrderTrendQuery struct {
	StartDate string `query:"startDate"` // 开始日期 YYYY-MM-DD
	EndDate   string `query:"endDate"`   // 结束日期 YYYY-MM-DD
	GroupBy   string `query:"groupBy"`   // 分组方式: day, week, month
}

// OrderTrendStats 订单趋势统计 - 适配 GoView 折线图组件
type OrderTrendStats struct {
	// 折线图组件数据（可直接用于 GoView 折线图组件）
	TrendChart GoViewDataset `json:"trendChart"` // 趋势图表数据

	// 汇总信息（数字卡片）
	TotalDays      int     `json:"totalDays"`      // 总天数
	TotalOrders    int64   `json:"totalOrders"`    // 总订单数
	TotalAmount    int64   `json:"totalAmount"`    // 总金额
	AvgDailyOrders float64 `json:"avgDailyOrders"` // 日均订单数
	AvgDailyAmount float64 `json:"avgDailyAmount"` // 日均金额
	GrowthRate     float64 `json:"growthRate"`     // 增长率
}



// RouteStatsQuery 路线统计查询参数
type RouteStatsQuery struct {
	StartDate string `query:"startDate"` // 开始日期
	EndDate   string `query:"endDate"`   // 结束日期
	Limit     int    `query:"limit,default=10"` // 返回数量限制
}

// RouteStats 路线统计 - 适配 GoView 柱状图和表格组件
type RouteStats struct {
	// 柱状图组件数据（可直接用于 GoView 柱状图组件）
	RouteChart GoViewDataset `json:"routeChart"` // 路线排行柱状图数据

	// 表格组件数据（可直接用于 GoView 表格组件）
	RouteTable GoViewTableDataset `json:"routeTable"` // 路线详情表格数据

	// 汇总信息（数字卡片）
	TotalRoutes    int   `json:"totalRoutes"`    // 总路线数
	ActiveRoutes   int   `json:"activeRoutes"`   // 活跃路线数
	TopRouteOrders int64 `json:"topRouteOrders"` // 最热路线订单数
	TopRouteAmount int64 `json:"topRouteAmount"` // 最热路线金额
}



// RegionStatsQuery 地区统计查询参数
type RegionStatsQuery struct {
	StartDate string `query:"startDate"` // 开始日期
	EndDate   string `query:"endDate"`   // 结束日期
	Type      string `query:"type"`      // 统计类型: begin, end, route
}

// RegionStats 地区统计 - 适配 GoView 饼图和表格组件
type RegionStats struct {
	// 饼图组件数据（可直接用于 GoView 饼图组件）
	RegionChart GoViewDataset `json:"regionChart"` // 地区分布饼图数据

	// 表格组件数据（可直接用于 GoView 表格组件）
	RegionTable GoViewTableDataset `json:"regionTable"` // 地区详情表格数据

	// 汇总信息（数字卡片）
	TotalRegions    int    `json:"totalRegions"`    // 总地区数
	TopRegion       string `json:"topRegion"`       // 最热地区
	TopRegionOrders int64  `json:"topRegionOrders"` // 最热地区订单数
}



// VehicleStatsQuery 车辆统计查询参数
type VehicleStatsQuery struct {
	StartDate string `query:"startDate"` // 开始日期
	EndDate   string `query:"endDate"`   // 结束日期
	Limit     int    `query:"limit,default=10"` // 返回数量限制
}

// VehicleStats 车辆统计 - 适配 GoView 柱状图和表格组件
type VehicleStats struct {
	// 柱状图组件数据（可直接用于 GoView 柱状图组件）
	VehicleChart GoViewDataset `json:"vehicleChart"` // 车辆使用率柱状图数据

	// 表格组件数据（可直接用于 GoView 表格组件）
	VehicleTable GoViewTableDataset `json:"vehicleTable"` // 车辆详情表格数据

	// 汇总信息（数字卡片）
	TotalVehicles     int     `json:"totalVehicles"`     // 总车辆数
	ActiveVehicles    int     `json:"activeVehicles"`    // 活跃车辆数
	AvgUtilization    float64 `json:"avgUtilization"`    // 平均使用率
	TopVehicleOrders  int64   `json:"topVehicleOrders"`  // 最忙车辆订单数
}



// UserStatsQuery 用户统计查询参数
type UserStatsQuery struct {
	StartDate string `query:"startDate"` // 开始日期
	EndDate   string `query:"endDate"`   // 结束日期
	Country   string `query:"country"`   // 国家筛选
}

// UserStats 用户统计 - 适配 GoView 饼图和数字卡片组件
type UserStats struct {
	// 数字卡片组件数据
	TotalUsers         int64 `json:"totalUsers"`         // 总用户数
	NewUsers           int64 `json:"newUsers"`           // 新增用户数
	ActiveUsers        int64 `json:"activeUsers"`        // 活跃用户数
	OrderingUsers      int64 `json:"orderingUsers"`      // 下单用户数
	DailyActiveUsers   int64 `json:"dailyActiveUsers"`   // 日活用户
	WeeklyActiveUsers  int64 `json:"weeklyActiveUsers"`  // 周活用户
	MonthlyActiveUsers int64 `json:"monthlyActiveUsers"` // 月活用户
	RetentionRate      float64 `json:"retentionRate"`    // 留存率

	// 饼图组件数据（国家分布）
	CountryChart GoViewDataset `json:"countryChart"` // 国家分布饼图数据

	// 表格组件数据（国家详情）
	CountryTable GoViewTableDataset `json:"countryTable"` // 国家详情表格数据
}

// RealtimeStatsQuery 实时统计查询参数
type RealtimeStatsQuery struct {
	Hours int `query:"hours,default=24"` // 查询最近几小时的数据
}

// RealtimeStats 实时统计 - 适配 GoView 实时大屏组件
type RealtimeStats struct {
	// 实时折线图数据（可直接用于 GoView 折线图组件）
	HourlyChart GoViewDataset `json:"hourlyChart"` // 小时趋势图表数据

	// 实时表格数据（可直接用于 GoView 表格组件）
	RealtimeTable GoViewTableDataset `json:"realtimeTable"` // 实时订单表格数据

	// 实时数字卡片
	CurrentHourOrders int64     `json:"currentHourOrders"` // 当前小时订单数
	CurrentHourAmount int64     `json:"currentHourAmount"` // 当前小时金额
	OnlineOrders      int64     `json:"onlineOrders"`      // 在线订单数
	PendingOrders     int64     `json:"pendingOrders"`     // 待处理订单数
	LastUpdateTime    time.Time `json:"lastUpdateTime"`    // 最后更新时间
	TrendDirection    string    `json:"trendDirection"`    // 趋势方向: up, down, stable
}

// OrderStatusRadarQuery 订单状态雷达图查询参数
type OrderStatusRadarQuery struct {
	StartDate string `query:"startDate"` // 开始日期 YYYY-MM-DD
	EndDate   string `query:"endDate"`   // 结束日期 YYYY-MM-DD
	CompareType string `query:"compareType"` // 对比类型: period(时期对比), none(无对比)
}

// RadarIndicator 雷达图指标定义
type RadarIndicator struct {
	Name string `json:"name"` // 指标名称
	Max  int64  `json:"max"`  // 最大值
}

// RadarSeriesData 雷达图系列数据
type RadarSeriesData struct {
	Name  string  `json:"name"`  // 系列名称
	Value []int64 `json:"value"` // 数据值数组
}

// OrderStatusRadarStats 订单状态雷达图统计 - 适配 GoView 雷达图组件
type OrderStatusRadarStats struct {
	// 雷达图组件数据（可直接用于 GoView 雷达图组件）
	RadarIndicator []RadarIndicator  `json:"radarIndicator"` // 雷达图指标定义
	SeriesData     []RadarSeriesData `json:"seriesData"`     // 雷达图系列数据

	// 汇总信息（数字卡片）
	TotalOrders       int64   `json:"totalOrders"`       // 总订单数
	CompletionRate    float64 `json:"completionRate"`    // 完成率
	CancellationRate  float64 `json:"cancellationRate"`  // 取消率
	RefundRate        float64 `json:"refundRate"`        // 退款率
	PendingOrders     int64   `json:"pendingOrders"`     // 待处理订单数
	ProcessingOrders  int64   `json:"processingOrders"`  // 处理中订单数
}


