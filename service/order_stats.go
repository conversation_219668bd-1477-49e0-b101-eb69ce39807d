package service

import (
	"ccapi/models/dto"
	"fmt"
	"github.com/beego/beego/v2/client/orm"
	"github.com/beego/beego/v2/core/logs"
	"github.com/beego/beego/v2/server/web/context"
	"strings"
	"time"
)

type OrderStatsServiceInter interface {
	// GetOverviewStats 获取订单概览统计
	GetOverviewStats(ctx *context.Context, userId int64) (*dto.OrderStatsOverview, error)
	// GetTrendStats 获取订单趋势统计
	GetTrendStats(query *dto.OrderTrendQuery, ctx *context.Context, userId int64) (*dto.OrderTrendStats, error)
	// GetRouteStats 获取路线统计
	GetRouteStats(query *dto.RouteStatsQuery, ctx *context.Context, userId int64) (*dto.RouteStats, error)
	// GetRegionStats 获取地区统计
	GetRegionStats(query *dto.RegionStatsQuery, ctx *context.Context, userId int64) (*dto.RegionStats, error)
	// GetVehicleStats 获取车辆统计
	GetVehicleStats(query *dto.VehicleStatsQuery, ctx *context.Context, userId int64) (*dto.VehicleStats, error)
	// GetUserStats 获取用户统计
	GetUserStats(query *dto.UserStatsQuery, ctx *context.Context, userId int64) (*dto.UserStats, error)
	// GetRealtimeStats 获取实时统计
	GetRealtimeStats(query *dto.RealtimeStatsQuery, ctx *context.Context, userId int64) (*dto.RealtimeStats, error)
	// GetStatusRadarStats 获取订单状态雷达图统计
	GetStatusRadarStats(query *dto.OrderStatusRadarQuery, ctx *context.Context, userId int64) (*dto.OrderStatusRadarStats, error)
}

type OrderStatsService struct {
}

func NewOrderStatsService() OrderStatsServiceInter {
	return &OrderStatsService{}
}

// GetOverviewStats 获取订单概览统计
func (s *OrderStatsService) GetOverviewStats(ctx *context.Context, userId int64) (*dto.OrderStatsOverview, error) {
	o := orm.NewOrmUsingDB("robobusWX")

	// 基础统计查询
	baseSql := `
		SELECT
			COUNT(*) as total_orders,
			COALESCE(SUM(CASE WHEN total_fee IS NOT NULL THEN total_fee ELSE price * num END), 0) as total_amount,
			COALESCE(SUM(num), 0) as total_passengers
		FROM ` + "`order`" + ` o
		LEFT JOIN ` + "`route`" + ` r ON o.route_id = r.id
		WHERE 1=1
	`

	// 添加用户权限过滤
	if userId != 1 {
		baseSql += fmt.Sprintf(" AND r.fms_user_id = %d", userId)
	}

	var baseStats struct {
		TotalOrders    int64 `orm:"column(total_orders)"`
		TotalAmount    int64 `orm:"column(total_amount)"`
		TotalPassengers int64 `orm:"column(total_passengers)"`
	}

	err := o.Raw(baseSql).QueryRow(&baseStats)
	if err != nil {
		logs.Error("获取基础统计失败: %v", err)
		return nil, err
	}

	// 今日统计
	today := time.Now()
	todayStart := time.Date(today.Year(), today.Month(), today.Day(), 0, 0, 0, 0, today.Location()).Unix()
	todayEnd := todayStart + 86400

	todaySql := baseSql + fmt.Sprintf(" AND o.created_time >= %d AND o.created_time < %d", todayStart, todayEnd)

	var todayStats struct {
		TodayOrders    int64 `orm:"column(total_orders)"`
		TodayAmount    int64 `orm:"column(total_amount)"`
		TodayPassengers int64 `orm:"column(total_passengers)"`
	}

	err = o.Raw(todaySql).QueryRow(&todayStats)
	if err != nil {
		logs.Error("获取今日统计失败: %v", err)
		return nil, err
	}

	// 状态统计 - 构建 GoView 饼图数据格式
	statusSql := `
		SELECT
			o.status,
			COUNT(*) as count,
			COALESCE(SUM(CASE WHEN total_fee IS NOT NULL THEN total_fee ELSE price * num END), 0) as amount
		FROM ` + "`order`" + ` o
		LEFT JOIN ` + "`route`" + ` r ON o.route_id = r.id
		WHERE 1=1
	`

	if userId != 1 {
		statusSql += fmt.Sprintf(" AND r.fms_user_id = %d", userId)
	}
	statusSql += " GROUP BY o.status ORDER BY o.status"

	var statusResults []struct {
		Status int   `orm:"column(status)"`
		Count  int64 `orm:"column(count)"`
		Amount int64 `orm:"column(amount)"`
	}

	_, err = o.Raw(statusSql).QueryRows(&statusResults)
	if err != nil {
		logs.Error("获取状态统计失败: %v", err)
		return nil, err
	}

	// 构建 GoView 饼图数据格式
	statusMap := map[int]string{
		0: "待确认",
		1: "已确认",
		2: "已取消",
		3: "已完成",
		4: "已退款",
	}

	statusChartSource := make([]interface{}, 0)
	statusTableSource := make([]interface{}, 0)

	for _, stat := range statusResults {
		statusName := statusMap[stat.Status]
		if statusName == "" {
			statusName = "未知状态"
		}

		percentage := float64(0)
		if baseStats.TotalOrders > 0 {
			percentage = float64(stat.Count) / float64(baseStats.TotalOrders) * 100
		}

		// 饼图数据
		statusChartSource = append(statusChartSource, map[string]interface{}{
			"status": statusName,
			"count":  stat.Count,
		})

		// 表格数据
		statusTableSource = append(statusTableSource, map[string]interface{}{
			"statusName": statusName,
			"count":      stat.Count,
			"amount":     float64(stat.Amount) / 100, // 转换为元
			"percentage": fmt.Sprintf("%.1f%%", percentage),
		})
	}

	result := &dto.OrderStatsOverview{
		TotalOrders:     baseStats.TotalOrders,
		TotalAmount:     baseStats.TotalAmount,
		TotalPassengers: baseStats.TotalPassengers,
		TodayOrders:     todayStats.TodayOrders,
		TodayAmount:     todayStats.TodayAmount,
		TodayPassengers: todayStats.TodayPassengers,
		StatusChart: dto.GoViewDataset{
			Dimensions: []string{"status", "count"},
			Source:     statusChartSource,
		},
		StatusTable: dto.GoViewTableDataset{
			Dimensions: []dto.GoViewTableColumn{
				{Title: "状态", Key: "statusName"},
				{Title: "订单数", Key: "count"},
				{Title: "金额(元)", Key: "amount"},
				{Title: "占比", Key: "percentage"},
			},
			Source: statusTableSource,
		},
	}

	return result, nil
}

// GetTrendStats 获取订单趋势统计
func (s *OrderStatsService) GetTrendStats(query *dto.OrderTrendQuery, ctx *context.Context, userId int64) (*dto.OrderTrendStats, error) {
	o := orm.NewOrmUsingDB("robobusWX")

	// 设置默认值
	if query.StartDate == "" {
		query.StartDate = time.Now().AddDate(0, 0, -30).Format("2006-01-02")
	}
	if query.EndDate == "" {
		query.EndDate = time.Now().Format("2006-01-02")
	}
	if query.GroupBy == "" {
		query.GroupBy = "day"
	}

	// 解析日期
	startTime, err := time.Parse("2006-01-02", query.StartDate)
	if err != nil {
		return nil, fmt.Errorf("开始日期格式错误: %v", err)
	}
	endTime, err := time.Parse("2006-01-02", query.EndDate)
	if err != nil {
		return nil, fmt.Errorf("结束日期格式错误: %v", err)
	}

	startTimestamp := startTime.Unix()
	endTimestamp := endTime.Unix() + 86400 // 包含结束日期当天

	// 根据分组方式构建日期格式
	var dateFormat string
	switch query.GroupBy {
	case "week":
		dateFormat = "DATE_FORMAT(FROM_UNIXTIME(o.created_time), '%Y-%u')"
	case "month":
		dateFormat = "DATE_FORMAT(FROM_UNIXTIME(o.created_time), '%Y-%m')"
	default: // day
		dateFormat = "DATE_FORMAT(FROM_UNIXTIME(o.created_time), '%Y-%m-%d')"
	}

	sql := fmt.Sprintf(`
		SELECT
			%s as date,
			COUNT(*) as order_count,
			COALESCE(SUM(CASE WHEN total_fee IS NOT NULL THEN total_fee ELSE price * num END), 0) as amount,
			COALESCE(SUM(num), 0) as passengers
		FROM `+"`order`"+` o
		LEFT JOIN `+"`route`"+` r ON o.route_id = r.id
		WHERE o.created_time >= %d AND o.created_time < %d
	`, dateFormat, startTimestamp, endTimestamp)

	if userId != 1 {
		sql += fmt.Sprintf(" AND r.fms_user_id = %d", userId)
	}

	sql += fmt.Sprintf(" GROUP BY %s ORDER BY date", dateFormat)

	var trendResults []struct {
		Date       string `orm:"column(date)"`
		OrderCount int64  `orm:"column(order_count)"`
		Amount     int64  `orm:"column(amount)"`
		Passengers int64  `orm:"column(passengers)"`
	}

	_, err = o.Raw(sql).QueryRows(&trendResults)
	if err != nil {
		logs.Error("获取趋势统计失败: %v", err)
		return nil, err
	}

	// 构建 GoView 折线图数据格式
	trendChartSource := make([]interface{}, 0)
	var totalOrders, totalAmount int64

	for _, result := range trendResults {
		avgAmount := float64(0)
		if result.OrderCount > 0 {
			avgAmount = float64(result.Amount) / float64(result.OrderCount) / 100 // 转换为元
		}

		// 折线图数据
		trendChartSource = append(trendChartSource, map[string]interface{}{
			"date":       result.Date,
			"orderCount": result.OrderCount,
			"amount":     float64(result.Amount) / 100, // 转换为元
			"passengers": result.Passengers,
			"avgAmount":  avgAmount,
		})

		totalOrders += result.OrderCount
		totalAmount += result.Amount
	}

	// 计算汇总信息
	totalDays := len(trendResults)
	avgDailyOrders := float64(0)
	avgDailyAmount := float64(0)
	growthRate := float64(0)

	if totalDays > 0 {
		avgDailyOrders = float64(totalOrders) / float64(totalDays)
		avgDailyAmount = float64(totalAmount) / float64(totalDays) / 100 // 转换为元

		// 计算增长率（简单的首尾对比）
		if len(trendResults) >= 2 {
			firstPeriod := trendResults[0].OrderCount
			lastPeriod := trendResults[len(trendResults)-1].OrderCount
			if firstPeriod > 0 {
				growthRate = float64(lastPeriod-firstPeriod) / float64(firstPeriod) * 100
			}
		}
	}

	result := &dto.OrderTrendStats{
		TrendChart: dto.GoViewDataset{
			Dimensions: []string{"date", "orderCount", "amount", "passengers", "avgAmount"},
			Source:     trendChartSource,
		},
		TotalDays:      totalDays,
		TotalOrders:    totalOrders,
		TotalAmount:    totalAmount,
		AvgDailyOrders: avgDailyOrders,
		AvgDailyAmount: avgDailyAmount,
		GrowthRate:     growthRate,
	}

	return result, nil
}

// GetRouteStats 获取路线统计
func (s *OrderStatsService) GetRouteStats(query *dto.RouteStatsQuery, ctx *context.Context, userId int64) (*dto.RouteStats, error) {
	o := orm.NewOrmUsingDB("robobusWX")

	// 设置默认值
	if query.Limit <= 0 {
		query.Limit = 10
	}

	// 构建时间过滤条件
	timeCondition := ""
	if query.StartDate != "" && query.EndDate != "" {
		startTime, err := time.Parse("2006-01-02", query.StartDate)
		if err == nil {
			endTime, err := time.Parse("2006-01-02", query.EndDate)
			if err == nil {
				startTimestamp := startTime.Unix()
				endTimestamp := endTime.Unix() + 86400
				timeCondition = fmt.Sprintf(" AND o.created_time >= %d AND o.created_time < %d", startTimestamp, endTimestamp)
			}
		}
	}

	sql := `
		SELECT
			o.route_id,
			COALESCE(r.name, '未知路线') as route_name,
			o.begin,
			o.end,
			COUNT(*) as order_count,
			COALESCE(SUM(CASE WHEN total_fee IS NOT NULL THEN total_fee ELSE price * num END), 0) as amount,
			COALESCE(SUM(num), 0) as passengers
		FROM ` + "`order`" + ` o
		LEFT JOIN ` + "`route`" + ` r ON o.route_id = r.id
		WHERE 1=1
	` + timeCondition

	if userId != 1 {
		sql += fmt.Sprintf(" AND r.fms_user_id = %d", userId)
	}

	sql += fmt.Sprintf(" GROUP BY o.route_id, r.name, o.begin, o.end ORDER BY order_count DESC LIMIT %d", query.Limit)

	var routeResults []struct {
		RouteId    int    `orm:"column(route_id)"`
		RouteName  string `orm:"column(route_name)"`
		Begin      string `orm:"column(begin)"`
		End        string `orm:"column(end)"`
		OrderCount int64  `orm:"column(order_count)"`
		Amount     int64  `orm:"column(amount)"`
		Passengers int64  `orm:"column(passengers)"`
	}

	_, err := o.Raw(sql).QueryRows(&routeResults)
	if err != nil {
		logs.Error("获取路线统计失败: %v", err)
		return nil, err
	}

	// 获取总订单数用于计算占比
	totalSql := `
		SELECT COUNT(*) as total
		FROM ` + "`order`" + ` o
		LEFT JOIN ` + "`route`" + ` r ON o.route_id = r.id
		WHERE 1=1
	` + timeCondition

	if userId != 1 {
		totalSql += fmt.Sprintf(" AND r.fms_user_id = %d", userId)
	}

	var totalCount int64
	err = o.Raw(totalSql).QueryRow(&totalCount)
	if err != nil {
		logs.Error("获取总订单数失败: %v", err)
		totalCount = 1 // 避免除零错误
	}

	// 构建 GoView 柱状图和表格数据格式
	routeChartSource := make([]interface{}, 0)
	routeTableSource := make([]interface{}, 0)
	var topRouteOrders, topRouteAmount int64

	for i, result := range routeResults {
		percentage := float64(result.OrderCount) / float64(totalCount) * 100
		avgAmount := float64(0)
		if result.OrderCount > 0 {
			avgAmount = float64(result.Amount) / float64(result.OrderCount) / 100 // 转换为元
		}

		routeName := fmt.Sprintf("%s(%s→%s)", result.RouteName, result.Begin, result.End)

		// 柱状图数据
		routeChartSource = append(routeChartSource, map[string]interface{}{
			"routeName":  routeName,
			"orderCount": result.OrderCount,
			"amount":     float64(result.Amount) / 100, // 转换为元
		})

		// 表格数据
		routeTableSource = append(routeTableSource, map[string]interface{}{
			"routeName":  routeName,
			"orderCount": result.OrderCount,
			"amount":     float64(result.Amount) / 100, // 转换为元
			"passengers": result.Passengers,
			"avgAmount":  avgAmount,
			"percentage": fmt.Sprintf("%.1f%%", percentage),
		})

		if i == 0 {
			topRouteOrders = result.OrderCount
			topRouteAmount = result.Amount
		}
	}

	// 获取路线汇总信息
	summarySql := `
		SELECT
			COUNT(DISTINCT o.route_id) as total_routes,
			COUNT(DISTINCT CASE WHEN o.created_time >= UNIX_TIMESTAMP(DATE_SUB(NOW(), INTERVAL 30 DAY)) THEN o.route_id END) as active_routes
		FROM ` + "`order`" + ` o
		LEFT JOIN ` + "`route`" + ` r ON o.route_id = r.id
		WHERE 1=1
	`

	if userId != 1 {
		summarySql += fmt.Sprintf(" AND r.fms_user_id = %d", userId)
	}

	var summaryResult struct {
		TotalRoutes  int `orm:"column(total_routes)"`
		ActiveRoutes int `orm:"column(active_routes)"`
	}

	err = o.Raw(summarySql).QueryRow(&summaryResult)
	if err != nil {
		logs.Error("获取路线汇总失败: %v", err)
		summaryResult.TotalRoutes = len(routeResults)
		summaryResult.ActiveRoutes = len(routeResults)
	}

	result := &dto.RouteStats{
		RouteChart: dto.GoViewDataset{
			Dimensions: []string{"routeName", "orderCount", "amount"},
			Source:     routeChartSource,
		},
		RouteTable: dto.GoViewTableDataset{
			Dimensions: []dto.GoViewTableColumn{
				{Title: "路线", Key: "routeName"},
				{Title: "订单数", Key: "orderCount"},
				{Title: "金额(元)", Key: "amount"},
				{Title: "乘客数", Key: "passengers"},
				{Title: "平均金额(元)", Key: "avgAmount"},
				{Title: "占比", Key: "percentage"},
			},
			Source: routeTableSource,
		},
		TotalRoutes:    summaryResult.TotalRoutes,
		ActiveRoutes:   summaryResult.ActiveRoutes,
		TopRouteOrders: topRouteOrders,
		TopRouteAmount: topRouteAmount,
	}

	return result, nil
}

// GetRegionStats 获取地区统计
func (s *OrderStatsService) GetRegionStats(query *dto.RegionStatsQuery, ctx *context.Context, userId int64) (*dto.RegionStats, error) {
	o := orm.NewOrmUsingDB("robobusWX")

	// 设置默认统计类型
	if query.Type == "" {
		query.Type = "begin" // 默认统计起点
	}

	// 构建时间过滤条件
	timeCondition := ""
	if query.StartDate != "" && query.EndDate != "" {
		startTime, err := time.Parse("2006-01-02", query.StartDate)
		if err == nil {
			endTime, err := time.Parse("2006-01-02", query.EndDate)
			if err == nil {
				startTimestamp := startTime.Unix()
				endTimestamp := endTime.Unix() + 86400
				timeCondition = fmt.Sprintf(" AND o.created_time >= %d AND o.created_time < %d", startTimestamp, endTimestamp)
			}
		}
	}

	// 根据统计类型选择字段
	var regionField string
	switch query.Type {
	case "end":
		regionField = "o.end"
	case "route":
		regionField = "CONCAT(o.begin, ' → ', o.end)"
	default: // begin
		regionField = "o.begin"
	}

	sql := fmt.Sprintf(`
		SELECT
			%s as region,
			COUNT(*) as order_count,
			COALESCE(SUM(CASE WHEN total_fee IS NOT NULL THEN total_fee ELSE price * num END), 0) as amount,
			COALESCE(SUM(num), 0) as passengers
		FROM `+"`order`"+` o
		LEFT JOIN `+"`route`"+` r ON o.route_id = r.id
		WHERE 1=1
	`, regionField) + timeCondition

	if userId != 1 {
		sql += fmt.Sprintf(" AND r.fms_user_id = %d", userId)
	}

	sql += fmt.Sprintf(" GROUP BY %s ORDER BY order_count DESC LIMIT 20", regionField)

	var regionResults []struct {
		Region     string `orm:"column(region)"`
		OrderCount int64  `orm:"column(order_count)"`
		Amount     int64  `orm:"column(amount)"`
		Passengers int64  `orm:"column(passengers)"`
	}

	_, err := o.Raw(sql).QueryRows(&regionResults)
	if err != nil {
		logs.Error("获取地区统计失败: %v", err)
		return nil, err
	}

	// 获取总订单数用于计算占比
	totalSql := `
		SELECT COUNT(*) as total
		FROM ` + "`order`" + ` o
		LEFT JOIN ` + "`route`" + ` r ON o.route_id = r.id
		WHERE 1=1
	` + timeCondition

	if userId != 1 {
		totalSql += fmt.Sprintf(" AND r.fms_user_id = %d", userId)
	}

	var totalCount int64
	err = o.Raw(totalSql).QueryRow(&totalCount)
	if err != nil {
		logs.Error("获取总订单数失败: %v", err)
		totalCount = 1
	}

	// 构建 GoView 饼图和表格数据格式
	regionChartSource := make([]interface{}, 0)
	regionTableSource := make([]interface{}, 0)
	var topRegion string
	var topRegionOrders int64

	for i, result := range regionResults {
		percentage := float64(result.OrderCount) / float64(totalCount) * 100

		// 饼图数据
		regionChartSource = append(regionChartSource, map[string]interface{}{
			"region": result.Region,
			"count":  result.OrderCount,
		})

		// 表格数据
		regionTableSource = append(regionTableSource, map[string]interface{}{
			"region":     result.Region,
			"orderCount": result.OrderCount,
			"amount":     float64(result.Amount) / 100, // 转换为元
			"passengers": result.Passengers,
			"percentage": fmt.Sprintf("%.1f%%", percentage),
		})

		if i == 0 {
			topRegion = result.Region
			topRegionOrders = result.OrderCount
		}
	}

	result := &dto.RegionStats{
		RegionChart: dto.GoViewDataset{
			Dimensions: []string{"region", "count"},
			Source:     regionChartSource,
		},
		RegionTable: dto.GoViewTableDataset{
			Dimensions: []dto.GoViewTableColumn{
				{Title: "地区", Key: "region"},
				{Title: "订单数", Key: "orderCount"},
				{Title: "金额(元)", Key: "amount"},
				{Title: "乘客数", Key: "passengers"},
				{Title: "占比", Key: "percentage"},
			},
			Source: regionTableSource,
		},
		TotalRegions:    len(regionResults),
		TopRegion:       topRegion,
		TopRegionOrders: topRegionOrders,
	}

	return result, nil
}

// GetVehicleStats 获取车辆统计
func (s *OrderStatsService) GetVehicleStats(query *dto.VehicleStatsQuery, ctx *context.Context, userId int64) (*dto.VehicleStats, error) {
	o := orm.NewOrmUsingDB("robobusWX")

	// 设置默认值
	if query.Limit <= 0 {
		query.Limit = 10
	}

	// 构建时间过滤条件
	timeCondition := ""
	if query.StartDate != "" && query.EndDate != "" {
		startTime, err := time.Parse("2006-01-02", query.StartDate)
		if err == nil {
			endTime, err := time.Parse("2006-01-02", query.EndDate)
			if err == nil {
				startTimestamp := startTime.Unix()
				endTimestamp := endTime.Unix() + 86400
				timeCondition = fmt.Sprintf(" AND o.created_time >= %d AND o.created_time < %d", startTimestamp, endTimestamp)
			}
		}
	}

	sql := `
		SELECT
			o.vehicle_id,
			COALESCE(d.name, CONCAT('车辆-', o.vehicle_id)) as vehicle_name,
			COALESCE(d.vin_code, '') as vin_code,
			COUNT(*) as order_count,
			COALESCE(SUM(CASE WHEN total_fee IS NOT NULL THEN total_fee ELSE price * num END), 0) as amount,
			COALESCE(SUM(num), 0) as passengers
		FROM ` + "`order`" + ` o
		LEFT JOIN ` + "`route`" + ` r ON o.route_id = r.id
		LEFT JOIN ` + "`device`" + ` d ON o.vehicle_id = d.id
		WHERE o.vehicle_id IS NOT NULL AND o.vehicle_id > 0
	` + timeCondition

	if userId != 1 {
		sql += fmt.Sprintf(" AND r.fms_user_id = %d", userId)
	}

	sql += fmt.Sprintf(" GROUP BY o.vehicle_id, d.name, d.vin_code ORDER BY order_count DESC LIMIT %d", query.Limit)

	var vehicleResults []struct {
		VehicleId   int    `orm:"column(vehicle_id)"`
		VehicleName string `orm:"column(vehicle_name)"`
		VinCode     string `orm:"column(vin_code)"`
		OrderCount  int64  `orm:"column(order_count)"`
		Amount      int64  `orm:"column(amount)"`
		Passengers  int64  `orm:"column(passengers)"`
	}

	_, err := o.Raw(sql).QueryRows(&vehicleResults)
	if err != nil {
		logs.Error("获取车辆统计失败: %v", err)
		return nil, err
	}

	// 构建 GoView 柱状图和表格数据格式
	vehicleChartSource := make([]interface{}, 0)
	vehicleTableSource := make([]interface{}, 0)
	var topVehicleOrders int64

	for i, result := range vehicleResults {
		// 简单的使用率计算（基于订单数）
		utilization := float64(result.OrderCount) / 100 // 简化计算，实际应该基于时间和容量

		// 柱状图数据
		vehicleChartSource = append(vehicleChartSource, map[string]interface{}{
			"vehicleName": result.VehicleName,
			"orderCount":  result.OrderCount,
			"utilization": utilization,
		})

		// 表格数据
		vehicleTableSource = append(vehicleTableSource, map[string]interface{}{
			"vehicleName": result.VehicleName,
			"vinCode":     result.VinCode,
			"orderCount":  result.OrderCount,
			"amount":      float64(result.Amount) / 100, // 转换为元
			"passengers":  result.Passengers,
			"utilization": fmt.Sprintf("%.1f%%", utilization),
		})

		if i == 0 {
			topVehicleOrders = result.OrderCount
		}
	}

	// 获取车辆汇总信息
	summarySql := `
		SELECT
			COUNT(DISTINCT o.vehicle_id) as total_vehicles,
			COUNT(DISTINCT CASE WHEN o.created_time >= UNIX_TIMESTAMP(DATE_SUB(NOW(), INTERVAL 30 DAY)) THEN o.vehicle_id END) as active_vehicles
		FROM ` + "`order`" + ` o
		LEFT JOIN ` + "`route`" + ` r ON o.route_id = r.id
		WHERE o.vehicle_id IS NOT NULL AND o.vehicle_id > 0
	`

	if userId != 1 {
		summarySql += fmt.Sprintf(" AND r.fms_user_id = %d", userId)
	}

	var summaryResult struct {
		TotalVehicles  int `orm:"column(total_vehicles)"`
		ActiveVehicles int `orm:"column(active_vehicles)"`
	}

	err = o.Raw(summarySql).QueryRow(&summaryResult)
	if err != nil {
		logs.Error("获取车辆汇总失败: %v", err)
		summaryResult.TotalVehicles = len(vehicleResults)
		summaryResult.ActiveVehicles = len(vehicleResults)
	}

	avgUtilization := float64(0)
	if len(vehicleResults) > 0 {
		totalUtilization := float64(0)
		for _, result := range vehicleResults {
			totalUtilization += float64(result.OrderCount) / 100
		}
		avgUtilization = totalUtilization / float64(len(vehicleResults))
	}

	result := &dto.VehicleStats{
		VehicleChart: dto.GoViewDataset{
			Dimensions: []string{"vehicleName", "orderCount", "utilization"},
			Source:     vehicleChartSource,
		},
		VehicleTable: dto.GoViewTableDataset{
			Dimensions: []dto.GoViewTableColumn{
				{Title: "车辆", Key: "vehicleName"},
				{Title: "VIN码", Key: "vinCode"},
				{Title: "订单数", Key: "orderCount"},
				{Title: "金额(元)", Key: "amount"},
				{Title: "乘客数", Key: "passengers"},
				{Title: "使用率", Key: "utilization"},
			},
			Source: vehicleTableSource,
		},
		TotalVehicles:    summaryResult.TotalVehicles,
		ActiveVehicles:   summaryResult.ActiveVehicles,
		AvgUtilization:   avgUtilization,
		TopVehicleOrders: topVehicleOrders,
	}

	return result, nil
}

// GetUserStats 获取用户统计
func (s *OrderStatsService) GetUserStats(query *dto.UserStatsQuery, ctx *context.Context, userId int64) (*dto.UserStats, error) {
	o := orm.NewOrmUsingDB("robobusWX")

	// 构建时间过滤条件
	timeCondition := ""
	if query.StartDate != "" && query.EndDate != "" {
		startTime, err := time.Parse("2006-01-02", query.StartDate)
		if err == nil {
			endTime, err := time.Parse("2006-01-02", query.EndDate)
			if err == nil {
				startTimestamp := startTime.Unix()
				endTimestamp := endTime.Unix() + 86400
				timeCondition = fmt.Sprintf(" AND o.created_time >= %d AND o.created_time < %d", startTimestamp, endTimestamp)
			}
		}
	}

	// 用户概览统计
	userOverviewSql := `
		SELECT
			COUNT(DISTINCT u.id) as total_users,
			COUNT(DISTINCT CASE WHEN u.created_time >= UNIX_TIMESTAMP(DATE_SUB(NOW(), INTERVAL 1 DAY)) THEN u.id END) as new_users,
			COUNT(DISTINCT CASE WHEN o.created_time >= UNIX_TIMESTAMP(DATE_SUB(NOW(), INTERVAL 1 DAY)) THEN o.user_id END) as daily_active_users,
			COUNT(DISTINCT CASE WHEN o.created_time >= UNIX_TIMESTAMP(DATE_SUB(NOW(), INTERVAL 7 DAY)) THEN o.user_id END) as weekly_active_users,
			COUNT(DISTINCT CASE WHEN o.created_time >= UNIX_TIMESTAMP(DATE_SUB(NOW(), INTERVAL 30 DAY)) THEN o.user_id END) as monthly_active_users,
			COUNT(DISTINCT o.user_id) as ordering_users
		FROM wx_user u
		LEFT JOIN ` + "`order`" + ` o ON u.id = o.user_id
		LEFT JOIN ` + "`route`" + ` r ON o.route_id = r.id
		WHERE 1=1
	`

	if userId != 1 {
		userOverviewSql += fmt.Sprintf(" AND r.fms_user_id = %d", userId)
	}

	var userOverview struct {
		TotalUsers         int64 `orm:"column(total_users)"`
		NewUsers           int64 `orm:"column(new_users)"`
		DailyActiveUsers   int64 `orm:"column(daily_active_users)"`
		WeeklyActiveUsers  int64 `orm:"column(weekly_active_users)"`
		MonthlyActiveUsers int64 `orm:"column(monthly_active_users)"`
		OrderingUsers      int64 `orm:"column(ordering_users)"`
	}

	err := o.Raw(userOverviewSql).QueryRow(&userOverview)
	if err != nil {
		logs.Error("获取用户概览统计失败: %v", err)
		return nil, err
	}

	// 国家分布统计
	countrySql := `
		SELECT
			COALESCE(u.country, '未知') as country,
			COUNT(DISTINCT u.id) as user_count,
			COUNT(o.id) as order_count,
			COALESCE(SUM(CASE WHEN total_fee IS NOT NULL THEN total_fee ELSE price * num END), 0) as amount
		FROM wx_user u
		LEFT JOIN ` + "`order`" + ` o ON u.id = o.user_id
		LEFT JOIN ` + "`route`" + ` r ON o.route_id = r.id
		WHERE 1=1
	` + timeCondition

	if query.Country != "" {
		countrySql += fmt.Sprintf(" AND u.country = '%s'", strings.ReplaceAll(query.Country, "'", "''"))
	}

	if userId != 1 {
		countrySql += fmt.Sprintf(" AND r.fms_user_id = %d", userId)
	}

	countrySql += " GROUP BY u.country ORDER BY user_count DESC LIMIT 10"

	var countryResults []struct {
		Country    string `orm:"column(country)"`
		UserCount  int64  `orm:"column(user_count)"`
		OrderCount int64  `orm:"column(order_count)"`
		Amount     int64  `orm:"column(amount)"`
	}

	_, err = o.Raw(countrySql).QueryRows(&countryResults)
	if err != nil {
		logs.Error("获取国家分布统计失败: %v", err)
		return nil, err
	}

	// 构建 GoView 饼图和表格数据格式
	countryChartSource := make([]interface{}, 0)
	countryTableSource := make([]interface{}, 0)

	for _, result := range countryResults {
		percentage := float64(0)
		if userOverview.TotalUsers > 0 {
			percentage = float64(result.UserCount) / float64(userOverview.TotalUsers) * 100
		}

		// 饼图数据
		countryChartSource = append(countryChartSource, map[string]interface{}{
			"country": result.Country,
			"count":   result.UserCount,
		})

		// 表格数据
		countryTableSource = append(countryTableSource, map[string]interface{}{
			"country":    result.Country,
			"userCount":  result.UserCount,
			"orderCount": result.OrderCount,
			"amount":     float64(result.Amount) / 100, // 转换为元
			"percentage": fmt.Sprintf("%.1f%%", percentage),
		})
	}

	// 计算留存率（简化计算）
	retentionRate := float64(0)
	if userOverview.TotalUsers > 0 {
		retentionRate = float64(userOverview.MonthlyActiveUsers) / float64(userOverview.TotalUsers) * 100
	}

	result := &dto.UserStats{
		TotalUsers:         userOverview.TotalUsers,
		NewUsers:           userOverview.NewUsers,
		ActiveUsers:        userOverview.MonthlyActiveUsers, // 使用月活作为活跃用户
		OrderingUsers:      userOverview.OrderingUsers,
		DailyActiveUsers:   userOverview.DailyActiveUsers,
		WeeklyActiveUsers:  userOverview.WeeklyActiveUsers,
		MonthlyActiveUsers: userOverview.MonthlyActiveUsers,
		RetentionRate:      retentionRate,
		CountryChart: dto.GoViewDataset{
			Dimensions: []string{"country", "count"},
			Source:     countryChartSource,
		},
		CountryTable: dto.GoViewTableDataset{
			Dimensions: []dto.GoViewTableColumn{
				{Title: "国家", Key: "country"},
				{Title: "用户数", Key: "userCount"},
				{Title: "订单数", Key: "orderCount"},
				{Title: "金额(元)", Key: "amount"},
				{Title: "占比", Key: "percentage"},
			},
			Source: countryTableSource,
		},
	}

	return result, nil
}

// GetRealtimeStats 获取实时统计
func (s *OrderStatsService) GetRealtimeStats(query *dto.RealtimeStatsQuery, ctx *context.Context, userId int64) (*dto.RealtimeStats, error) {
	o := orm.NewOrmUsingDB("robobusWX")

	// 设置默认值
	if query.Hours <= 0 {
		query.Hours = 24
	}

	now := time.Now()
	startTime := now.Add(-time.Duration(query.Hours) * time.Hour)
	startTimestamp := startTime.Unix()

	// 当前小时统计
	currentHourStart := time.Date(now.Year(), now.Month(), now.Day(), now.Hour(), 0, 0, 0, now.Location()).Unix()
	currentHourEnd := currentHourStart + 3600

	currentHourSql := `
		SELECT
			COUNT(*) as order_count,
			COALESCE(SUM(CASE WHEN total_fee IS NOT NULL THEN total_fee ELSE price * num END), 0) as amount
		FROM ` + "`order`" + ` o
		LEFT JOIN ` + "`route`" + ` r ON o.route_id = r.id
		WHERE o.created_time >= %d AND o.created_time < %d
	`

	if userId != 1 {
		currentHourSql += fmt.Sprintf(" AND r.fms_user_id = %d", userId)
	}

	var currentHourStats struct {
		OrderCount int64 `orm:"column(order_count)"`
		Amount     int64 `orm:"column(amount)"`
	}

	err := o.Raw(fmt.Sprintf(currentHourSql, currentHourStart, currentHourEnd)).QueryRow(&currentHourStats)
	if err != nil {
		logs.Error("获取当前小时统计失败: %v", err)
		return nil, err
	}

	// 最近N小时趋势
	hourlySql := `
		SELECT
			DATE_FORMAT(FROM_UNIXTIME(o.created_time), '%%H:00') as hour,
			COUNT(*) as order_count,
			COALESCE(SUM(CASE WHEN total_fee IS NOT NULL THEN total_fee ELSE price * num END), 0) as amount,
			COALESCE(SUM(num), 0) as passengers
		FROM ` + "`order`" + ` o
		LEFT JOIN ` + "`route`" + ` r ON o.route_id = r.id
		WHERE o.created_time >= %d
	`

	if userId != 1 {
		hourlySql += fmt.Sprintf(" AND r.fms_user_id = %d", userId)
	}

	hourlySql += " GROUP BY DATE_FORMAT(FROM_UNIXTIME(o.created_time), '%H:00') ORDER BY hour"

	var hourlyResults []struct {
		Hour       string `orm:"column(hour)"`
		OrderCount int64  `orm:"column(order_count)"`
		Amount     int64  `orm:"column(amount)"`
		Passengers int64  `orm:"column(passengers)"`
	}

	_, err = o.Raw(fmt.Sprintf(hourlySql, startTimestamp)).QueryRows(&hourlyResults)
	if err != nil {
		logs.Error("获取小时趋势统计失败: %v", err)
		return nil, err
	}

	// 构建 GoView 折线图数据格式
	hourlyChartSource := make([]interface{}, 0)
	for _, result := range hourlyResults {
		hourlyChartSource = append(hourlyChartSource, map[string]interface{}{
			"hour":       result.Hour,
			"orderCount": result.OrderCount,
			"amount":     float64(result.Amount) / 100, // 转换为元
			"passengers": result.Passengers,
		})
	}

	// 实时订单表格（最近的订单）
	realtimeTableSql := `
		SELECT
			o.order_no,
			o.begin,
			o.end,
			o.status,
			CASE WHEN total_fee IS NOT NULL THEN total_fee ELSE price * num END as amount,
			o.num as passengers,
			FROM_UNIXTIME(o.created_time) as created_time
		FROM ` + "`order`" + ` o
		LEFT JOIN ` + "`route`" + ` r ON o.route_id = r.id
		WHERE o.created_time >= %d
	`

	if userId != 1 {
		realtimeTableSql += fmt.Sprintf(" AND r.fms_user_id = %d", userId)
	}

	realtimeTableSql += " ORDER BY o.created_time DESC LIMIT 20"

	var realtimeResults []struct {
		OrderNo     string `orm:"column(order_no)"`
		Begin       string `orm:"column(begin)"`
		End         string `orm:"column(end)"`
		Status      int    `orm:"column(status)"`
		Amount      int64  `orm:"column(amount)"`
		Passengers  int    `orm:"column(passengers)"`
		CreatedTime string `orm:"column(created_time)"`
	}

	_, err = o.Raw(fmt.Sprintf(realtimeTableSql, startTimestamp)).QueryRows(&realtimeResults)
	if err != nil {
		logs.Error("获取实时订单失败: %v", err)
		return nil, err
	}

	statusMap := map[int]string{
		0: "待确认",
		1: "已确认",
		2: "已取消",
		3: "已完成",
		4: "已退款",
	}

	realtimeTableSource := make([]interface{}, 0)
	for _, result := range realtimeResults {
		statusName := statusMap[result.Status]
		if statusName == "" {
			statusName = "未知状态"
		}

		realtimeTableSource = append(realtimeTableSource, map[string]interface{}{
			"orderNo":     result.OrderNo,
			"route":       fmt.Sprintf("%s → %s", result.Begin, result.End),
			"status":      statusName,
			"amount":      float64(result.Amount) / 100, // 转换为元
			"passengers":  result.Passengers,
			"createdTime": result.CreatedTime,
		})
	}

	// 获取在线订单和待处理订单数
	onlineOrdersSql := `
		SELECT
			COUNT(CASE WHEN status IN (0, 1) THEN 1 END) as online_orders,
			COUNT(CASE WHEN status = 0 THEN 1 END) as pending_orders
		FROM ` + "`order`" + ` o
		LEFT JOIN ` + "`route`" + ` r ON o.route_id = r.id
		WHERE 1=1
	`

	if userId != 1 {
		onlineOrdersSql += fmt.Sprintf(" AND r.fms_user_id = %d", userId)
	}

	var onlineStats struct {
		OnlineOrders  int64 `orm:"column(online_orders)"`
		PendingOrders int64 `orm:"column(pending_orders)"`
	}

	err = o.Raw(onlineOrdersSql).QueryRow(&onlineStats)
	if err != nil {
		logs.Error("获取在线订单统计失败: %v", err)
		onlineStats.OnlineOrders = 0
		onlineStats.PendingOrders = 0
	}

	// 计算趋势方向（简单的当前小时与前一小时对比）
	trendDirection := "stable"
	if len(hourlyResults) >= 2 {
		current := hourlyResults[len(hourlyResults)-1].OrderCount
		previous := hourlyResults[len(hourlyResults)-2].OrderCount
		if current > previous {
			trendDirection = "up"
		} else if current < previous {
			trendDirection = "down"
		}
	}

	result := &dto.RealtimeStats{
		HourlyChart: dto.GoViewDataset{
			Dimensions: []string{"hour", "orderCount", "amount", "passengers"},
			Source:     hourlyChartSource,
		},
		RealtimeTable: dto.GoViewTableDataset{
			Dimensions: []dto.GoViewTableColumn{
				{Title: "订单号", Key: "orderNo"},
				{Title: "路线", Key: "route"},
				{Title: "状态", Key: "status"},
				{Title: "金额(元)", Key: "amount"},
				{Title: "乘客数", Key: "passengers"},
				{Title: "创建时间", Key: "createdTime"},
			},
			Source: realtimeTableSource,
		},
		CurrentHourOrders: currentHourStats.OrderCount,
		CurrentHourAmount: currentHourStats.Amount,
		OnlineOrders:      onlineStats.OnlineOrders,
		PendingOrders:     onlineStats.PendingOrders,
		LastUpdateTime:    time.Now(),
		TrendDirection:    trendDirection,
	}

	return result, nil
}

// GetStatusRadarStats 获取订单状态雷达图统计
func (s *OrderStatsService) GetStatusRadarStats(query *dto.OrderStatusRadarQuery, ctx *context.Context, userId int64) (*dto.OrderStatusRadarStats, error) {
	o := orm.NewOrmUsingDB("robobusWX")

	// 设置默认时间范围（当前月）
	var startTime, endTime time.Time
	if query.StartDate != "" && query.EndDate != "" {
		var err error
		startTime, err = time.Parse("2006-01-02", query.StartDate)
		if err != nil {
			return nil, fmt.Errorf("开始日期格式错误: %v", err)
		}
		endTime, err = time.Parse("2006-01-02", query.EndDate)
		if err != nil {
			return nil, fmt.Errorf("结束日期格式错误: %v", err)
		}
	} else {
		// 默认查询当前月
		now := time.Now()
		startTime = time.Date(now.Year(), now.Month(), 1, 0, 0, 0, 0, now.Location())
		endTime = startTime.AddDate(0, 1, 0).Add(-time.Second)
	}

	startTimestamp := startTime.Unix()
	endTimestamp := endTime.Unix()

	// 查询各状态订单统计
	statusSql := `
		SELECT
			o.status,
			COUNT(*) as count
		FROM ` + "`order`" + ` o
		LEFT JOIN ` + "`route`" + ` r ON o.route_id = r.id
		WHERE o.created_time >= ? AND o.created_time <= ?
	`

	if userId != 1 {
		statusSql += fmt.Sprintf(" AND r.fms_user_id = %d", userId)
	}
	statusSql += " GROUP BY o.status ORDER BY o.status"

	var statusResults []struct {
		Status int   `orm:"column(status)"`
		Count  int64 `orm:"column(count)"`
	}

	_, err := o.Raw(statusSql, startTimestamp, endTimestamp).QueryRows(&statusResults)
	if err != nil {
		logs.Error("获取订单状态统计失败: %v", err)
		return nil, err
	}

	// 状态映射和默认值（注释掉未使用的变量）
	// statusMap := map[int]string{
	// 	0: "待确认订单",
	// 	1: "已确认订单",
	// 	2: "已取消订单",
	// 	3: "已完成订单",
	// 	4: "已退款订单",
	// 	5: "进行中订单", // 扩展状态，如果需要的话
	// }

	// 初始化所有状态的计数
	statusCounts := map[int]int64{
		0: 0, // 待确认订单
		1: 0, // 已确认订单
		2: 0, // 已取消订单
		3: 0, // 已完成订单
		4: 0, // 已退款订单
		5: 0, // 进行中订单
	}

	// 填充实际数据
	var totalOrders int64
	for _, result := range statusResults {
		if _, exists := statusCounts[result.Status]; exists {
			statusCounts[result.Status] = result.Count
			totalOrders += result.Count
		}
	}

	// 构建雷达图数据
	radarIndicator := []dto.RadarIndicator{
		{Name: "待确认订单", Max: getMaxValue(statusCounts[0], 200)},
		{Name: "已确认订单", Max: getMaxValue(statusCounts[1], 1000)},
		{Name: "进行中订单", Max: getMaxValue(statusCounts[5], 300)},
		{Name: "已完成订单", Max: getMaxValue(statusCounts[3], 800)},
		{Name: "已取消订单", Max: getMaxValue(statusCounts[2], 150)},
		{Name: "已退款订单", Max: getMaxValue(statusCounts[4], 100)},
	}

	seriesData := []dto.RadarSeriesData{
		{
			Name: "订单状态分布",
			Value: []int64{
				statusCounts[0], // 待确认订单
				statusCounts[1], // 已确认订单
				statusCounts[5], // 进行中订单
				statusCounts[3], // 已完成订单
				statusCounts[2], // 已取消订单
				statusCounts[4], // 已退款订单
			},
		},
	}

	// 计算各种比率
	var completionRate, cancellationRate, refundRate float64
	if totalOrders > 0 {
		completionRate = float64(statusCounts[3]) / float64(totalOrders) * 100
		cancellationRate = float64(statusCounts[2]) / float64(totalOrders) * 100
		refundRate = float64(statusCounts[4]) / float64(totalOrders) * 100
	}

	result := &dto.OrderStatusRadarStats{
		RadarIndicator:   radarIndicator,
		SeriesData:       seriesData,
		TotalOrders:      totalOrders,
		CompletionRate:   completionRate,
		CancellationRate: cancellationRate,
		RefundRate:       refundRate,
		PendingOrders:    statusCounts[0],
		ProcessingOrders: statusCounts[5],
	}

	return result, nil
}

// getMaxValue 获取雷达图最大值，确保实际值不超过最大值
func getMaxValue(actualValue int64, defaultMax int64) int64 {
	if actualValue > defaultMax {
		// 如果实际值超过默认最大值，则设置为实际值的1.2倍（向上取整到百位）
		maxValue := int64(float64(actualValue) * 1.2)
		return ((maxValue + 99) / 100) * 100 // 向上取整到百位
	}
	return defaultMax
}
